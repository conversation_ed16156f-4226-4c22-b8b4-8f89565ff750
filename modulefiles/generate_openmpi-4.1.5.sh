#! /bin/bash

#define parameters which are passed in.
HOME=$1
VERSION=$2

#define the template.
cat  << EOF
#%Module 1.0
#
#  OpenMPI module for use with 'environment-modules' package:

# On load print component name and version being loaded
if { [ module-info mode load ] } {
    puts stderr "Loading OpenMPI version $VERSION "
}

# On remove print component name and version being removed
if { [ module-info mode ] == "unload" || [ module-info mode ] == "remove" }  {
    puts stderr "Removing OpenMPI version $VERSION "
}

conflict     mpi
set mpi_root $HOME

prepend-path       PATH        \$mpi_root/bin
prepend-path      LD_LIBRARY_PATH \$mpi_root/lib
prepend-path      MANPATH     \$mpi_root/share/man
setenv          MPI_BIN     \$mpi_root/bin
setenv            MPI_SYSCONFIG   \$mpi_root/etc
setenv            MPI_INCLUDE \$mpi_root/include
setenv            MPI_LIB     \$mpi_root/lib
setenv            MPI_MAN     \$mpi_root/share/man
setenv          MPI_SUFFIX  _openmpi
setenv            MPI_HOME    \$mpi_root
EOF