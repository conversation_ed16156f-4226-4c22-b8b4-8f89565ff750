#! /bin/bash

#define parameters which are passed in.
HOME=$1
VERSION=$2

#define the template.
cat  << EOF
#%Module######################################################################
###
###      cuda software modulefile
###


proc ModulesHelp { } {
        puts stderr "\t cuda/$VERSION"
        puts stderr "\t usage: \$ nvcc -h"
}

module-whatis "For more information, \$ module help cuda/$VERSION"

conflict cuda

set CUDA_PATH $HOME
setenv CUDA_HOME \${CUDA_PATH}

prepend-path CUDA_HOME \${CUDA_PATH}
prepend-path CUDA_PATH \${CUDA_PATH}
prepend-path LD_LIBRARY_PATH \${CUDA_PATH}/lib64
prepend-path LIBRARY_PATH \${CUDA_PATH}/lib64
prepend-path LD_LIBRARY_PATH \${CUDA_PATH}/libnsight
prepend-path LIBRARY_PATH \${CUDA_PATH}/libnsight
prepend-path LD_LIBRARY_PATH \${CUDA_PATH}/libnvvp
prepend-path LIBRARY_PATH \${CUDA_PATH}/libnvvp
prepend-path CPATH \${CUDA_PATH}/include
prepend-path C_INCLUDE_PATH \${CUDA_PATH}/include
prepend-path PATH \${CUDA_PATH}/bin
prepend-path CUDA_ROOT \${CUDA_PATH}
EOF