
###
 # @Author: sunkaixuan01 <EMAIL>
 # @Date: 2023-01-18 14:05:24
 # @LastEditors: sunkaixuan01 <EMAIL>
 # @LastEditTime: 2023-02-02 15:12:28
 # @FilePath: \chpc_image_builder\test\test_cluster.sh
 # @Description: 集群功能一键测试脚本,使用方法
#! /bin/bash
set -x

CLUSTER_CONTROL_FILE="/opt/chpc/cluster_control.sh"
CLUSTER_CONTROL_BIN_FILE="/opt/chpc/cluster_control"
SLURM="slurm"
SGE="sge"
TEST_QUEUE_NAME="queueTest"
DEFAULT_PASSWD="baidu@123"
# 解析shell参数
function parse_args() {
    args=`getopt -o s:p:n: -l computeHost:,masterHost:,computeHostIP: -- "$@"`
    if [ $? != 0 ] ; then 
        echo "Parse args error! Terminating..." >&2 ; 
        exit 1 ; 
    fi
    echo $args
    eval set -- "$args"
    while true ; do
        case "$1" in
            -s) scheduler_type="$2"; shift 2 ;;
            -n) node_type="$2"; shift 2 ;;
            -p) queue_name="$2"; shift 2 ;;
            --computeHost) compute_host="$2"; shift 2;;
            --masterHost) master_host="$2"; shift 2;;
            --computeHostIP) compute_host_ip="$2"; shift 2;;
            -h) help; exit 0 ;;    
            --) shift ; break ;;
            *) echo "Parameters error!!!$1" ; exit 1 ;;        
        esac
    done
    exec_func=$1
}

function main() {
    if [ x"$exec_func" == x"testSubmitJob" ] 
    then
        test_submit_job $scheduler_type $compute_host
        ret_code=$?
        if [ $ret_code != 0 ]
        then
            echo "submit job failed"
            return 1
        fi
    elif [ x"$exec_func" == x"testAllCase" ]
    then
        test_all_case 
        ret_code=$?
        if [ $ret_code != 0 ]
        then
            echo "test all case failed"
            return 1
        fi
    elif [ x"$exec_func" == x"testStartCluster" ]
    then 
        test_start_cluster $scheduler_type $master_host $compute_host $compute_host_ip $node_type
        ret_code=$?
        if [ $ret_code != 0 ]
        then
            echo "test start cluster failed"
            return 1
        fi
    elif [ x"$exec_func" == x"testLdap" ]
    then
        test_ladp_login $compute_host
        ret_code=$?
        if [ $ret_code != 0 ]
        then
            echo "test ldap failed"
            return 1
        fi
    fi       
}
# 启动集群
function test_start_cluster() {
    scheduler_type=$1
    master_host=$2
    compute_host=$3
    compute_host_ip=$4
    node_type=$5
    $CLUSTER_CONTROL_BIN_FILE -m createCluster -c "{\"enable_ha\":false,\"scheduler_type\":\"$scheduler_type\",\"node_type\":\"$node_type\",\"cluster_name\":\"clusterTest\",\"master_hostname\":\"$master_host\",\"backup_hostnames\":[],\"login_hostname\":\"\",\"sharedstorage_list\":[],\"queue_list\":[{\"is_default\":true,\"name\":\"queue1\",\"node\":[{\"ip\":\"$compute_host_ip\",\"spec\":\"bcc.c4.c2m8\",\"id\":\"i-xxx\",\"hostname\":\"$compute_host\"}]}]}"
    ret_code=$?
    if [ $ret_code != 0 ]
    then
        echo "start cluster failed"
        return 1
    fi
}
# 测试任务提交
function test_submit_job() {
    scheduler_type=$1
    compute_host=$2
    if [ x"$scheduler_type" == x"$SLURM" ]
    then
        result=`/opt/slurm/22.05.9/bin/srun -N 1 hostname`
        ret_code=$?
        if [ $ret_code != 0 ]
        then
            echo "srun ret code not zero"
            return 1
        fi
        if [ x"$result" != x"$compute_host" ]
        then
            echo "srun fail"
            return 1
        fi
        mkdir -p /home/<USER>
        chown -R work:work /home/<USER>
        cd /home/<USER>
        echo "#! /bin/bash">test.sh
        echo "hostname">>test.sh
        job_id=`/opt/slurm/22.05.9/bin/sbatch test.sh|awk '{print $4}'`
        sleep 10s
        out_file="slurm-$job_id.out"
        scp $compute_host:/home/<USER>/$out_file /home/<USER>
        if [ ! -f $out_file ]
        then
            echo "sbatch out file not exit"
            return 1
        fi
        result=`cat $out_file`
        if [ x"$result" != x"$compute_host" ]
        then
            echo "sbatch fail"
            return 1
        fi
    elif [ x"$scheduler_type" == x"$SGE" ]
    then
        mkdir -p /home/<USER>
        chown -R work:work /home/<USER>
        cd /home/<USER>
        echo "#! /bin/bash">test.sh
        echo "hostname">>test.sh
        job_id=`su work -c "qsub test.sh"|awk '{print $3}'`
        sleep 10s
        out_file="test.sh.o$job_id"
        scp $compute_host:/home/<USER>/$out_file /home/<USER>
        if [ ! -f $out_file ]
        then
            echo "qsub out file not exit"
            return 1
        fi
        result=`cat $out_file`
        if [ x"$result" != x"$compute_host" ]
        then
            echo "qsub job failed"
            return 1
        fi
    else
        echo "invalid schduler"
        return 1
    fi

}
# 创建新队列
function test_add_queue() {
    scheduler_type=$1
    queue_name=$2
    if [ x"$scheduler_type" == x"$SLURM" ]
    then
        bash $CLUSTER_CONTROL_FILE createSlurmQueues -p $queue_name
        ret_code=$?
        if [ $ret_code != 0 ]
        then
            echo "add slurm queue failed"
            return 1
        fi
    elif [ x"$scheduler_type" == x"$SGE" ]
    then
        bash $CLUSTER_CONTROL_FILE createSgeQueues -p $queue_name
        ret_code=$?
        if [ $ret_code != 0 ]
        then
            echo "add sge queue failed"
            return 1
        fi
    else
        echo "invalid schduler"
        return 1
    fi
}
# 删除队列
function test_remove_queue() {
    scheduler_type=$1
    queue_name=$2
    if [ x"$scheduler_type" == x"$SLURM" ]
    then
        bash $CLUSTER_CONTROL_FILE deleteSlurmQueues -p $queue_name
        ret_code=$?
        if [ $ret_code != 0 ]
        then
            echo "add slurm queue failed"
            return 1
        fi
    elif [ x"$scheduler_type" == x"$SGE" ]
    then
        bash $CLUSTER_CONTROL_FILE deleteSgeQueues -p $queue_name
        ret_code=$?
        if [ $ret_code != 0 ]
        then
            echo "add sge queue failed"
            return 1
        fi
    else
        echo "invalid schduler"
        return 1
    fi
}
# 添加节点到队列 
function test_add_node_to_queue() {
    scheduler_type=$1
    queue_name=$2
    compute_host=$3
    if [ x"$scheduler_type" == x"$SLURM" ]
    then
        ssh root@$compute_host "systemctl stop slurmd && sed -i \"10d\" /usr/lib/systemd/system/slurmd.service && sed -i \"10iExecStart=/opt/slurm/22.05.9/sbin/slurmd -Z --conf Feature=feature_$queue_name --conf-server $master_host -D -s\"  /usr/lib/systemd/system/slurmd.service && systemctl daemon-reload && systemctl start slurmd"
        return 0
    elif [ x"$scheduler_type" == x"$SGE" ]
    then
        bash $CLUSTER_CONTROL_FILE addSgeComputeToQueue -p $queue_name --computeHosts $compute_host
        ret_code=$?
        if [ $ret_code != 0 ]
        then
            echo "add sge queue failed"
            return 1
        fi
    else
        echo "invalid schduler"
        return 1
    fi

}
# 从队列中删除节点
function test_remove_node_from_queue() {
    scheduler_type=$1
    queue_name=$2
    compute_host=$3
    if [ x"$scheduler_type" == x"$SLURM" ]
    then
        bash $CLUSTER_CONTROL_FILE removeSlurmNode --computeHosts $compute_host
        ret_code=$?
        if [ $ret_code != 0 ]
        then
            echo "remove slurm node from queue failed"
            return 1
        fi
        return 0
    elif [ x"$scheduler_type" == x"$SGE" ]
    then
        bash $CLUSTER_CONTROL_FILE removeSgeComputeFromQueue -p $queue_name --computeHosts $compute_host
        ret_code=$?
        if [ $ret_code != 0 ]
        then
            echo "remove sge node from queue failed"
            return 1
        fi
    else
        echo "invalid schduler"
        return 1
    fi
    return 0
}
# 测试ldap
function test_ladp_login() {
    ldappasswd  -x -D "cn=Manager,dc=baiduhpc,dc=com" -W "uid=work,ou=people,dc=baiduhpc,dc=com" -S
    ret_code=$?
    if [ $ret_code != 0 ]
    then
        echo "test ldap modify passwd failed"
        return 1
    fi
    ssh work@$compute_host "exit"
    ret_code=$?
    if [ $ret_code != 0 ]
    then
        echo "test ldap login failed"
        return 1
    fi
    ssh root@$compute_host "exit"
    ret_code=$?
    if [ $ret_code != 0 ]
    then
        echo "test ldap login failed"
        return 1
    fi
    return 0
}
# 测试场景：作业提交、新建队列，新增节点，删除队列，删除节点，添加节点到队列，从队列中删除节点，重启主节点，重启计算节点
function test_all_case() {
    # 测试任务提交
    test_submit_job $scheduler_type $compute_host
    if [ $ret_code != 0 ]
    then
        echo "test submit job failed"
        return 1
    fi
    # 测试从队列中删除节点
    test_remove_node_from_queue $scheduler_type $queue_name $compute_host
    if [ $ret_code != 0 ]
    then
        echo "test remove host from queue failed"
        return 1
    fi
    # 测试删除队列
    test_remove_queue $scheduler_type  $queue_name
    if [ $ret_code != 0 ]
    then
        echo "test remove host from queue failed"
        return 1
    fi
    # 测试添加队列
    test_add_queue $scheduler_type $TEST_QUEUE_NAME
    if [ $ret_code != 0 ]
    then
        echo "test add queue failed"
        return 1
    fi
    # 测试添加节点到队列
    test_add_node_to_queue $scheduler_type $TEST_QUEUE_NAME $compute_host
    if [ $ret_code != 0 ]
    then
        echo "test add host to queue failed"
        return 1
    fi
    # 测试重启
}
parse_args $@
main