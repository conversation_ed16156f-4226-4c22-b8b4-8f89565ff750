#! /bin/bash
source /etc/profile
module load vnc_proxy/1.0.0
. ${vnc_root}/conf/vncfunction
while [ $# -gt 0 ];do
  var=$1
  shift
  case $var in
      -u|--user)
     arg_user=$1
      shift;;
      -H|--host)
      _host=$1
      shift;;
      -j|--jobid)
      _jobid=$1
      shift;;
      *)
      echo "Usage:"
      exit 1
      ;;
    esac
done
#
# Check imput args
#
#if [ $# -ne 1 ]; then
#  echo_err "usage: $0 <user name>"
#  exit 1
#fi

#arg_user=$1
#vnc_proxys=`getVncProxyServers`
vnc_proxys=${VNC_PROXYS}
nodelist=/tmp/$RANDOM
echo >$nodelist
for i in ${vnc_proxys}; 
do
  echo "$i">>$nodelist
done

psshRes=`sh ${vnc_root}/bin/pssh.sh $nodelist "sh ${vnc_root}/bin/vnc_query ${arg_user}"|grep _PSSH_FOLDER:`
output_folder=`echo "$psshRes"|grep OUTPUT_PSSH_FOLDER:|awk -F: '{print $2}'`

cat $output_folder/* |grep SID
rm $nodelist
pssh_res_folder=`dirname $output_folder`
if 
        echo $pssh_res_folder|grep "^/tmp"|grep pssh_ >/dev/null
then
        rm -rf $pssh_res_folder
fi