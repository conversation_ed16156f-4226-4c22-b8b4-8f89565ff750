#!/bin/bash
source /etc/profile
module load vnc_proxy/1.0.0

novnc_cmd=$(echo "$no_vnc --listen 8780 --web $vnc_root/noVNC/dist --token-plugin TokenFile --token-source $vnc_root/noVNC/conf/token.conf --cert $vnc_root/noVNC/conf/self.pem --file-only --log-file /opt/novnc/logs/novnc.log >/dev/null 2>&1")

cat > novnc.service << EOF
[Unit]
# Description=novnc demon service

[Service]
Type=simple
ExecStart=$novnc_cmd
KillSignal=SIGTERM
TimeoutStopSec=5
KillMode=process
[Install]
WantedBy=multi-user.target
EOF


cp novnc.service /lib/systemd/system/novnc.service
if [ $? -ne 0 ]; then
    echo "Error: Copy novnc.service failed."
    exit 1
fi
systemctl daemon-reload
systemctl restart novnc.service