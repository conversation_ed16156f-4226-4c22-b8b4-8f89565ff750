#!/bin/sh

host=$1
port=$2
username=$3
password=`python "${vnc_root}/bin/vncpasswd.py" $4`
#password=$4
fullScreen=$5
shareMode=$6

vncContent=`cat ${vnc_root}/bin/template.vnc`

if [ -f "$EXECPROXY_CONFDIR/vnc/vnc.hosts" ];then
	hostip=`cat "$EXECPROXY_CONFDIR/vnc/vnc.hosts" | grep "$host" | awk '{print $1}'`
	if [ -n "$hostip" ];then
		vncContent=`echo $vncContent| sed s?@host@?${hostip}?g`
	fi
fi

vncContent=`echo $vncContent| sed s?@host@?${host}?g`
vncContent=`echo $vncContent| sed s?@port@?${port}?g`
vncContent=`echo $vncContent| sed s?@username@?${username}?g`
vncContent=`echo $vncContent| sed s?@password@?${password}?g`
vncContent=`echo $vncContent| sed s?@fullScreen@?${fullScreen}?g`

if [ "observe" == "$shareMode" ]; then
	vncContent="$vncContent SendPointerEvents=false SendKeyEvents=false SendCutText=false"
fi

echo $vncContent | awk '{for(i=0;++i<=NF;)a[i]=a[i]?a[i] FS $i:$i}END{for(i=0;i++<NF;)print a[i]}'

