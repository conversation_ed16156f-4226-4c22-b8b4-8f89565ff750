#!/bin/bash
SCRIPT_PATH="$(cd "$(dirname "$0")" && pwd -P)"

source /etc/profile
module load vnc_proxy/1.0.0

cd $vnc_root

rm -rf noVNC/conf
mkdir -p noVNC/conf
pushd noVNC/conf
openssl req -new -x509 -days 3650 -nodes -config $SCRIPT_PATH/openssl.cnf -out self.pem -keyout self.pem

touch token.conf
for (( i=1; i<=100; i++ ))
do
    # 计算当前的端口号（从5900开始加）
    port=$((5900 + i))
    # 构造并打印目标字符串
    target="target$port: localhost:$port"
    echo "$target">>token.conf
done

popd