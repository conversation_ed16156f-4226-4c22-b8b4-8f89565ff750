#!/usr/bin/env python

import sys
#from struct import pack, unpack

import d3des

def getVncPassword(password):
    password = (password + '\x00'*8)[:8]
    strkey = ''.join([ chr(x) for x in d3des.vnckey ])
    key = d3des.deskey(strkey, False)
    crypted = d3des.desfunc(password, key)
    return crypted.encode('hex')

if __name__ == '__main__':
    passwd=sys.argv[1]
    print getVncPassword(passwd)
