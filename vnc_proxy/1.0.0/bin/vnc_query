#! /bin/bash

#
# Import util function
#
source /etc/profile
module load vnc_proxy/1.0.0
. ${vnc_root}/conf/vncfunction

#
# Check imput args
#
if [ $# -ne 1 ]; then
  echo_err "usage: $0 <user name>"
  exit 1
fi

arg_user=$1

#
# Check if current user ok
# Query use root to do
#
cur_user=`whoami`
#if [ "${cur_user}" != "root" ]; then
#  if [ "${cur_user}" != "${arg_user}" ]; then
#    echo_err "user not same as owner"
#    exit 1
#  fi
#fi

#
# Check variable config
#
if ! check_var; then
  exit 1
fi


##
tmpVncFile=/tmp/vnccheck_$RANDOM
touch $tmpVncFile
function splitVnc(){
#
  # initial session var
  #
  _VNC_USER=""
  _VNC_SID=""
  _VNC_PID=""
  _VNC_WIDTH=""
  _VNC_HEIGHT=""
  _VNC_DATE=""
  _VNC_PASS=""
  _VNC_JOB=""

  #
  # import session info
  #
  . "${_session}"

  if [ "${arg_user}" == "root" -o "${arg_user}" == "${_VNC_USER}" ]; then
  #
  # check whether pid ok
  #
  _x_pid=`pid_of_user_session ${_VNC_USER} ${_VNC_SID}`
  if [ "${_VNC_PID}" != "${_x_pid}" ]; then
    echo_err "Session \"${_VNC_SID}\" timeout (lost process)"
    rm -rf "${_session}"
    # care next
    return 1
  fi

  # check whether job ok
  if [ ! -z "${_VNC_JOB}" ]; then
	#running
	if ! check_job "${_VNC_JOB}" > /dev/null 2>&1; then
		echo_err "Session \"${_VNC_SID}\" timeout (no job)"
      # CareHere: leave session
      #rm -rf "${_session}"
		_VNC_JOB=""
	fi
  fi
  
    #
    # Print self vnc
    #
	#clean loss vnc,ignore root
	if [ -z "${_VNC_JOB}" -a "root" != "${_VNC_USER}" ];then
		${vnc_root}/bin/vnc_delete ${_VNC_USER} ${_VNC_SID}
	else
		echo "SID=${_VNC_SID},PID=${_VNC_PID},USER=${_VNC_USER},PASSWD=${_VNC_PASS},WIDTH=${_VNC_WIDTH},HEIGHT=${_VNC_HEIGHT},JOB=${_VNC_JOB},DATE=${_VNC_DATE},SERVERNAME=${_VNC_SERVERNAME},SERVERADDR=${_VNC_IPS},ARCHIVE=${_VNC_ARCHIVE},CODE=${_VNC_CODE},LOCALE=${_VNC_LOCALE}" >>$tmpVncFile
	fi
  else
    #
    # Ignore it
    #
    echo_err "Session \"${_VNC_SID}\" not owned by ${arg_user}"

  fi
}
#
# Check all vnc sessions
#
#ls -1 ${VNC_SESSION_DIR}/*.* 2>/dev/null | \
#while read _session ; do
#	splitVnc &
#done
if [ `ls ${VNC_SESSION_DIR}|wc -l` != 0 ];then
for _session in `ls ${VNC_SESSION_DIR}/*.*`
do
	splitVnc &
done
fi
wait
cat $tmpVncFile
rm -f $tmpVncFile
#
# Create Succees
#
exit 0

