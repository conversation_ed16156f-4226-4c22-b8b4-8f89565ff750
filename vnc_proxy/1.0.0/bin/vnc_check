#! /bin/bash

#
# Import util function
#
source /etc/profile
module load vnc_proxy/1.0.0
. ${vnc_root}/conf/vncfunction

#
# Check imput args
#
if [ $# -lt 2 ]; then
  echo_err "usage: $0 <user name> <vnc sid>"
  exit 1
fi

arg_user=$1
arg_sid=$2

#
# Check if current user ok
#
cur_user=`whoami`
if [ ${cur_user} != "root" ]; then
  if [ ${cur_user} != ${arg_user} ]; then
    echo_err "user not same as owner"
    exit 1
  fi
fi

#
# Check variable config
#
#if ! check_var > /dev/null 2>&1; then
if ! check_var; then
  exit 1
fi

#
# Check process
#
_pid=`pid_of_user_session ${arg_user} ${arg_sid}`
#echo "_pid:${_pid}"

if [ -z "${_pid}" ]; then
  echo_err "session \"$arg_sid\" of user \"${arg_user}\" not exist"
  exit 1
fi

sid_file=${VNC_SESSION_DIR}/${arg_user}.${arg_sid}

if [ ! -s ${sid_file} ]; then
  echo_err "session \"$arg_sid\" of user \"${arg_user}\" started manually"
  exit 1
fi

#
# Import session info
#
. ${sid_file}
#cat ${sid_file}

# CareHere: how to do
if [ "${_VNC_PID}" != "${_pid}" ]; then
  echo_err "session \"${arg_sid}\" of user \"${arg_user}\" may lost "
  exit 1
fi

#
# Check whether releated with job
#
if [ -z "${_VNC_JOB}" ]; then
  echo "session \"$arg_sid\" of user \"${arg_user}\" OK"
  exit 0
else
  if ! check_job ${_VNC_JOB}; then
    echo_err "session \"${arg_sid}\" of user \"${arg_user}\" related with job \"${_VNC_JOB}\" should be killed ( job quit )"
    #kill -9 ${_VNC_PID} 
    exit 1
  else
    echo "session \"${arg_sid}\" of user \"${arg_user}\" related with \"${_VNC_JOB}\" OK"
    exit 0
  fi
fi

