#! /bin/bash

#
# Import util function
#
source /etc/profile
module load vnc_proxy/1.0.0
. ${vnc_root}/conf/vncfunction

#
# export config path
#
export VNC_CONFIG=${vnc_root}/conf/config

#
# check whether config exist
# already inported by vncfunction
#
if ! test -f $VNC_CONFIG; then
  exit 1
fi

#
# print all variables
#
echo "VNC_HOSTNAME=$VNC_HOSTNAME"
echo "VNC_CODE=$VNC_CODE"
echo "VNC_ARCHIVE=$VNC_ARCHIVE"
echo "VNC_WIDTH=$VNC_WIDTH"
echo "VNC_HEIGHT=$VNC_HEIGHT"
echo "VNC_LOCALE=$VNC_LOCALE"
echo "VNC_LOGINPASSWD=$VNC_LOGINPASSWD"
echo "VNC_CLIENTCTRL=$VNC_CLIENTCTRL"
echo "VNC_TIMEOUT=$VNC_TIMEOUT"
echo "VNC_PROXYS=$VNC_PROXYS"

#
# Create Succees
#
exit 0

