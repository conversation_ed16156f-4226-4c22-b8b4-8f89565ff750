#! /bin/bash

#
# Import util function
#
source /etc/profile
module load vnc_proxy/1.0.0
. ${vnc_root}/conf/vncfunction
#
# Check imput args
#
if [ $# -lt 2 ]; then
  echo_err "usage: $0 <user name> <vnc sid> [<job id>]"
  exit 1
fi

arg_user=$1
arg_sid=$2
arg_job=$3

#
# Check if current user ok
#
cur_user=`whoami`
if [ ${cur_user} != "root" ]; then
  if [ ${cur_user} != ${arg_user} ]; then
    echo_err "user not same as owner"
    exit 1
  fi
fi
#
# Check variable config
#
if ! check_var; then
  exit 1
fi

#
# Check process
#
_pid=`pid_of_user_session ${arg_user} ${arg_sid}`
#echo "_pid:${_pid}"

if [ -z "${_pid}" ]; then
  echo_err "session \"$arg_sid\" of user \"${arg_user}\" not exist"
  exit 1
fi

#
# Check whether releated with job
#
#if [ -z "${arg_job}" ]; then
#  echo "session \"$arg_sid\" of user \"${arg_user}\" OK"
#  exit 0
#fi
#echo "_job:${arg_job}"
_hostname=`hostname`

sid_file=${VNC_SESSION_DIR}/${arg_user}.${arg_sid}.${_hostname}
#echo "sid_file:${sid_file}"
lock_file=${VNC_LOCKDIR}/X${arg_sid}

if [ ! -s ${sid_file} ]; then
  echo "session \"$arg_sid\" of user \"${arg_user}\" started manually"
  # CareHere: should return success
  # exit 1
  exit 0
fi

#
# Import session info
#
. ${sid_file}
#cat ${sid_file}

#echo "${_VNC_PID}"
#echo "${_pid}"

# CareHere: how to do
if [ "${_VNC_PID}" != "${_pid}" ]; then
  echo "session \"${arg_sid}\" of user \"${arg_user}\" may lost "
  # CareHere: should return success
  # exit 1
  exit 0
fi

#echo "${_VNC_SID}"
#echo "${arg_sid}"

# CareHere: how to do
if [ "${_VNC_SID}" != "${arg_sid}" ]; then
  # impossible, but modify by hand
  echo_err "session \"${arg_sid}\" of user \"${arg_user}\" mismatch!"
  exit 1
fi

#
# kill vnc session
#
if [ "${cur_user}" != "${_VNC_USER}" ]; then
  _output="`su -l ${_VNC_USER} -c \"${VNC_SERVER} -kill :${arg_sid}\"`"
  _result="$?"
else
  _output="`${VNC_SERVER} -kill :${arg_sid} 2>&1`"
  _result="$?"
fi

if [ ! "${_result}" = "0" ] ; then
  if [ ! -z "${_VNC_PID}" ] ; then
    kill -9 ${_VNC_PID} > /dev/null 2>&1
    _result="$?"
  fi
  if [ ! "${_result}" = "0" ] ; then
    echo_err "session \"${arg_sid}\" Killing Failed"
    exit 1
  fi
fi

rm -rf "${sid_file}" > /dev/null 2>&1
rm -rf "${lock_file}" > /dev/null 2>&1

clean_passwdfile_and_xstartup

echo "session \"${arg_sid}\" Killed OK"

#
# Create Succees
#
exit 0
