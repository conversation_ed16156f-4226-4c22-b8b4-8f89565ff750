#!/bin/bash

query_status_connect() {
   targetstr=''
   for line in $(grep -n $1 $2 | awk -F: '{print $1}')
   do
      datestr=''
      flag=${line}
      while [ "$datestr" == "" ]
      do
         templine=$(sed -n "${flag}{x;p};h" $2)
         datestr=$(echo ${templine} | grep '..:..:..')
         #echo "look line is: $dateline"
         flag=$((flag - 1))
      done
      #echo "target line is: $datestr"
	  format_datestr=$(date "+%Y-%m-%d %H:%M:%S" -d "$datestr")
      #echo $format_datestr
      datastr=$(sed -n "${line} p" $2)
	  format_datastr=$(expr "$datastr" : '.*\( [0-9]\+\.[0-9]\+\.[0-9]\+\.[0-9]\+::[0-9]\+\)');
	  if [ "$format_datastr" == "" ];then
		continue;
	  fi
      #echo "datastr is : $datastr";
	  if [ "$1" == "accepted" ];then
         targetstr="${targetstr}OPENTIME=${format_datestr},CLIENTINFO=${format_datastr};"
	  else
         targetstr="${targetstr}CLOSETIME=${format_datestr},CLIENTINFO=${format_datastr};"
	  fi
   done
   if [ "$1" == "accepted" ];then
      echo "[authenticated] ${targetstr%;}"
   else
      echo "[disconnected] ${targetstr%;}" 
   fi
}

if [ "$#" -eq 0 ];then
  node=$(hostname)
  echo ${node}
  #pslist=$(ps -ef | grep vnc | grep "/Xvnc" | grep -v "grep" | awk '{print $1$9}' 2>/dev/null)
  pslist=$(ps -eo user:32,args | grep vnc | grep "/Xvnc" | grep -v "grep" | awk '{print $1$3}' 2>/dev/null)
  for i in $pslist
  do
    uname=$(echo $i | cut -d ":" -f1)
    vport=$(echo $i | cut -d ":" -f2)
    #uhome=$(finger -mlp ${uname}|head -n2| tail -n1|awk '{print $2}' 2>/dev/null)
    uhome=$(getent passwd | grep "^${uname}:" | tail -n1 | cut -d":" -f6)
    vpid=$(cat ${uhome}/.vnc/${node}:${vport}.pid)
    echo "--------------------"
    echo "${uname}-${vpid}-${node}:${vport}"
    query_status_connect 'accepted' ${uhome}/.vnc/${node}:${vport}.log
    query_status_connect 'closed' ${uhome}/.vnc/${node}:${vport}.log
  done
else
  user=""
  vsname=""
  while [ $# -gt 0 ];
  do
     var=$1
     shift
     case $var in
     -u|--user)
     user=$1
     shift;;
     -v|--vsname)
     vsname=$1
     shift;;
	 *)
	 echo "Usage:"
	 exit 1
	 ;;
     esac
  done
  if [ "$user" == "" ];then
     echo "invalid value of -u/--user : $user"
	 exit 1
  fi
  if [ "$vsname" == "" -o "$(echo $vsname | grep \:)" == "" ];then
     echo "invalid value of -v/--vsname : $vsname"
	 exit 1
  fi
  node=$(echo $vsname | cut -d ":" -f1)
  vport=$(echo $vsname | cut -d ":" -f2)
  #echo "node: $node, vport: $vport"
  #uhome=$(finger -mlp ${user}|head -n2| tail -n1|awk '{print $2}' 2>/dev/null)
  uhome=$(getent passwd | grep "^${user}:" | tail -n1 | cut -d":" -f6)
  #echo "uhome: $uhome"
  #echo "${uhome}/.vnc/${node}:${vport}.log"

  vpid=$(cat ${uhome}/.vnc/${node}:${vport}.pid)
  echo "${user}-${vpid}-${node}:${vport}"
  query_status_connect 'accepted' ${uhome}/.vnc/${node}:${vport}.log
  query_status_connect 'closed' ${uhome}/.vnc/${node}:${vport}.log
fi
