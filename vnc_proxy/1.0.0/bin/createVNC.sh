#!/bin/bash
#-u --user
user=""
#-j --jobid
job=""

while [ $# -gt 0 ];do
  var=$1
  shift 
  case $var in
      -u|--user)
      user=$1
      shift;;
      -j|--jobid)
      job=$1
      shift;;
      *)
      echo "Usage: -u(--user) / -j(--jobid)" 1>&2
      ;;
    esac
done

#echo user:$user
#echo jobid:$job
if
        echo "$user"|grep ^- >/dev/null
then
        echo "invalid value of -u : $user"
        exit 1;
fi
if [ "" == "$user" ];then
	echo "lack:-u/--user"
	exit 1;
fi
currentUser=`whoami`
if [ "$currentUser" != "$user" ];then
	echo "$user is not match current user"
	exit 1
fi

if
        echo "$job"|grep ^- >/dev/null
then
        echo "invalid value of -j : $job"
        exit 1;
fi


#=====================================
sh ${vnc_root}/bin/managercreate "$user" "$job"
