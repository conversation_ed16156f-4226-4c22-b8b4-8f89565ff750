#! /bin/bash

export PATH=.:$PATH

#
# Import util function
#
source /etc/profile
module load vnc_proxy/1.0.0
. ${vnc_root}/conf/vncfunction

#
# Query all Xvnc process
#
function queryVncProcs() {

	# for CentOS 5.x / SLES 10.x
	# root     14156     1  0 15:59 pts/5    00:00:00 Xvnc :3 -desktop gv253:3 (root) -httpd ${vnc_root}/classes -auth /root/.Xauthority -geometry 1280x1024 -rfbwait 30000 -rfbauth /root/.vnc/passwd -rfbport 5903 -fp catalogue:/etc/X11/fontpath.d -pn -rfbauth /root/.vnc/passwd.root.20150717172610.cdd6c8b2
	# for CentOS 6.x / SLES 11.x
	# root     14156     1  0 15:59 pts/5    00:00:00 /usr/bin/Xvnc :3 -desktop gv253:3 (root) -httpd ${vnc_root}/classes -auth /root/.Xauthority -geometry 1280x1024 -rfbwait 30000 -rfbauth /root/.vnc/passwd -rfbport 5903 -fp catalogue:/etc/X11/fontpath.d -pn -rfbauth /root/.vnc/passwd.root.20150717172610.cdd6c8b2
	# liulinw+ 192930      1  0 7月30 ?       00:07:51 /bin/Xvnc :7 -auth /public/home/<USER>/.Xauthority -desktop node1:7 (liulinwei) -fp catalogue:/etc/X11/fontpath.d -geometry 1280x1088 -httpd ${vnc_root}/classes -pn -rfbauth /public/home/<USER>/.vnc/passwd -rfbport 5907 -rfbwait 30000 -rfbauth /public/home/<USER>/.vnc/passwd.20190730082024.6ef3bf24 -listen tcp	
	#for linx
	#root      5776     1  0 Nov03 ?        00:00:10 Xvnc4 :1 -desktop ningsi52:1 (root) -httpd /usr/share/vnc-java -auth /root/.Xauthority -geometry 1280x1088 -depth 16 -rfbwait 30000 -rfbauth /root/.vnc/passwd -rfbport 5901 -pn -fp /usr/X11R6/lib/X11/fonts/Type1/,/usr/X11R6/lib/X11/fonts/Speedo/,/usr/X11R6/lib/X11/fonts/misc/,/usr/X11R6/lib/X11/fonts/75dpi/,/usr/X11R6/lib/X11/fonts/100dpi/,/usr/share/fonts/X11/misc/,/usr/share/fonts/X11/Type1/,/usr/share/fonts/X11/75dpi/,/usr/share/fonts/X11/100dpi/ -co /etc/X11/rgb -rfbauth /root/.vnc/passwd.20161103095532.126498f8

	#
	# if length of username is larger than 8, uid replaced
	#    so change default column width of username to 32
	#
	#ps -eo pid,user=CumbersomeUserNames -o args | grep vnc | grep -v grep | awk '
	if
		lsb_release -d|grep Linx
	then
		#Linx
		ps -eo pid,user:32,args | grep vnc | grep -v grep | awk '
		{       
			if ($3 == "Xvnc4") {
				split($4,arr,":");
				printf("%10-d %s.%s\n",$1,$2,arr[2]);
			}
		}'
	else
		#SUSE & REDHAT
	ps -eo pid,user:32,args | grep vnc | grep -v grep | awk '
	{	
		if ($3 == "/usr/bin/Xvnc" || $3 == "/bin/Xvnc" || $3 == "Xvnc") {
			split($4,arr,":");
			printf("%10-d %s.%s\n",$1,$2,arr[2]);
		}
	}'
	fi

}

#
# 
#
function randomfile() {

	local datetime=`date +'%Y%m%d_%H%M%S'`
	local tempfile=.T${datetime}.R$RANDOM
	echo $tempfile

}

function deleteFile()
{
	if [ $# -eq 1 ]; then
		test -f $1 && rm -f $1 > /dev/null 2>&1
	fi
}

#
# Truncate line from file, depending on keystr
#
function deleteKeyline() {

#	if [ $# -ne 2 ]; then
#    		echo_err "Invalid parameter"
#		exit 1
#	fi

#	if [ ! -f $1 ]; then
#    		echo_err "Fine \"$1\" not found"
#		exit 1
#	fi

	local tempfile=$(randomfile)
	until [ ! -f "$tempfile" -a "$tempfile" != "$1" ]
	do
		tempfile=$(randomfile)
		sleep 1
	done

	cat $1 | grep -v -w "$2" > $tempfile
	mv -f $tempfile $1

}

function vnc_session_timeout() {

  local sessionfile=$1
  local begin=${_VNC_DATE}
  local now=`date +'%Y-%m-%d %H:%M:%S'` 
  local runtime=$(($(date +%s -d "$now") - $(date +%s -d "$begin")));
  local maxtime=$((VNC_TIMEOUT*60));
  if [ $runtime -gt $maxtime ]; then
    echo "Session $sessionfile Timeout ($runtime>$maxtime)"
    return 0
  else
    echo "Session $sessionfile NOT Timeout ($runtime<=$maxtime)"
    return 1
  fi

}

function vnc_session_connected() {

  local sessionfile=$1
  #
  # VNC_BASEPORT can be found at ${vnc_root}/conf/config
  #    if not configed, use default below:
  #    580X: http URL port
  #    590X: VNC Port for VncViewer
  #    600X: DISPLAY accepted for external session (export DISPLAY={hostname:{id}})
  #
  if [ -z "$VNC_BASEPORT" ]; then
    VNC_BASEPORT="5800 5900 6000"
  fi
  #local desktop_no=${_VNC_SID}
  local desktop_no=`echo "$sessionfile" | cut -d. -f2`
  local related_ports=""
  for typex in $(echo $VNC_BASEPORT)
  do
    portx=$((typex + desktop_no))
    if [ -z "$related_ports" ]; then
      related_ports=$portx
    else
      related_ports="$related_ports:$portx"
    fi
  done

  #echo "related_ports=$related_ports"

  local CONNECTED=$(netstat -tnp | grep -w Xvnc | awk -v CHECKPORTS="$related_ports" 'NF==7 && $6=="ESTABLISHED" {
  #    printf("jobport=%s\n",CHECKPORTS);
    portfds=split(CHECKPORTS,parr,":");
    fds=split($4,uarr,":");
  #    printf("fds[1]=%s, fds[%d]=%s\n",uarr[1],fds,uarr[fds]);
    for (i=1; i <= portfds; i++) {
      #if (parr[i] == uarr[fds]){
      if (fds == 2 && parr[i] == uarr[fds]){
        printf("%d\n",parr[i]);
        exit
      }
    }
  }')
  local ret=1
  if [ -z "$CONNECTED" ]; then
    echo "Session $sessionfile NOT Connected"
  else
    echo "Session $sessionfile Connected (Port:$(echo $CONNECTED))"
    ret=0
  fi
  return $ret
}

arg_user=""

#
# Check imput args
#
if [ $# -gt 1 ]; then
  echo_err "usage: $0 [<username>]"
  exit 1
elif [ $# -eq 1 ]; then
  arg_user="$1"
fi

#
# Root can clean all vnc
# non-Root clean self vn
#

#
# Check if current user ok
# Query use root to do
#
cur_user=`whoami`
if [ "${cur_user}" != "root" ]; then
  if [ -z "${arg_user}" ]; then
    arg_user=$cur_user
  elif [ "${cur_user}" != "${arg_user}" ]; then
    echo_err "non-root user can only clean self session"
    exit 1
  fi
else
  if [ -z "$arg_user" ]; then
    arg_user=$cur_user
  fi
fi

#
# Check variable config
#
if ! check_var; then
  exit 1
fi

#
# Working in session dir
#
cd ${VNC_SESSION_DIR} >/dev/null 2>&1

TRACEFILE=$(randomfile)

#echo "TRACEFILE CONTENT:"
#queryVncProcs
queryVncProcs > $TRACEFILE

#
# Check all vnc sessions
#
ls -1 *.* 2>/dev/null | \
while read _session ; do

  #
  # Two kinds format of filename
  # 1. root.1
  # 2. root.1.gv253
  #
  keystr=${_session}
  fdnum=$(echo $_session | awk -F"." '{print NF}')
  if [ $fdnum -eq 2 ]; then
    :
  elif [ $fdnum -eq 3 ]; then
    keystr=${_session%.*}
  else
    # invalid filename, delete it?
    #rm -f ${_session} >/dev/null 2>&1
    continue
  fi

  #
  # initial session var
  #
  _VNC_USER=""
  _VNC_SID=""
  _VNC_PID=""
  _VNC_WIDTH=""
  _VNC_HEIGHT=""
  _VNC_DATE=""
  _VNC_PASS=""
  _VNC_JOB=""
  _VNC_PASSWDFILE=""
  _VNC_XSTARTUP=""

  #
  # import session info
  #
  . "${_session}"

  # pass it
  if [ "$arg_user" != "all" -a "$arg_user" != "$_VNC_USER" ]; then
    echo_err "Session \"${_VNC_SID}\" not owned by $arg_user"
    deleteKeyline $TRACEFILE $keystr
    continue
  fi

  #
  # only valid process left
  #

  #
  # check pid 
  #
  #echo "check pid"
  if ! grep -w ${keystr} $TRACEFILE >/dev/null 2>&1; then
    echo_err "Session \"${_VNC_SID}\" process lost"
    rm -rf "${_session}"
    #  rm -f /tmp/.X${_VNC_SID}-lock
    #  rm -f /tmp/.X11-unix/X${_VNC_SID}
    clean_passwdfile_and_xstartup
    continue
  fi
  
  _x_pid=`grep -w ${keystr} $TRACEFILE 2>&1 | awk 'NR==1{print $1}'`
  if [ "${_VNC_PID}" != "${_x_pid}" ]; then
    echo_err "Session \"${_VNC_SID}\" process mismatch"
    rm -rf "${_session}"
    #  rm -f /tmp/.X${_VNC_SID}-lock
    #  rm -f /tmp/.X11-unix/X${_VNC_SID}
    clean_passwdfile_and_xstartup
    continue
  fi

  # check whether job ok
  if [ ! -z "${_VNC_JOB}" ]; then

    #if ! qselect -s R | grep -w "${_VNC_JOB}" > /dev/null 2>&1; then
    if ! check_job "${_VNC_JOB}"; then
      echo_err "Session \"${_VNC_SID}\" job not found"
      #kill -9 ${_VNC_PID}
      #rm -f /tmp/.X${_VNC_SID}-lock
      #rm -f /tmp/.X11-unix/X${_VNC_SID}
      rm -rf "${_session}"
      clean_passwdfile_and_xstartup
      continue
    fi

  fi

  #
  # check whether unused longer than timeout 
  #
  #echo "check connection"
  if ! vnc_session_connected ${_session} && vnc_session_timeout ${_session}; then
    echo_err "Session \"${_VNC_SID}\" have been unused for too long time"
    rm -f ${_session}
    clean_passwdfile_and_xstartup
    continue
  fi

  # omit the vnc process that is normal and needn't be killed
  deleteKeyline $TRACEFILE ${keystr}

done

  #echo "final result:"
  #cat $TRACEFILE 
  #echo
  #ls -l $TRACEFILE 

  if [ ! -s $TRACEFILE ]; then
    echo "NO session need clean"
    deleteFile $TRACEFILE
    exit 0
  fi

  #
  # kill all vnc pids of the user
  #
  killpids=`cat $TRACEFILE | awk -v USER=$arg_user '{
    if (USER != "all"){
      split($2,uarr,".");
      if (USER == uarr[1]){
        print $1
      }
    } else {
      print $1
    }
  }'`

  if [ ! -z "$killpids" ]; then
    echo "process to be killed:" $killpids
    kill -9 $killpids
  fi

  rmlocks=`cat $TRACEFILE | awk -v USER=$arg_user '{
    split($2,uarr,".");
    if (USER != "all"){
      if (USER == uarr[1]){
        printf("/tmp/.X%d-lock\n",uarr[2]);
      }
    } else {
        printf("/tmp/.X%d-lock\n",uarr[2]);
    }
  }'`
  if [ ! -z "$rmlocks" ]; then
    echo "vnc lock file to be remove:" $rmlocks
    rm -f $rmlocks
  fi

  rmunix=`cat $TRACEFILE | awk -v USER=$arg_user '{
    split($2,uarr,".");
    if (USER != "all"){
      if (USER == uarr[1]){
        printf("/tmp/.X11-unix/X%d\n",uarr[2]);
      }
    } else {
        printf("/tmp/.X11-unix/X%d\n",uarr[2]);
    }
  }'`
  if [ ! -z "$rmunix" ]; then
    echo "vnc unix socket to be removed:" $rmunix
    rm -f $rmunix
  fi

#rm -f $TRACEFILE
deleteFile $TRACEFILE

cd - > /dev/null 2>&1

#
# Clean Succees
#
exit 0

