#! /bin/bash

#
# Import util function
#
source /etc/profile
module load vnc_proxy/1.0.0
. ${vnc_root}/conf/vncfunction

#
# Check imput args
#
if [ $# -lt 3 ]; then
  echo_err "usage: $0 <server name>  <user name> <vnc sid> [<job id>]"
  exit 1
fi


arg_servername=$1
arg_user=$2
arg_sid=$3
arg_job=$4


ssh "${arg_servername}" "sh ${vnc_root}/bin/vnc_delete ${arg_user} ${arg_sid} ${arg_job}"

python ${vnc_root}/noVNC/utils/websockify/deleteToken.py  
