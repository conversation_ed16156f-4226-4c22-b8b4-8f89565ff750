#! /bin/bash

#
# Import util function
#
source /etc/profile
module load vnc_proxy/1.0.0
. ${vnc_root}/conf/vncfunction

function is_redhat_7()
{
  # check version of redhat
  test -f /etc/redhat-release && cat /etc/redhat-release |grep " 7.*">/dev/null
}

#
# Check imput args
#
if [ $# -lt 1 ]; then
  echo_err "usage: $0 <user name> [<job id>]"
  exit 1
fi

arg_user=$1
arg_job=$2

#
# Check if current user ok
#
cur_user=`whoami`
if [ ${cur_user} != ${arg_user} ]; then
  echo_err "user not same as owner"
  exit 1
fi

#
# Check variable config
#
if ! check_var; then
  exit 1
fi

#
# Check whether job exist
#
if test ! -z "${arg_job}" && ! check_job $arg_job; then
  echo_err "job not exist"
  exit 1
fi

#////////////////////////////////////

if [ ! -d "${HOME}/.vnc" ]; then
  mkdir "${HOME}/.vnc"
fi

# backup $HOME/.vnc/xstartup
#if [ -f "${XSTARTUP_USER}" ] ; then
#  mv -f "${XSTARTUP_USER}" "${XSTARTUP_BAK}"
#fi

# copy self or default xstartup.template
if [ -f "${TEMPLATE_USER}" ]; then
  cp -f "${TEMPLATE_USER}" "${XSTARTUP_USER}"
else
  cp -f "${TEMPLATE_DEFAULT}" "${XSTARTUP_USER}"
fi
chmod 755 "${XSTARTUP_USER}"

#_pass_file="/tmp/passwd.${arg_user}.`date '+%Y%m%d%H%M%S'`.${RANDOM}"
#_pass_file="${HOME}/.vnc/passwd"

  	
if ! _pass_file=`touch_pass_file`; then
  	echo_err "VNC passwd file exist"
  	exit 1
fi

#_pass_file_bak="${_pass_file}.saved"

#if [ -f "${_pass_file}" ]; then
#  mv -f "${_pass_file}" "${_pass_file_bak}"
#fi

#_pass=`get_pass`
_pass=`get_pass`

#
# generate passwd file for vncserver
#

#
# touch default passwd for older vncserver (at Centos5.x and SLES 10.x)
#   if not ${HOME}/.vnc/passwd exist, 
#   vncserver will block at starting, even if other passwd file was specified
#
if [ ! -f "${VNC_LOGINAUTH}" ]; then
  if [ -z "${VNC_LOGINPASSWD}" ]; then
    VNC_LOGINPASSWD="dawning123"
  fi
  if ! make_pass_to_file ${VNC_LOGINAUTH} ${VNC_LOGINPASSWD}; then
    echo_err "VNC default passwd fail, ret: $?"
    exit 1
  fi
fi

# for self passwd file
if ! make_pass_to_file ${_pass_file} ${_pass}; then
  echo_err "VNC passwd fail, ret: $?"
  exit 1
fi

# set init value
_exitcode="0"
_result=""

strRandom="${arg_user}.`date '+%Y%m%d%H%M%S'`.${RANDOM}"
startlog="/tmp/.vconsole.${strRandom}"
if [ ! -z "${arg_job}" ]; then
  startlog="/tmp/.job_${arg_job}.${strRandom}"
fi

_addon_params=""
if is_redhat_7; then
  _addon_params="-listen tcp"
fi

for i in `seq 1 ${MAX_TRY}`
do
    #_result="`${VNC_SERVER} -geometry ${VNC_WIDTH}x${VNC_HEIGHT} -rfbauth \"${_pass_file}\" 2>&1`"
    ${VNC_SERVER} -geometry ${VNC_WIDTH}x${VNC_HEIGHT} -rfbauth ${_pass_file} ${_addon_params} >${startlog}  2>&1
    #${VNC_SERVER} -geometry ${VNC_WIDTH}x${VNC_HEIGHT} >${startlog}  2>&1
    _exitcode="$?"

    clear_warning ${startlog}

    if [ "${_exitcode}" != "0" ] ; then
      echo "start bad : $i" >> ${startlog}
      #echo "start bad : $i"
      sleep 1
      continue
    else
      #echo "try to get start: $i" >> ${startlog}
      echo "start ok : $i" >> ${startlog}
      #echo "start ok : $i"
      break
    fi

done

echo "Exit value like this : ${_exitcode}" >> ${startlog}

#
#( #cd "${HOME}"
#  DISPLAY=":${VNC_SID}.0"
#  export DISPLAY
#  #eval ${@}
#) >/dev/null 2>&1 </dev/null &
#

sleep 1

#if [ -f "${XSTARTUP_BAK}" ] ; then
#  (
#    mv -f "${XSTARTUP_BAK}" "${XSTARTUP_USER}"
#  ) >/dev/null 2>&1 </dev/null &
#fi

#if [ -f "${_pass_file_bak}" ] ; then
#  (
#    mv -f "${_pass_file_bak}" "${_pass_file}"
#  ) >/dev/null 2>&1 </dev/null &
#fi

#rm -rf "${_pass_file}"
if [ "${_exitcode}" != "0" ] ; then
  test -f ${_pass_file} && rm -f ${_pass_file} > /dev/null 2>&1
  echo_err "VNC Server start fail, ret: ${_exitcode}"
  exit 1
fi

_result="`cat ${startlog}`"
rm -f ${startlog}

# get SID
_sid="`echo \"${_result}\" | awk 'BEGIN{FS=\":\"}/desktop is/{print \$NF}'`"
#echo "sid=${_sid}"
_hostname=`hostname`
#IPS=`echo -e $(/sbin/ifconfig  | grep 'inet addr:' | cut -d: -f2 | awk '{ print $1}')`
#IPS=`echo -e $(/sbin/ifconfig  2>/dev/null | grep 'inet addr:'| grep -v '127.0.0.1' | cut -d: -f2 | awk '{ print $1}')`

lsb_version=$(echo `lsb_release -r | cut -d ":" -f2`)
max_version=${lsb_version:0:1}
IPS=''
if [ "$(( $max_version >= 7 ))" == "1" ];then
  IPS=`echo -e $(/sbin/ifconfig  2>/dev/null | grep 'inet '| grep -v '127.0.0.1' | awk '{print $2}')`
else
  IPS=`echo -e $(/sbin/ifconfig  2>/dev/null | grep 'inet addr:'| grep -v '127.0.0.1' | cut -d: -f2 | awk '{ print $1}')`
fi

#echo "${_sid}:${_hostname}:${IPS}:${VNC_ARCHIVE}:${VNC_CODE}:${VNC_WIDTH}:${VNC_HEIGHT}:${_pass}"

( #cd "${HOME}"
  DISPLAY=":${_sid}.0"
  export DISPLAY
  xhost + 
  #eval ${@}
) >/dev/null 2>&1 </dev/null &

pass_encrypted=`rsa_encrypt_base64 $_pass`

# get PID
_pid=`pid_of_user_session ${arg_user} ${_sid}`
#echo "pid=${_pid}"
#_hostname=`hostname`
_date=`date +'%Y-%m-%d %H:%M:%S'`
echo "SID=${_sid},PID=${_pid},USER=${arg_user},PASSWD=${pass_encrypted},WIDTH=${VNC_WIDTH},HEIGHT=${VNC_HEIGHT},JOB=${arg_job},QUEUE=${_VNC_QUEUE},DATE=${_date},SERVERNAME=${_hostname},SERVERADDR=${IPS},ARCHIVE=${VNC_ARCHIVE},CODE=${VNC_CODE},LOCALE=${VNC_LOCALE}"
cat > "${VNC_SESSION_DIR}/${arg_user}.${_sid}.${_hostname}" <<EOF
_VNC_USER="${arg_user}"
_VNC_SID="${_sid}"
_VNC_PID="${_pid}"
_VNC_IPS="${IPS}"
_VNC_WIDTH="${VNC_WIDTH}"
_VNC_HEIGHT="${VNC_HEIGHT}"
_VNC_DATE="${_date}"
_VNC_PASS="${pass_encrypted}"
_VNC_JOB="${arg_job}"
_VNC_SERVERNAME="${_hostname}"
_VNC_ARCHIVE="${VNC_ARCHIVE}"
_VNC_CODE="${VNC_CODE}"
_VNC_LOCALE="${VNC_LOCALE}"
_VNC_TIMEOUT="${VNC_TIMEOUT}"
_VNC_PASSWDFILE="${_pass_file}"
_VNC_XSTARTUP="${XSTARTUP_USER}"

EOF

cp "${VNC_SESSION_DIR}/${arg_user}.${_sid}.${_hostname}" ${HOME}/.vnc

#
# Create Succees
#
exit 0

