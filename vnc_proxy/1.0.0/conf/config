#!/bin/bash

# Top directory of VNC tools
VNC_ROOT=${vnc_root}

# VNC WORK DIR
VNC_SESSION_DIR=${vnc_session}

# VNC CONF DIR
VNC_CONF_DIR=${VNC_ROOT}/conf

# VNC_CONF_DEFAULT
VNC_CONF_DEFAULT=${VNC_CONF_DIR}/DEFAULT.xstartup

# hostname or IP address of VNC Server
VNC_HOSTNAME="${proxy_host}"

#VNC http home
VNC_CLASS_DIR="${vnc_root}/classes"

# class and jar file of VNC Applet
VNC_CODE="com.tigervnc.vncviewer.VncViewer.class"
VNC_ARCHIVE="VncViewer.jar"

# set default window size
VNC_WIDTH="1280"
VNC_HEIGHT="1088"

# max idle time of session
# - unit: minutes (about 4 hours)
VNC_TIMEOUT=${vnc_timeout}

#
# Max number of trying to start vncserver
#
MAX_TRY=5

# VNC Ports based on
VNC_BASEPORT="5800 5900 6000"

# set default login passwd
VNC_LOGINPASSWD="dawning123"
VNC_LOGINAUTH="${HOME}/.vnc/passwd"

# Encoding used by VNC Plugin
VNC_LOCALE="zh_CN.UTF-8"
#VNC_LOCALE="C"

# Xstart variable
XSTARTUP_USER="${HOME}/.vnc/xstartup"
XSTARTUP_BAK="${HOME}/.vnc/xstartup.saved"
TEMPLATE_DEFAULT="${VNC_CONF_DEFAULT}"
TEMPLATE_USER="${VNC_CONF_DIR}/${USER}.xstartup"

# show control menu in applet (yes or no)
VNC_CLIENTCTRL="yes"

# VNC lock file
VNC_LOCKDIR=/tmp/.X11-unix

# Vnc Server Utility path
VNC_SERVER="/usr/bin/vncserver"

# Vnc Password Utility path
VNC_PASSWD="${vnc_root}/vncpasswd"

DB_USERNAME="root"
DB_NAME="gv_local"

DB_PASSWD="root123"

#
# All VNC Servers
VNC_PROXYS="${proxy_host}"

