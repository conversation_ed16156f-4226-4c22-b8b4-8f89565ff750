#!/bin/bash

# import Global Variable
source /etc/profile
module load vnc_proxy/1.0.0
. ${vnc_root}/conf/config

echo_err() {
  echo "$*" >&2
}

print_vnc_conf(){

  echo "---- VNC Configuration ----"
  echo VNC_ROOT=${VNC_ROOT}
  echo VNC_SESSION_DIR=${VNC_SESSION_DIR}
  echo VNC_CONF_DIR=${VNC_CONF_DIR}
  echo VNC_CONF_DEFAULT=${VNC_CONF_DEFAULT}
  echo VNC_HOSTNAME=${VNC_HOSTNAME}
  echo VNC_SERVER=${VNC_SERVER}
  echo VNC_PASSWD=${VNC_PASSWD}
  echo VNC_CLASS_DIR=${VNC_CLASS_DIR}
  echo VNC_CODE=${VNC_CODE}
  echo VNC_ARCHIVE=${VNC_ARCHIVE}
  echo VNC_LOCALE=${VNC_LOCALE}
  echo

}

set_lang(){

  # Setting locale
  if [ -z "${VNC_LOCALE}" ] ; then
    VNC_LOCALE="C"
  fi
  LANG="${VNC_LOCALE}"; export LANG
  LANGUAGE="${VNC_LOCALE}"; export LANGUAGE
  LC_ALL="${VNC_LOCALE}"; export LC_ALL
  

}

check_var(){

  # Check VNC_ROOT
  if [ -z "${VNC_ROOT}" ]; then
    echo_err "VNC_ROOT not set"
    return 1
  fi
  if [ ! -d ${VNC_ROOT} ]; then
    echo_err "VNC_ROOT(${VNC_ROOT}) not exist"
    return 1
  fi

  # Check VNC_SESSION_DIR
  if [ -z "${VNC_SESSION_DIR}" ]; then
    echo_err "VNC_SESSION_DIR not set"
    return 1
  fi
  if [ ! -d ${VNC_SESSION_DIR} ]; then
    echo_err "VNC_SESSION_DIR(${VNC_SESSION_DIR}) not exist"
    return 1
  fi

  # Check VNC_CONF_DIR
  if [ -z "${VNC_CONF_DIR}" ]; then
    echo_err "VNC_CONF_DIR not set"
    return 1
  fi
  if [ ! -d ${VNC_CONF_DIR} ]; then
    echo_err "VNC_CONF_DIR(${VNC_CONF_DIR}) not exist"
    return 1
  fi

  # Check VNC_CONF_DEFAULT
  if [ -z "${VNC_CONF_DEFAULT}" ]; then
    echo_err "VNC_CONF_DEFAULT not set"
    return 1
  fi
  if [ ! -f ${VNC_CONF_DEFAULT} ]; then
    echo_err "VNC_CONF_DEFAULT(${VNC_CONF_DEFAULT}) not exist"
    return 1
  fi

  # Check VNC_HOSTNAME
  if [ -z "${VNC_HOSTNAME}" ]; then
    echo_err "VNC_HOSTNAME not set"
    return 1
  fi
  if ! ping -c 1 ${VNC_HOSTNAME} > /dev/null 2>&1; then
    echo_err "VNC_HOSTNAME(${VNC_HOSTNAME}) not connected"
    return 1
  fi

  # Check VNC_SERVER
  if [ -z "Z${VNC_SERVER}" ]; then
    echo_err "VNC_SERVER not set"
    return 1
  fi
  if [ ! -x ${VNC_SERVER} ]; then
    echo_err "VNC_SERVER(${VNC_SERVER}) not executable"
    return 1
  fi

  # Check VNC_PASSWD
  if [ -z "${VNC_PASSWD}" ]; then
    echo_err "VNC_PASSWD not set"
    return 1
  fi
  if [ ! -x ${VNC_PASSWD} ]; then
    echo_err "VNC_PASSWD(${VNC_PASSWD}) not executable"
    return 1
  fi

  # Check VNC_CLASS_DIR
  if [ -z "${VNC_CLASS_DIR}" ]; then
    echo_err "VNC_CLASS_DIR not set"
    return 1
  fi
  if [ ! -d ${VNC_CLASS_DIR} ]; then
    echo_err "VNC_CLASS_DIR(${VNC_CLASS_DIR}) not exist"
    return 1
  fi

  # Check VNC_CODE
  if [ -z "${VNC_CODE}" ]; then
    echo_err "VNC_CODE not set"
    return 1
  fi
  # Commont out VNC_CODE
  # Because in some kind of Linux, VncViewer.class exist in VNC_ARCHIVE (jar package)
  #
  #if [ ! -f ${VNC_CLASS_DIR}/${VNC_CODE} ]; then
  #  echo_err "VNC_CODE(${VNC_CODE}) not exist"
  #  return 1
  #fi

  # Check VNC_ARCHIVE
  if [ -z "${VNC_ARCHIVE}" ]; then
    echo_err "VNC_ARCHIVE not set"
    return 1
  fi
  if [ ! -f ${VNC_CLASS_DIR}/${VNC_ARCHIVE} ]; then
    echo_err "VNC_ARCHIVE(${VNC_ARCHIVE}) not exist"
    return 1
  fi

  # Check VNC_LOCALE
  if [ -z "${VNC_LOCALE}" ]; then
    VNC_LOCALE="C"
  fi
  LANG="${VNC_LOCALE}"; export LANG
  LANGUAGE="${VNC_LOCALE}"; export LANGUAGE
  LC_ALL="${VNC_LOCALE}"; export LC_ALL

  return 0
}

check_job(){
  SCONTROL_PATH="/opt/slurm/22.05.9/bin/scontrol"
  if [ -f "$SCONTROL_PATH" ]; then
    /opt/slurm/22.05.9/bin/scontrol show job $1 > /dev/null 2>&1
  else
    /opt/pbs/bin/qstat -f $1 > /dev/null 2>&1
  fi
}

#
# for random string
#
get_random_str(){

  local randomstr=""

  # Length of random str
  local strlen=8

  #############
  # Method Zero:
  #             : limit-1: static string
  #############
  #randomstr="dawning123"

  #############
  # Method One:
  #             : limit-1: length maybe difference
  #############
  #randomstr="$RANDOM"

  #############
  # Method Two:
  #             : limit-1: depend on utils "od" (coreutils)
  #             : limit-2: only 8-bit string supported
  #############
  #randomstr=$(od -N 4 -t x4 /dev/random | head -1 | awk '{print $2}')
  # Change random to urandom, because random may take too long time before return (at CentOS7)
  randomstr=$(od -N 4 -t x4 /dev/urandom | head -1 | awk '{print $2}')

  #############
  # Method Three:
  #             : limit-1: depend on utils "md5sum" (coreutils)
  #             : limit-2: strlen should less than 33 (md5sum)
  #############
  #randomstr=$(cat /dev/urandom | head -1 | md5sum | head -c $strlen)

  #############
  # Method Four:
  #             : limit-1: depend on utils "strings" (binutils)
  #             : limit-2: response time is longer than other method
  #############
  #randomstr=$(cat /dev/urandom | sed 's/[^a-zA-Z0-9]//g' | strings -n $strlen | head -c $strlen)

  echo $randomstr
  
}

#
# Generate random passwd filename
#
get_pass_file(){

  #
  # final passfile name
  #
  local passfile=""

  #
  # random string, for passfile name
  #
  local ramstr=`get_random_str`

  #
  # base name for passwd file
  #
  local basefile="${HOME}/.vnc/passwd"

  #
  # get filename, format {base file}.{user}.{random}
  #
  #passfile=${basefile}.`whoami`.`date '+%Y%m%d%H%M%S'`.${ramstr}
  passfile=${basefile}.`date '+%Y%m%d%H%M%S'`.${ramstr}
  echo $passfile
  
}

#
# Generate random string for vnc-passwd
#
get_pass(){

  local pass=`get_random_str`

  echo $pass
  
}

#
# query proc pid of specified session
# usage: pid_of_user_session {user name} {session id}
#
pid_of_user_session(){

  if [ $# -ne 2 ]; then
    echo_err "invalid arguments"
    return
  fi
  local uname=$1
  local sid=$2
  if ! id $uname >&/dev/null; then
    echo_err "user $uname not exist"
    return
  fi
  if
	  lsb_release -d|grep Linx >/dev/null 2>&1
  then
	#Linx
  	ps -eo pid,user:32,args | grep -w "Xvnc4 :${sid}" | grep -v 'grep' | awk -v USER=${uname} '{if ($2 == USER) print $1}'
  else
  	ps -eo pid,user:32,args | grep -w "Xvnc :${sid}" | grep -v 'grep' | awk -v USER=${uname} '{if ($2 == USER) print $1}'
  fi
}

#
# clear warnning session
# usage: clear_warning {vnc start log file}
# for example: /tmp/xxx
#
clear_warning(){

  local idlist="`awk -F: 'NF==2{if($1 ~/Remove this file if there is no X server/){print $2}}' $1`"
  #echo $idlist
  if [ ! -z "${idlist}" ]; then
    for i in ${idlist};
    do
      lock1=/tmp/.X11-unix/X$i
      lock2=/tmp/.X$i-lock
      # care socket file
      if [ -S ${lock1} ]; then
	rm -f ${lock1}
      fi
      # care socket file
      if [ -f ${lock2} ]; then
	rm -f ${lock2}
      fi
    done
  fi

}

vnc_start(){
  UNAME="`whoami`"
  VNC_PROXYSERVER=`gethost`
  #VNC_PROXYSERVER="gv244"
  ssh -o StrictHostKeyChecking=no -o BatchMode=yes ${VNC_PROXYSERVER} ${vnc_root}/bin/vnc_create ${UNAME} $1
  #${vnc_root}/bin/managercreate ${UNAME} $1
  return $?
}

vnc_stop(){
  UNAME="`whoami`"
  #ssh -o StrictHostKeyChecking=no -o BatchMode=yes $2 ${vnc_root}/bin/vnc_delete $UNAME $1 > /dev/null 2>&1
  ssh -o StrictHostKeyChecking=no -o BatchMode=yes $2 ${vnc_root}/bin/vnc_delete $UNAME $1 
  return $?
}

gethost(){
_executename=''
_min=0
_index=1

#
# Check all VNC_PROXYS servers
#

# Method One: not worked
#VNC_PROXYS=`getVncProxyServers`

# Method Two: not worked
#VNC_PROXYS already setted in ${vnc_root}/conf/config

# Method Three: worked
# Query "VNC_PROXYS" configuration from $VNC_HOSTNAME
ALL_PROXYS=$(ssh -o StrictHostKeyChecking=no -o BatchMode=yes ${VNC_HOSTNAME} ${vnc_root}/bin/vnc_config | grep -w VNC_PROXYS | awk -F= 'NF==2{print $2}')
if [ ! -z "$ALL_PROXYS" ]; then
  VNC_PROXYS=$ALL_PROXYS
fi

for i in ${VNC_PROXYS};
do
_date="`date +'%Y-%m-%d%H:%M:%S'`"
#echo "${_date}"
#echo "${arg_user}"
UNAME="`whoami`"
#echo $i
ssh -o StrictHostKeyChecking=no -o BatchMode=yes $i ${vnc_root}/bin/vnc_query root >> /tmp/$i."${_date}".txt
_num=`cat /tmp/$i.${_date}.txt | wc -l`
#echo ${_date}
if [ "${_index}" -eq 1 ];then
_executename=$i
_min="${_num}"
_index=2
fi

sleep 1
#echo ${_num}
if [ "${_num}" -lt "${_min}" ]; then
  _min=${_num}
_executename=$i
#echo ${_mun}
fi

done
echo ${_executename};
}


function clean_passwdfile_and_xstartup()
{

  #
  # Griview3.2.0, add _VNC_PASSWDFILE and _VNC_XSTARTUP
  #
  
  # delete _VNC_PASSWDFILE (which is not the default ${HOME}/.vnc/passwd)
  # worked at Gridview3.2.0 and later
  #
  if [ ! -z "${_VNC_PASSWDFILE}" ] && [ "${_VNC_PASSWDFILE}" != "${HOME}/.vnc/passwd" ]; then
    rm -rf "${_VNC_PASSWDFILE}" > /dev/null 2>&1
  fi
  
  #
  # delete _VNC_XSTARTUP (which is not the default ${HOME}/.vnc/xstartup)
  # NOT worked at Gridview3.2.0, because ${HOME}/.vnc/xstartup is used
  #
  if [ ! -z "${_VNC_XSTARTUP}" ] && [ "${_VNC_XSTARTUP}" != "${XSTARTUP_USER}" ]; then
    rm -rf "${_VNC_XSTARTUP}" > /dev/null 2>&1
  fi

}

function touch_pass_file()
{

  local findname=0
  local filename=""
  for i in `seq 1 ${MAX_TRY}`
  do
  	filename=`get_pass_file`
  	if [ -f ${filename} ]; then
  		sleep 1
  		continue
  	else
  		findname=1
  		break
  	fi
  done

  if [ ${findname} -eq 0 ]; then
	return 1
  fi

  if ! touch ${filename}; then
	return 1
  fi

  echo "${filename}"
  return 0

}

#
# Utils to make pass to passwd file
# Usage: $0 <passfile> <passwd>
#
function make_pass_to_file()
{

  if [ $# -ne 2 ]; then
    return 2
  fi

  local passfile=$1
  local password=$2

  # use vncpasswd
  echo ${password} | ${VNC_PASSWD} -f > ${passfile} 2>/dev/null
  if [ $? -ne 0 ]; then
  	echo ${password} | vncpasswd -f > ${passfile} 2>/dev/null
  	if [  $? -ne 0 ]; then
  		test -f ${passfile} && rm -f ${passfile} > /dev/null 2>&1
		return 1
  	fi
  fi
  
  # use vncpasswd.arg
  #${VNC_PASSWD} ${passfile} ${password} 2>/dev/null
  
  chmod 600 ${passfile}
  #echo "passfile:${passfile}"

  return 0
}

#get all Vnc Proxy Servers from database on tables resource and resource_relation;
getVncProxyServers(){
    RES=`ssh -o StrictHostKeyChecking=no -o BatchMode=yes ${VNC_HOSTNAME} "/opt/gvmysql/bin/mysql --protocol=tcp -P 3309 -u${DB_USERNAME} -p${DB_PASSWD}"  <<EOF
        use ${DB_NAME};
        SELECT NAME FROM gv_rm_resource res,gv_rm_resource_relation rel WHERE res.ID=rel.DEPENDANT_ID AND rel.DEPENDED_ID='1006';
EOF`
    if [ -z "$RES" ];then
#       echo "$RES is empty"
        RES=`hostname`
    fi  
    
    if [ -n "$RES" ];then
        READL= echo $RES | cut -d' ' -f 2-  
    fi  
    echo $READL
}

function generate_rsa_keys {
    local key_dir="$1"
    mkdir -p "$key_dir"
    pushd "$key_dir"
    openssl genpkey -algorithm RSA -out private_key.pem -pkeyopt rsa_keygen_bits:2048
    openssl rsa -pubout -in private_key.pem -out public_key.pem
    popd
    echo "RSA 密钥对已生成在 $key_dir"
}

function rsa_encrypt_base64 {
    local public_key="$rsa_key_path/public_key.pem"
    local plaintext="$1"
    echo -n "$plaintext" | openssl rsautl -encrypt -pubin -inkey "$public_key" | base64
}

function rsa_decrypt_base64 {
    local private_key="$rsa_key_path/private_key.pem"
    local encrypted_base64="$1"
    local decrypted_raw=$(echo "$encrypted_base64" | base64 --decode | openssl rsautl -decrypt -inkey "$private_key")

    echo -n "$decrypted_raw"
}

function get_password {
    sessionfile=$1
    vnc_pass=$(awk '/_VNC_PASS/{flag=1; print; next} flag; /_VNC_JOB/{exit}' "$sessionfile" | tr -d '\n' | grep -oP '_VNC_PASS="\K.*' | grep -oP '.*?(?="_VNC_JOB)' | head -n1)

    rsa_decrypt_base64 $vnc_pass
}

function get_session_id {
    sessionfile=$1
    sessionId=$(cat $sessionfile|sed -n 's/^_VNC_SID="\(.*\)".*$/\1/p')
    echo -n $sessionId
}

function get_password_by_job {
    jobId=$1
    vnc_job="_VNC_JOB=\"${jobId}\""
    sessionfile=""

    for file in "${vnc_session}"/*; do
        # 检查文件是否存在且是一个普通文件
        if [ -f "$file" ]; then
            # 使用 grep 检查文件中是否包含 $vnc_job
            if grep -qF -- "$vnc_job" "$file"; then
                sessionfile=$file
            fi
        fi
    done

    if [ -f "$sessionfile" ]; then
        get_password $sessionfile
    fi
}

function get_vnc_port_by_job {
    jobId=$1
    vnc_job="_VNC_JOB=\"${jobId}\""
    sessionfile=""

    for file in "${vnc_session}"/*; do
        # 检查文件是否存在且是一个普通文件
        if [ -f "$file" ]; then
            # 使用 grep 检查文件中是否包含 $vnc_job
            if grep -qF -- "$vnc_job" "$file"; then
                sessionfile=$file
            fi
        fi
    done

    if [ -f "$sessionfile" ]; then
        get_session_id $sessionfile
    fi
}