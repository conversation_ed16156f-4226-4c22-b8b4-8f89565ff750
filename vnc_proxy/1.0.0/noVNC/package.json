{"name": "@novnc/novnc", "version": "1.5.0", "description": "An HTML5 VNC client", "browser": "lib/rfb", "directories": {"lib": "lib", "doc": "docs", "test": "tests"}, "files": ["lib", "AUTHORS", "VERSION", "docs/API.md", "docs/LIBRARY.md", "docs/LICENSE*"], "scripts": {"lint": "eslint app core po/po2js po/xgettext-html tests utils", "test": "karma start karma.conf.js", "prepublish": "node ./utils/convert.js --clean"}, "repository": {"type": "git", "url": "git+https://github.com/novnc/noVNC.git"}, "author": "<PERSON> <<EMAIL>> (https://github.com/kanaka)", "contributors": ["<PERSON> <<EMAIL>> (https://github.com/samhed)", "<PERSON> <<EMAIL>> (https://github.com/CendioOssman)"], "license": "MPL-2.0", "bugs": {"url": "https://github.com/novnc/noVNC/issues"}, "homepage": "https://github.com/novnc/noVNC", "devDependencies": {"@babel/core": "latest", "@babel/preset-env": "latest", "babel-plugin-import-redirect": "latest", "browserify": "latest", "chai": "latest", "commander": "latest", "eslint": "latest", "fs-extra": "latest", "globals": "latest", "jsdom": "latest", "karma": "latest", "karma-mocha": "latest", "karma-chrome-launcher": "latest", "@chiragrupani/karma-chromium-edge-launcher": "latest", "karma-firefox-launcher": "latest", "karma-ie-launcher": "latest", "karma-mocha-reporter": "latest", "karma-safari-launcher": "latest", "karma-script-launcher": "latest", "karma-sinon-chai": "latest", "mocha": "latest", "node-getopt": "latest", "po2json": "latest", "sinon": "latest", "sinon-chai": "latest"}, "dependencies": {}, "keywords": ["vnc", "rfb", "novnc", "websockify"]}