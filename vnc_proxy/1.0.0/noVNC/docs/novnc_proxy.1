.TH novnc_proxy 1  "June 25, 2020" "version 1.2.0" "USER COMMANDS"

.SH NAME
novnc_proxy - noVNC proxy server
.SH SYNOPSIS
.B novnc_proxy [--listen [HOST:]PORT] [--vnc VNC_HOST:PORT] [--cert CERT] [--ssl-only]

Starts the WebSockets proxy and a mini-webserver and
provides a cut-and-paste URL to go to.

    --listen [HOST:]PORT  Port for proxy/webserver to listen on
                          Default: 6080 (on all interfaces)
    --vnc VNC_HOST:PORT   VNC server host:port proxy target
                          Default: localhost:5900
    --cert CERT           Path to combined cert/key file, or just
                          the cert file if used with --key
                          Default: self.pem
    --key KEY             Path to key file, when not combined with cert
    --web WEB             Path to web files (e.g. vnc.html)
                          Default: ./
    --ssl-only            Disable non-https connections.

    --record FILE         Record traffic to FILE.session.js

    --syslog SERVER       Can be local socket such as /dev/log, or a UDP host:port pair.

    --heartbeat SEC       send a ping to the client every SEC seconds
    --timeout SEC         after SEC seconds exit when not connected
    --idle-timeout SEC    server exits after SEC seconds if there are no
                          active connections

.SH AUTHOR
The noVNC Authors
https://github.com/novnc/noVNC

.SH SEE ALSO
websockify(1), nova-novncproxy(1)
