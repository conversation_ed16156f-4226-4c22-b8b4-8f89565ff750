# slurm.conf file 

# 集群名和hostname
# cluster_name=
# SlurmctldHost=
# 高可用需添加
# SlurmctldHost=
# SlurmctldHost=
# common conf
GresTypes=gpu
MaxNodeCount=10000
MpiDefault=pmix
ProctrackType=proctrack/cgroup
ReturnToService=1
SlurmctldPidFile=/var/run/slurmctld.pid
SlurmctldPort=6817
SlurmdPidFile=/var/run/slurmd.pid
SlurmdPort=6818
SlurmdSpoolDir=/var/spool/slurmd
SlurmUser=slurm
# StateSaveLocation=/var/spool/slurmctld
# 高可用替换为共享存储路径
# StateSaveLocation=/mnt/cfs/slurmctld
SwitchType=switch/none
TaskPlugin=task/cgroup
TreeWidth=65533
SlurmctldParameters=enable_configless
# TIMERS
InactiveLimit=0
KillWait=30
MinJobAge=300
# SlurmctldTimeout=120
# 高可用替换为
# SlurmctldTimeout=30
SlurmdTimeout=300
Waittime=0
# SCHEDULING
SchedulerType=sched/backfill
SelectType=select/cons_tres
SelectTypeParameters=CR_CPU_Memory
# JOB PRIORITY
PriorityType=priority/multifactor
PriorityWeightQOS=10
# accounting conf
AccountingStorageEnforce=limits
AccountingStorageType=accounting_storage/slurmdbd
JobCompPass=Baidu@123
JobCompType=jobcomp/mysql
JobCompUser=slurm
JobAcctGatherFrequency=30
# JobAcctGatherType=jobacct_gather/cgroup11
# 作业监控需添加
# AcctGatherProfileType=acct_gather_profile/hdf5
# JobAcctGatherFrequency=10 
# JobAcctGatherType=jobacct_gather/linux
# 高可用需添加
# JobCompHost=127.0.0.1
# JobCompLoc=slurm_jobcomp_db
# JobCompPort=6446
# log file conf
SlurmctldDebug=info
SlurmctldLogFile=/var/log/slurm/slurmctld.log
SlurmdDebug=info
SlurmdLogFile=/var/log/slurm/slurmd.log
SrunPortRange=60001-63000
# auth
AuthAltTypes=auth/jwt
AuthAltParameters=jwt_key=/var/spool/slurm/statesave/jwt_hs256.key
# COMPUTE NODES
# Nodeset=set_$queue_name Feature=feature_$queue_name
# partition_name=$queue_name Default=YES Nodes=set_$queue_name



