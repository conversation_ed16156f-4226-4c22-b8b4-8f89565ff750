# slurm db conf file
# munge auth
AuthType=auth/munge
AuthInfo=/var/run/munge/munge.socket.2
# jwt auth
AuthAltTypes=auth/jwt
AuthAltParameters=jwt_key=/var/spool/slurm/statesave/jwt_hs256.key
# slurm conf 
SlurmUser=slurm
MessageTimeout=60
DbdHost=localhost
DbdAddr=127.0.0.1
# 高可用场景需要填写
# DbdBackupHost=
# DbdBackupHost

# log conf
DebugLevel=debug5
DefaultQOS=normal
LogFile=/var/log/slurm/slurmdbd.log
PidFile=/var/run/slurmdbd.pid
# storge conf
StorageType=accounting_storage/mysql
StorageHost=localhost
StorageLoc=slurm_acct_db
# StoragePort=3306
# 高可用场景替换
# StoragePort=6446
StorageUser=slurm
StoragePass=Baidu@123
TrackSlurmctldDown=yes


