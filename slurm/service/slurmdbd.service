[Unit]
Description=Slurm DBD accounting daemon
After=network-online.target munge.service mysql.service mysqld.service mariadb.service
Wants=network-online.target
ConditionPathExists=/opt/slurm/22.05.9/etc/slurmdbd.conf

[Service]
Type=simple
EnvironmentFile=-/etc/sysconfig/slurmdbd
EnvironmentFile=-/etc/default/slurmdbd
ExecStart=/opt/slurm/22.05.9/sbin/slurmdbd -D -s $SLURMDBD_OPTIONS
ExecReload=/bin/kill -HUP $MAINPID
LimitNOFILE=65536
TasksMax=infinity

# Uncomment the following lines to disable logging through journald.
# NOTE: It may be preferable to set these through an override file instead.
#StandardOutput=null
#StandardError=null

[Install]
WantedBy=multi-user.target
