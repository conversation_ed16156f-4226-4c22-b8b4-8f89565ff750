[Unit]
Description=Slurm REST daemon
After=network-online.target slurmctld.service
Wants=network-online.target
ConditionPathExists=/opt/slurm/22.05.9/etc/slurm.conf

[Service]
Type=simple
EnvironmentFile=-/etc/sysconfig/slurmrestd
EnvironmentFile=-/etc/default/slurmrestd
# slurmrestd should not run as root or the slurm user.
# Please either use the -u and -g options in /etc/sysconfig/slurmrestd or
# /etc/default/slurmrestd, or explicitly set the User and Group in this file
# an unpriviledged user to run as.
# User=
# Group=
# Default to listen on both socket and slurmrestd port
User=slurmapi
Group=slurmapi
ExecStart=/opt/slurm/22.05.9/sbin/slurmrestd -f /opt/slurm/22.05.9/etc/slurmrestd.conf -a jwt -s openapi/v0.0.38,openapi/dbv0.0.38 0.0.0.0:6820
# Enable auth/jwt be default, comment out the line to disable it for slurmrestd
# Environment="SLURM_JWT=daemon"
ExecReload=/bin/kill -HUP $MAINPID
Environment=LD_LIBRARY_PATH=/usr/local/lib

[Install]
WantedBy=multi-user.target
