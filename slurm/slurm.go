/*
 * @Author: sunkaixuan01 <EMAIL>
 * @Date: 2022-11-30 15:25:05
 * @LastEditors: sunkaixuan01 <EMAIL>
 * @LastEditTime: 2023-02-09 16:23:28
 * @FilePath: \slurm_conf\slurm\slurm.go
 * @Description: slurm节点启动函数
 */
package slurm

import (
	"fmt"

	"github.com/astaxie/beego/logs"

	"baidu/hpc/chpc_image_builder/ldap"
	"baidu/hpc/chpc_image_builder/util"
)

// 启动slurm节点,根据节点类型执行对应的启动命令
func StartSlurmNode(clusterParam util.ConsleParam) (err error) {
	args := []string{util.ShellSciptName, "startNfs"}
	err = util.ExecCmd(args...)
	if err != nil {
		logs.Error(err.Error())
		return
	}
	if clusterParam.NodeType == util.MasterNode {
		// 主节点启动函数
		err = startSlurmMasterNode(clusterParam)
		if err != nil {
			logs.Error(err.Error())
			return
		}
	} else if clusterParam.NodeType == util.ComputeNode {
		// 计算节点启动函数
		err = startSlurmComputeNode(clusterParam)
		if err != nil {
			logs.Error(err.Error())
			return
		}
	} else if clusterParam.NodeType == util.BackUpNode {
		// 备节点启动函数
		err = startSlurmBackupNode(clusterParam)
		if err != nil {
			logs.Error(err.Error())

			return
		}
	} else if clusterParam.NodeType == util.LoginNode {
		// 登录节点启动函数
		err = startSlurmLoginNode(clusterParam)
		if err != nil {
			logs.Error(err.Error())
			return
		}
	} else {
		err = fmt.Errorf("invalid node type %v", clusterParam.NodeType)
		logs.Error(err.Error())
		return
	}
	return
}

// 启动slurm主节点
func startSlurmMasterNode(clusterParam util.ConsleParam) (err error) {
	logs.Debug("create slurm clutser conf is %+v", clusterParam)
	err = ldap.StartLdapServer()
	if err != nil {
		logs.Error(err.Error())
		return
	}
	err = ldap.StartLdapClient(clusterParam)
	if err != nil {
		logs.Error(err.Error())
		return
	}
	if clusterParam.EnableHa {
		// TO DO,高可用启动
		return
	}
	// 非高可用场景主节点启动
	defaultPartitionList := make([]string, 0)
	otherPartitionList := make([]string, 0)
	for _, par := range clusterParam.QueueList {
		if par.IsDefault {
			defaultPartitionList = append(defaultPartitionList, par.Name)
		} else {
			otherPartitionList = append(otherPartitionList, par.Name)
		}
	}
	defaultPartitionStr := util.Array2String(defaultPartitionList...)
	otherPartitionListStr := util.Array2String(otherPartitionList...)

	// 修改主节点配置
	args := []string{util.ShellSciptName, "modifySlurmMasterConfNoHA", "-c", clusterParam.ClusterName, "--masterHost", clusterParam.MasterHostName}
	err = util.ExecCmd(args...)
	if err != nil {
		logs.Error("modify slurm master conf fail,error info is %s", err.Error())
		return
	}

	// 添加非默认队列信息
	if len(otherPartitionList) > 0 {
		args = []string{util.ShellSciptName, "createSlurmQueues", "-p", otherPartitionListStr}
		err = util.ExecCmd(args...)
		if err != nil {
			logs.Error("add other queue:%v fail,error info is %s", otherPartitionListStr, err.Error())
			return
		}
	}
	// 添加默认队列信息
	if len(defaultPartitionList) > 1 {
		err = fmt.Errorf("only allow one default queue,please change cluster conf")
		logs.Error(err.Error())
		return
	}
	if len(defaultPartitionList) > 0 {
		args = []string{util.ShellSciptName, "createSlumDefaultQueue", "-p", defaultPartitionStr}
		err = util.ExecCmd(args...)
		if err != nil {
			logs.Error("add default queue:%v fail,error info is %s", defaultPartitionStr, err.Error())
			return
		}
	}
	// 启动主节点
	args = []string{util.ShellSciptName, "startSlurmMasterNoHA", "-c", clusterParam.ClusterName, "--masterHost", clusterParam.MasterHostName}
	err = util.ExecCmd(args...)
	if err != nil {
		logs.Error("start slurm master node fail,error info is %s", err.Error())
		return
	}

	region := clusterParam.Region
	if "" == region {
		region = "default"
	}

	args = []string{util.ShellSciptName, "startChpcServer", "--region", region}
	err = util.ExecCmd(args...)
	if err != nil {
		logs.Error("start chpc server fail,error info is %s", err.Error())
		return
	}

	return
}

// 启动slurm计算节点
func startSlurmComputeNode(clusterParam util.ConsleParam) (err error) {
	err = ldap.StartLdapClient(clusterParam)
	if err != nil {
		logs.Error(err.Error())
		return
	}
	var queueName string
	// 获取计算节点归属的队列
	for _, par := range clusterParam.QueueList {
		for _, node := range par.Node {
			result, err := util.CheckIP(node.IP)
			if err != nil {
				logs.Error("Get local ip error,%v", err.Error())
				return err
			}
			if result {
				queueName = par.Name
				break
			}
		}
	}
	if queueName == "" {
		err = fmt.Errorf("slurmqueue_name is empty,please check cluster param:%+v", clusterParam)
		logs.Error(err.Error())
		return
	}

	args := []string{
		util.ShellSciptName, "startSlurmComputeNoHA",
		"--masterHost", clusterParam.MasterHostName,
		"-p", queueName,
		"-c", clusterParam.ClusterName,
	}

	err = util.ExecCmd(args...)
	if err != nil {
		logs.Error("start slurm compute fail,error info is %s", err.Error())
		return
	}
	return
}

// TO DO,备节点
func startSlurmBackupNode(clusterParam util.ConsleParam) (err error) {
	args := []string{"1.sh", "StartSlurmSlave"}
	err = util.ExecCmd(args...)
	if err != nil {
		logs.Error("start slurm slave,error info is %s", err.Error())
		return
	}
	return
}

// 登录节点
func startSlurmLoginNode(clusterParam util.ConsleParam) (err error) {

	args := []string{
		util.ShellSciptName, "startSlurmLoginNoHA",
		"--masterHost", clusterParam.MasterHostName,
		"-c", clusterParam.ClusterName,
	}

	err = util.ExecCmd(args...)
	if err != nil {
		logs.Error("start slurm login fail,error info is %s", err.Error())
		return
	}
	return
}
