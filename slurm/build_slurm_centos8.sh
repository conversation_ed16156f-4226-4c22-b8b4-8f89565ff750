#! /bin/bash
###
 # @Author: sunkaixuan01 <EMAIL>
 # @Date: 2022-12-19 21:06:24
 # @LastEditors: sunkaixuan01 <EMAIL>
 # @LastEditTime: 2023-02-06 10:27:47
 # @FilePath: \chpc_image_builder\slurm\build_slurm.sh
 # @Description: slurm产品库制作
###
set -x
set -e

CURRENT_DIR=$(cd $(dirname $0); pwd)
# 临时文件下载目录
TEMP_DOWNLOAD_DIR="/opt/downloads"
# slurm conf path
SLURM_CONF_FILES="$CURRENT_DIR/conf/*.conf"
SLURM_SERVICE_FILES="$CURRENT_DIR/service/*.service"
SLURM_CONF_TEMPLATE_FILES="$CURRENT_DIR/conf/*.template"
SLURM_SERVICE_TEMPLATE_FILES="$CURRENT_DIR/service/*.template"
#munge.key path
MUNGE_KEY_FILE="$CURRENT_DIR/conf/munge.key"
# hwloc安装路径
HWLOC_INSTALL_DIR="/opt/hwloc/2.8.0"
# pmix安装路径
PMIX_INSTALL_DIR="/opt/pmix/3.1.6"
# slurm安装路径
SLURM_INSTALL_DIR="/opt/slurm/22.05.9"
# openmpi安装路径
MPI_INSTALL_DIR="/opt/openmpi/4.1.5"

REDHAT=1
UBUNTU2004=2
UBUNTU2204=3

# 判断当前操作系统为centos或ubuntu
function check_env() {
    if [ -f "/etc/redhat-release" ]
    then
        echo $REDHAT
    elif [ -f "/etc/lsb-release" ]
    then
        source /etc/lsb-release
        if [ "$DISTRIB_RELEASE" = "20.04" ]; then
          echo $UBUNTU2004
        elif [ "$DISTRIB_RELEASE" = "22.04" ]; then
          echo $UBUNTU2204
        fi
    else
        echo 0
    fi
}

# slurm 安装
function install_slurm() {
    if [ ! -d $TEMP_DOWNLOAD_DIR ]
    then
        mkdir -p $TEMP_DOWNLOAD_DIR
    fi
    cd $TEMP_DOWNLOAD_DIR
    ret_code=`check_env`
    if [ $ret_code == $REDHAT ]
    then
        yum install -y lz4-devel numactl-devel libevent libevent-devel environment-modules libjwt libjwt-devel munge munge-libs hdf5 hdf5-devel json-c json-c-devel python3 gcc-c++ libtool-ltdl-devel readline-devel --skip-broken
        # mysql安装
        wget https://chpc-dev.bj.bcebos.com/image-build/mysql-8.0.31-1.el7.x86_64.rpm-bundle.tar
        wget https://chpc-dev.bj.bcebos.com/image-build/mysql-router-community-8.0.31-1.el7.x86_64.rpm
        wget https://chpc-dev.bj.bcebos.com/image-build/mysql-shell-8.0.31-1.el7.x86_64.rpm
        tar -xvf mysql-8.0.31-1.el7.x86_64.rpm-bundle.tar
        yum install -y $TEMP_DOWNLOAD_DIR/mysql-*.rpm
        # centos需要执行,ubuntu不需要
        create-munge-key -f
        chown -R munge: /etc/munge/ /var/log/munge/ /var/lib/munge/ /var/run/munge/
    elif [ $ret_code == $UBUNTU2004 ]
    then
        apt update && apt-get -y install vim unzip git build-essential libssl-dev libmysql++-dev libnuma-dev environment-modules cmake libpmi-pmix-dev hwloc libevent-dev libhwloc-dev libjwt-dev libpmix2 libpmix-dev libmunge-dev libjson-c-dev libhttp-parser-dev libyaml-dev libhdf5-dev munge

        # mysql安装
        wget https://chpc-dev.bj.bcebos.com/image-build/mysql-server_8.0.30-1ubuntu20.04_amd64.deb-bundle.tar
        tar -xvf mysql-server_8.0.30-1ubuntu20.04_amd64.deb-bundle.tar
        DEBIAN_FRONTEND=noninteractive apt -yq install $TEMP_DOWNLOAD_DIR/mysql-*.deb
        # mysql router安装
        wget https://chpc-dev.bj.bcebos.com/image-build/mysql-router-community_8.0.30-1ubuntu20.04_amd64.deb
        apt -y install $TEMP_DOWNLOAD_DIR/mysql-router-community_8.0.30-1ubuntu20.04_amd64.deb
        # mysql shell安装
        wget https://chpc-dev.bj.bcebos.com/image-build/mysql-shell_8.0.30-1ubuntu20.04_amd64.deb
        apt -y install $TEMP_DOWNLOAD_DIR/mysql-shell_8.0.30-1ubuntu20.04_amd64.deb

    elif [ $ret_code == $UBUNTU2204 ]
        then
            # 禁止弹出交互窗口，默认不重启服务，仅列出可选项
            sed -i "s/\#\$nrconf{restart} = 'i'/\$nrconf{restart} = 'l'/" /etc/needrestart/needrestart.conf
            apt update && apt-get -y install git build-essential libssl-dev libmysql++-dev libnuma-dev environment-modules cmake hwloc libevent-dev libhwloc-dev libjwt-dev libpmix2 libpmix-dev libmunge-dev libjson-c-dev libhttp-parser-dev libyaml-dev libhdf5-dev munge
            # {slurm_base}/lib/slurm/cgroup_v2.so need libdbus-1-dev
            apt-get -y install libdbus-1-dev

            # mysql安装
            wget https://chpc-dev.bj.bcebos.com/image-build/mysql-server_8.0.33-1ubuntu22.04_amd64.deb-bundle.tar
            tar -xvf mysql-server_8.0.33-1ubuntu22.04_amd64.deb-bundle.tar
            DEBIAN_FRONTEND=noninteractive apt -yq install $TEMP_DOWNLOAD_DIR/mysql-*.deb
            # mysql router安装
            wget https://chpc-dev.bj.bcebos.com/image-build/mysql-router-community_8.0.33-1ubuntu22.04_amd64.deb
            apt -y install $TEMP_DOWNLOAD_DIR/mysql-router-community_8.0.33-1ubuntu22.04_amd64.deb
            # mysql shell安装
            wget https://chpc-dev.bj.bcebos.com/image-build/mysql-shell_8.0.33-1ubuntu22.04_amd64.deb
            apt -y install $TEMP_DOWNLOAD_DIR/mysql-shell_8.0.33-1ubuntu22.04_amd64.deb

    else
        echo "unexpected os,only support centos or ubuntu,exit!!!"
        return 1
    fi
    cd $TEMP_DOWNLOAD_DIR/
    rm -rf *
    # slurm安装
    wget https://chpc-dev.bj.bcebos.com/image-build/hwloc-hwloc-2.8.0.tar.gz
    wget https://chpc-dev.bj.bcebos.com/image-build/pmix-3.1.6.tar.gz
    wget https://chpc-dev.bj.bcebos.com/image-build/slurm-22.05.9.tar.bz2
#    wget https://chpc.bj.bcebos.com/packages/openmpi-4.1.5.tar.gz
    tar -xf hwloc-hwloc-2.8.0.tar.gz
    tar -xf pmix-3.1.6.tar.gz
    tar -xf slurm-22.05.9.tar.bz2
#    tar -xf openmpi-4.1.5.tar.gz

    # apt 自动安装的hwloc（2.1.0版本，2.7.0版本），在bcc.gn5.c2m8 规格的实例上会导致libhwloc.so产生FPE错误，无法启动slurmd
    # 编译安装hwloc-2.8.0可以解决这个问题，具体问题原因是hwloc的版本还是apt导致的？需要进一步排查
    cd $TEMP_DOWNLOAD_DIR/hwloc-hwloc-2.8.0
    ./autogen.sh
    ./configure --prefix=$HWLOC_INSTALL_DIR
    make -j
    make install

    # pmix编译
    cd $TEMP_DOWNLOAD_DIR/pmix-3.1.6
    ./configure --prefix=$PMIX_INSTALL_DIR
    make -j
    make install

    #openmpi编译
#    cd $TEMP_DOWNLOAD_DIR/openmpi-4.1.5
#    ./configure --prefix=$MPI_INSTALL_DIR
#    make -j
#    make install
#
#    #openmpi module file
#    if [ ! -d /etc/modulefiles/mpi ]
#    then
#        mkdir -p /etc/modulefiles/mpi
#    fi
#    cd $CURRENT_DIR/../modulefiles
#    bash generate_openmpi-4.1.5.sh $MPI_INSTALL_DIR 4.1.5 > /etc/modulefiles/mpi/openmpi-4.1.5

    # slurm编译
    ln -fs $CURRENT_DIR/lib/libnvidia-ml.so.520.61.05 $CURRENT_DIR/lib/libnvidia-ml.so
    cd $TEMP_DOWNLOAD_DIR/slurm-22.05.9
    ./configure --with-mysql_config=/usr/bin --with-json --with-jwt --with-http-parser --with-yaml --with-hwloc=$HWLOC_INSTALL_DIR --with-pmix=$PMIX_INSTALL_DIR --with-munge --with-hdf5 --prefix=$SLURM_INSTALL_DIR --with-nvml LDFLAGS="-L$CURRENT_DIR/lib/" CPPFLAGS="-I$CURRENT_DIR/include/"
    make -j
    make install


    echo PATH=$PMIX_INSTALL_DIR/bin:$SLURM_INSTALL_DIR/bin:$SLURM_INSTALL_DIR/sbin:\$PATH >> /etc/profile
    source /etc/profile

    # conf 替换
    mkdir -p $SLURM_INSTALL_DIR/etc/
    cp $SLURM_CONF_FILES $SLURM_INSTALL_DIR/etc/
    cp $SLURM_SERVICE_FILES /usr/lib/systemd/system/
    # template替换
    mkdir -p $SLURM_INSTALL_DIR/template/tmp
    cp $SLURM_CONF_TEMPLATE_FILES $SLURM_INSTALL_DIR/template/
    cp $SLURM_SERVICE_TEMPLATE_FILES $SLURM_INSTALL_DIR/template/

    # munge.key 替换, md5: 34c98e1170201c57e9faca1bafa9344c
    # slurm 集群依赖munge做权限校验，只有munge.key一致才能成功将新节点加入到集群中
    cp -f $MUNGE_KEY_FILE /etc/munge/

    ret_code=`check_env`
    if [ $ret_code == 1 ]
    then
        systemctl disable mysqld
        systemctl stop mysqld
    elif [ $ret_code == 2 ]
    then
        systemctl disable mysql
        systemctl stop mysql
    fi

    systemctl stop mysqlrouter
    systemctl stop munge
    systemctl disable mysqlrouter
    systemctl disable slurmctld
    systemctl disable slurmd
    systemctl disable slurmdbd
    systemctl disable slurmrestd
    systemctl disable munge

    cd $TEMP_DOWNLOAD_DIR
    rm -rf *
}
install_slurm