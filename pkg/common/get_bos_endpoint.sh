#!/bin/bash

# 设置不同地域的BOS地址
get_bos_endpoint() {
    local region=$1
    local endpoint

    case $region in
    "bj")
        endpoint="https://chpc-online.bj.bcebos.com"
        ;;
    "bd")
        endpoint="https://chpc-online-bd.bd.bcebos.com"
        ;;
    "gz")
        endpoint="https://chpc-online-gz.gz.bcebos.com"
        ;;
    "su")
        endpoint="https://chpc-online-su.su.bcebos.com"
        ;;
    "fwh")
        endpoint="https://chpc-online-fwh.fwh.bcebos.com"
        ;;
    "sandbox")
        endpoint="http://chpc-sandbox.bj-bos-sandbox.baidu-int.com"
        ;;
    "bjtest")
        endpoint="https://chpc-online-bjtest.bj.bcebos.com"
        ;;
    # 其他地域...
    *)
        echo "错误: 不支持的地域 $region"
        exit 1
        ;;
    esac
    echo $endpoint
}
