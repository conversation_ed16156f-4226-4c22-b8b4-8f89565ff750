#!/bin/bash

REDHAT=1
UBUNTU2004=2
UBUNTU2204=3
UBUNTU1804=4

BASE_INSTALL_DIR="/home/<USER>"
MODULE_HOME="/home/<USER>/share/modules/modulefiles"

# 判断当前操作系统为centos或ubuntu
function check_env() {
    if [ -f "/etc/redhat-release" ]; then
        echo $REDHAT
    elif [ -f "/etc/lsb-release" ]; then
        source /etc/lsb-release
        if [ "$DISTRIB_RELEASE" == "20.04" ]; then
            echo $UBUNTU2004
        elif [ "$DISTRIB_RELEASE" == "22.04" ]; then
            echo $UBUNTU2204
        elif [ "$DISTRIB_RELEASE" == "18.04" ]; then
            echo $UBUNTU1804
        fi
    else
        echo 0
    fi
}

function get_default_module_home() {
    ret_code=$(check_env)
    if [[ $ret_code == $REDHAT ]]; then
        echo "/usr/share/Modules/modulefiles"
    else
        echo "/usr/share/modules/modulefiles"
    fi
}
