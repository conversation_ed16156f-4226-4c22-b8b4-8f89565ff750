#!/bin/bash
set -x

BASE_INSTALL_DIR="/opt"
CMAKE_VERSION="3.26.4"
CMAKE_DIR="$BASE_INSTALL_DIR/cmake/$CMAKE_VERSION"
SET_DEFAULT=0

CUR_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

source check_env.sh
source get_bos_endpoint.sh

function install_cmake() {
    region=$2
    echo $region
    endpoint=$(get_bos_endpoint $region)
    wget $endpoint/release/pkg/amd64/pkg/cmake-$CMAKE_VERSION-linux-x86_64.tar.gz
    tar -xf cmake-$CMAKE_VERSION-linux-x86_64.tar.gz

    # 安装目录 $CMAKE_DIR
    mkdir -p $CMAKE_DIR
    cp -rf cmake-$CMAKE_VERSION-linux-x86_64/* $CMAKE_DIR/
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "cp cmake files failed"
        return 1
    fi

    # 安装 modulefile
    MODULEFILE_DIR="$(get_default_module_home)/cmake"
    mkdir -p $MODULEFILE_DIR
    bash $CUR_DIR/build_cmake_modulefile.sh "$CMAKE_DIR" >$MODULEFILE_DIR/$CMAKE_VERSION

    if [[ $SET_DEFAULT > 0 ]]; then
        echo -e "#%Module\n\nset ModulesVersion $CMAKE_VERSION" >$MODULEFILE_DIR/.version
    fi

    return 0
}

install_cmake $@
