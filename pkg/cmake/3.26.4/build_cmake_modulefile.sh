#! /bin/bash

#define parameters which are passed in.
HOME=$1

#define the template.
cat  << EOF
#%Module######################################################################
##
##      cmake software modulefile
##
proc ModulesHelp { } {
        puts stderr "This is a cmake compiler"
}

set root $HOME
setenv CMAKE_HOME \$root

prepend-path PATH \$root/bin
prepend-path DOCPATH \$root/doc
prepend-path MANPATH \$root/man
EOF