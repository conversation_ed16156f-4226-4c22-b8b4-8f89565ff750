#!/bin/bash
# This is mainly to install/uninstall btune agent
# @Filename: control
# @Author:   liuxingxing (<EMAIL>)
# @Update:     2023/04/07 10:17

BTUNE_DIR="/opt/btune"
SELF_DIR=$(cd $(dirname $0);pwd)

AGILE_RELEASE_PRODUCT_HTTP_URL="http://download.bcm.baidubce.com/btune-agent/bTuneAgent.tar.gz"

ARCH=`arch`

function run_env_init()
{
  # try install perf
  if [[ ! -f "/usr/bin/perf" ]];then
    yum -y install perf || apt -y install perf
  fi
}

function install()
{
  # run env init
  run_env_init
  if [[ $? -ne 0 ]];then
    echo " run env init failed."
  fi

  # package env init
  mkdir -p ${BTUNE_DIR}
  pushd ${BTUNE_DIR} &>/dev/null
  if [[ -d btune-agent ]]; then
    echo "btune-agent exited already."
    return 2
  fi
  rm -rf output*
  wget -t 12 -T 120 -O ${BTUNE_DIR}/output.tar.gz ${AGILE_RELEASE_PRODUCT_HTTP_URL}
  if [[ $? -ne 0 ]];then
    echo " agent download failed."
    return 3
  fi
  tar -zxvf ${BTUNE_DIR}/output.tar.gz
  ls output/btune-agent.tgz
  if [[ $? -ne 0 ]];then
    if [[ "$ARCH" == "aarch64" ]]; then
      mv output/aarch64_build btune-agent
    else
      mv output/x86_64_build btune-agent
    fi
  else
    mv output btune-agent
  fi
  pushd btune-agent &>/dev/null
  tar -zxvf btune-agent.tgz
  rm -rf btune-agent.tgz
  popd &>/dev/null
  rm -rf output*
  popd &>/dev/null

  # start agent
  bash $BTUNE_DIR/btune-agent/control start
  if [[ $? -ne 0 ]];then
    echo " agent start failed."
    return 4
  fi

  return 0
}

function uninstall()
{
  # stop agent
  if [[ -f $BTUNE_DIR/btune-agent/control ]]; then
    bash $BTUNE_DIR/btune-agent/control stop
    if [[ $? -ne 0 ]];then
      echo " agent stop failed."
      return 1
    fi
  fi

  # clean package env
  rm -rf $BTUNE_DIR/btune-agent
  if [[ "X$(ls $BTUNE_DIR)" == "X" ]];then
    rm -rf $BTUNE_DIR
  fi
}

if [[ $# != 1 ]];then
  echo "$0 install|uninstall|start|stop|restart|status|keepalive"
else
  if [[ $1 == "restart" ]];then
    bash $BTUNE_DIR/btune-agent/control stop
    if [[ $? -eq 0 ]];then
      bash $BTUNE_DIR/btune-agent/control start
    fi
  elif [[ $1 == "stop" ]];then
    bash $BTUNE_DIR/btune-agent/control stop
  elif [[ $1 == "start" ]];then
    bash $BTUNE_DIR/btune-agent/control start
  elif [[ $1 == "status" ]];then
    bash $BTUNE_DIR/btune-agent/control status
  elif [[ $1 == "keepalive" ]];then
    bash $BTUNE_DIR/btune-agent/control keepalive
  elif [[ $1 == "install" ]];then
    uninstall
    install
  elif [[ $1 == "uninstall" ]];then
    uninstall  
  else
    echo "$0 install|uninstall|start|stop|restart|status|keepalive"
  fi
fi
