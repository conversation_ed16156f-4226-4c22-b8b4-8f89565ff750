#!/bin/bash
set -x

source check_env.sh
source get_bos_endpoint.sh

function install() {

    TARGET_FILE="/opt/btune"
    SYMLINK="/home/<USER>/btune"
    mkdir -p $TARGET_FILE

    if [ ! -d "/home/<USER>" ]; then
        sudo -u work bash -c "cd /home/<USER>"
    fi

    if [ ! -e "$SYMLINK" ]; then
        ln -s "$TARGET_FILE" "$SYMLINK"
        echo "软链接创建成功: $SYMLINK -> $TARGET_FILE"
    else
        echo "软链接已存在: $SYMLINK"
    fi

    bash btune_install.sh install
}

install $@
