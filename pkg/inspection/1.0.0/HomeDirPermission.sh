#!/bin/bash

source output.sh
ItemType="StorageStatus"
ItemName="HomeDirPermission"

# 获取home目录的路径
HOME_DIR="/home"

# 使用ls -l命令获取权限信息
PERMISSIONS=$(ls -ld "$HOME_DIR" | awk '{print $1}')

# 检查是否可读可写（对于所有者）
OWNER_READ_WRITE=$(echo "$PERMISSIONS" | grep -q 'rw' && echo "true" || echo "false")

if [ "$OWNER_READ_WRITE" = "false" ]; then
    output_metrics "$ItemType" "$ItemName" "FAILED" "Home 目录的所有者没有可读可写的权限，请检查。" ""
    exit
fi

output_metrics "$ItemType" "$ItemName" "NORMAL" "" ""
