#!/bin/bash

source output.sh
ItemType="StorageStatus"
ItemName="MountPointStatus"

softwareDir=$1
sharedStorageList=$2

if [ -z "$sharedStorageList" ]; then
    output_metrics "$ItemType" "$ItemName" "FAILED" "args sharedStorageList is empty." ""
    exit
fi
if [ -z "$softwareDir" ]; then
    output_metrics "$ItemType" "$ItemName" "FAILED" "args softwareDir is empty." ""
    exit
fi

function check_mount() {
    mountTarget=$1
    mountDir=$2
    if mountpoint -q "$mountDir"; then
        actualMountTarget=$(findmnt -n -o SOURCE --target "$mountDir")
        if echo "$actualMountTarget" | grep -q "$mountTarget"; then
            # echo "The directory $mountDir is correctly mounted."
            :
        else
            output_metrics "$ItemType" "$ItemName" "ABNORMAL" "The directory $mountDir is mounted, but the source is $actualMountTarget instead of $mountTarget." "影响集群软件的安装、运行，以及用户数据的存储。"
            exit
        fi
    else
        output_metrics "$ItemType" "$ItemName" "ABNORMAL" "The directory $mountDir is not mounted." "影响集群软件的安装、运行，以及用户数据的存储。"
        exit
    fi
}

echo $sharedStorageList | tr -d '\\' | jq -r '.[] | "\(.storage_protocol)\t\(.mount_target)\t\(.mount_dir)\t\(.mount_option | rtrimstr("\""))"' | while IFS=$'\t' read -r protocol target dir option; do

    check_mount $target $dir
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        output_metrics "$ItemType" "$ItemName" "ABNORMAL" "$target:$dir is not mounted." "影响集群软件的安装、运行，以及用户数据的存储。"
        echo "mount $mount_dir cfs failed"
        exit
    fi
done

if [ ! -d "$softwareDir" ]; then
    output_metrics "$ItemType" "$ItemName" "ABNORMAL" "The directory $softwareDir is not existed." "影响集群软件的安装、运行，以及用户数据的存储。"
    exit
fi

output_metrics "$ItemType" "$ItemName" "NORMAL" "" ""
