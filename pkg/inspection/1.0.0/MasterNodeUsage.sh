#!/bin/bash

source output.sh
ItemType="NodeResource"
ItemName="MasterNodeUsage"

# CPU 使用率检测
cpu_usage() {
    # 使用 mpstat 或其他工具获取 CPU 使用率，这里以 mpstat 为例
    # 如果没有安装 mpstat，可以通过 sysstat 包安装
    cpu_idle=$(mpstat 1 1 | awk '$12 ~ /[0-9.]+/ { print 100 - $12 }')
    cpu_used=$(echo "100 - $cpu_idle" | bc)
    echo $cpu_used
}

# 内存使用率检测
mem_usage() {
    # 使用 free 命令获取内存使用率
    mem_used=$(free | awk '/^Mem:/ {print $3/$2 * 100.0}')
    echo $mem_used
}

# 磁盘使用率检测
disk_usage() {
    # 使用 df 命令获取根文件系统的磁盘使用率
    disk_used=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    echo $disk_used
}

# 获取各资源使用率
cpu=$(cpu_usage)
mem=$(mem_usage)
disk=$(disk_usage)

# 设置阈值
threshold=$1

# 如果 threshold 未设置，则默认为 80%
if [ -z "$threshold" ]; then
    threshold=80
fi

# 检查并退出
if (($(echo "$cpu > $threshold" | bc -l))); then
    output_metrics "$ItemType" "$ItemName" "WARNING" "CPU 使用率超过 $threshold%" "影响集群的稳定性和调度功能的使用。"
    exit
fi

if (($(echo "$mem > $threshold" | bc -l))); then
    output_metrics "$ItemType" "$ItemName" "WARNING" "内存使用率超过 $threshold%" "影响集群的稳定性和调度功能的使用。"
    exit
fi

if (($(echo "$disk > $threshold" | bc -l))); then
    output_metrics "$ItemType" "$ItemName" "WARNING" "磁盘使用率超过 $threshold%" "影响集群的稳定性和调度功能的使用。"
    exit
fi

output_metrics "$ItemType" "$ItemName" "NORMAL" "" ""
