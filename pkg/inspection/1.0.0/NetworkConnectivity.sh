#!/bin/bash

source output.sh
ItemType="NetworkStatus"
ItemName="NetworkConnectivity"

if [ "$#" -lt 1 ]; then
    output_metrics "$ItemType" "$ItemName" "FAILED" "args is empty." ""
    exit
fi

args=("$@")

for arg in "${args[@]}"; do
    # 查看是否能 ping 通
    ping -c 1 $arg >/dev/null 2>&1
    if [ $? -eq 0 ]; then
        :
    else
        output_metrics "$ItemType" "$ItemName" "ABNORMAL" "connect to $arg failed." "影响监控数据的采集和集群稳定性。"
        exit
    fi
    # 查看是否内网解析
    real_domain="$arg"
    while true; do
        # 查询域名的CNAME记录
        cname=$(dig +short "$real_domain" CNAME)
        # 如果没有CNAME记录，则跳出循环
        if [ -z "$cname" ]; then
            break
        fi
        # 更新实际解析的域名
        real_domain="$cname"
    done
    # 判断域名中是否有 shifen
    if echo "$real_domain" | grep -q "shifen"; then
        output_metrics "$ItemType" "$ItemName" "ABNORMAL" "非内网解析" "影响监控数据的采集和集群稳定性。"
        exit
    fi
done

output_metrics "$ItemType" "$ItemName" "NORMAL" "" ""
