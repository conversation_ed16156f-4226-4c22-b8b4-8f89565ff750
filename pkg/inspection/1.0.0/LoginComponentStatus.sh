#!/bin/bash

source output.sh
ItemType="ComponentStatus"
ItemName="LoginComponentStatus"

masterIp=$1
masterHost=$2
scheduler=$3

if [ -z "$masterHost" ]; then
    output_metrics "$ItemType" "$ItemName" "FAILED" "args masterHost is empty." ""
    exit
fi

if [ -z "$masterIp" ]; then
    output_metrics "$ItemType" "$ItemName" "FAILED" "args masterIp is empty." ""
    exit
fi

if [ -z "$scheduler" ]; then
    output_metrics "$ItemType" "$ItemName" "FAILED" "args scheduler is empty." ""
    exit
fi

# 检查调度器服务状态
server_status=$(systemctl status chpc-app-server | grep Active | awk '{print $3}' | cut -d "(" -f2 | cut -d ")" -f1)
if [ "$server_status" != "running" ]; then
    output_metrics "$ItemType" "$ItemName" "ABNORMAL" "chpc-app-server is not running." "影响 CHPC 客户端的正常使用。"
    exit
fi

# 检查域账号服务
ldap_status=$(systemctl status nslcd | grep Active | awk '{print $3}' | cut -d "(" -f2 | cut -d ")" -f1)
if [ "$ldap_status" != "running" ]; then
    output_metrics "$ItemType" "$ItemName" "ABNORMAL" "ladp-server is not running." "影响集群用户配置功能的正常使用。"
    exit
fi

# 检查是否能连接到调度器并执行查询操作
timeout 3 ping -c 1 $masterHost >/dev/null 2>&1
if [ $? -eq 0 ]; then
    # echo "connect to $masterHost success"
    :
else
    output_metrics "$ItemType" "$ItemName" "ABNORMAL" "connect to $masterHost failed, scheduler is not available." "影响集群调度功能的正常使用。"
    exit
fi

if [ "$scheduler" = "pbs" ]; then
    timeout 3 /opt/pbs/bin/qmgr -c "list server"
    if [[ $? -ne 0 ]]; then
        output_metrics "$ItemType" "$ItemName" "ABNORMAL" "pbs qmgr command failed." "影响集群调度功能的正常使用。"
        exit
    fi

    if [ "$scheduler" = "pbs" ]; then
        # if grep -qF -- "PBS_START_MOM=1" /etc/pbs.conf; then
        operators=$(/opt/pbs/bin/qmgr -c "list server")
        if [[ $? -ne 0 ]]; then
            output_metrics "$ItemType" "$ItemName" "ABNORMAL" "pbs qmgr command failed." "影响集群调度功能的正常使用。"
            exit
        fi
        if echo $operators | grep -q "root@$(hostname)"; then
            # echo "proxy node is the manager of scheduler."
            :
        else
            output_metrics "$ItemType" "$ItemName" "ABNORMAL" "login node is not the operator of scheduler." "影响集群调度功能的正常使用。"
            exit
        fi
    fi

fi
if [ "$scheduler" = "slurm" ]; then
    timeout 3 /opt/slurm/22.05.9/bin/scontrol show node > /dev/null 2>&1
    if [[ $? -ne 0 ]]; then
        output_metrics "$ItemType" "$ItemName" "ABNORMAL" "slurm scontrol command failed." "影响集群调度功能的正常使用。"
        exit
    fi
fi

output_metrics "$ItemType" "$ItemName" "NORMAL" "" ""
