#!/bin/bash

source output.sh
ItemType="FileExist"
ItemName="SchedulerPluginExist"

scheduler=$1
schedulerVersion=$2
pluginVersion=$3

if [ -z "$scheduler" ]; then
    output_metrics "$ItemType" "$ItemName" "FAILED" "args scheduler is empty." ""
    exit
fi

if [ -z "$schedulerVersion" ]; then
    output_metrics "$ItemType" "$ItemName" "FAILED" "args schedulerVersion is empty." ""
    echo "args schedulerVersion is empty."
    exit
fi

if [ -z "$pluginVersion" ]; then
    output_metrics "$ItemType" "$ItemName" "FAILED" "args pluginVersion is empty." ""
    exit
fi

folder_path="/plugin/chpc/$scheduler/$schedulerVersion/$pluginVersion"

# 检查文件夹是否存在
if [ ! -d "$folder_path" ]; then
    output_metrics "$ItemType" "$ItemName" "ABNORMAL" "文件夹 $folder_path 不存在。" "影响集群的正常使用。"
    exit
fi

# 检查文件夹下是否有 Python 脚本
python_script_found=0
for file in "$folder_path"/*; do
    if [ -f "$file" ] && [[ "$file" == *.py ]]; then
        python_script_found=1
        break
    fi
done

# 如果没有找到 Python 脚本，退出脚本
if [ $python_script_found -eq 0 ]; then
    output_metrics "$ItemType" "$ItemName" "ABNORMAL" "文件夹 $folder_path 下没有 Python 脚本。" "影响集群的正常使用。"
    exit
fi

cmd="python3 $folder_path/queue_list.py --cal"

$cmd > /dev/null 2>&1
if [ $? -ne 0 ]; then
    output_metrics "$ItemType" "$ItemName" "ABNORMAL" "脚本测试执行失败。" "影响集群的正常使用。"
    exit
fi

output_metrics "$ItemType" "$ItemName" "NORMAL" "" ""
