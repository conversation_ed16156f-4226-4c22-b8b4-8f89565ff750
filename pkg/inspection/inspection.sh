#!/bin/bash
set -x

action=""
cluster_id=""
cluster_name=""
scheduler=""
scheduler_version=""
plugin_version=""
user_id=""
region=""
master_ip=""
master_host=""
login_host=""
login_ip=""
mount_target=""
mount_dir=""
software_dir=""
shared_storage_list=""
mount_option=""
node_type=""
cluster_type=""
enable_ha=""
enable_monitor=""
bos_endpoint=""
master_arch=""
ssh_port=""
max_nodes=""
max_cpus=""
task_id=""
inspection_funcs=""
check_network_url=""

source /etc/profile

declare -A func_to_node_type
declare -A func_to_args

query_func() {
    local func=$1
    local entry=()

    # 遍历信息数组
    for item in "${info_array[@]}"; do
        # 每个 item 是一个包含三个字段的子数组
        local current_func=${item[0]}

        # 检查 func 是否匹配
        if [ "$current_func" == "$func" ]; then
            entry=("${item[@]}")
            break
        fi
    done

    # 返回匹配的 entry 数组或空数组
    echo "${entry[@]}"
}

# 解析shell参数
function parse_args() {
    args=$(getopt -o h:o:t: -l action:,clusterId:,clusterName:,scheduler:,schedulerVersion:,pluginVersion:,userId:,region:,masterIp:,masterHost:,loginHost::,mountTarget:,mountDir:,softwareDir:,mountOption:,sharedStorageList:,nodeType:,clusterType:,enableHa:,enableMonitor:,bosEndpoint:,masterArch:,sshPort:,maxNodes:,maxCpus:,taskId:,inspectionFuncs:,checkNetworkUrl: -- "$@")
    if [ $? != 0 ]; then
        echo "Parse error! Terminating..." >&2
        exit 1
    fi
    echo $args
    echo ""
    eval set -- "$args"
    while true; do
        case "$1" in
        --action)
            action="$2"
            shift 2
            ;;
        --clusterId)
            cluster_id="$2"
            shift 2
            ;;
        --clusterName)
            cluster_name="$2"
            shift 2
            ;;
        --scheduler)
            scheduler="$2"
            # 如果 scheduler 为 openpbs，转换为 pbs
            if [[ $scheduler == "openpbs" ]]; then
                scheduler="pbs"
            fi
            shift 2
            ;;
        --schedulerVersion)
            scheduler_version="$2"
            shift 2
            ;;
        --pluginVersion)
            plugin_version="$2"
            shift 2
            ;;
        --userId)
            user_id="$2"
            shift 2
            ;;
        --region)
            region="$2"
            shift 2
            ;;
        --downloadScripts)
            download_scripts="$2"
            shift 2
            ;;
        --masterIp)
            master_ip="$2"
            shift 2
            ;;
        --masterHost)
            master_host="$2"
            shift 2
            ;;
        --loginHost)
            login_host="$2"
            shift 2
            ;;
        --mountTarget)
            mount_target="$2"
            shift 2
            ;;
        --mountDir)
            mount_dir="$2"
            shift 2
            ;;
        --softwareDir)
            software_dir="$2"
            shift 2
            ;;
        --sharedStorageList)
            shared_storage_list="$2"
            shift 2
            ;;
        --mountOption)
            # mount_option="$2"
            shift
            ;;
        --nodeType)
            node_type="$2"
            shift 2
            ;;
        --clusterType)
            cluster_type="$2"
            shift 2
            ;;
        --enableHa)
            enable_ha="$2"
            shift 2
            ;;
        --enableMonitor)
            enable_monitor="$2"
            shift 2
            ;;
        --bosEndpoint)
            bos_endpoint="$2"
            shift 2
            ;;
        --masterArch)
            master_arch="$2"
            shift 2
            ;;
        --sshPort)
            ssh_port="$2"
            shift 2
            ;;
        --maxNodes)
            max_nodes="$2"
            shift 2
            ;;
        --maxCpus)
            max_cpus="$2"
            shift 2
            ;;
        --taskId)
            task_id="$2"
            shift 2
            ;;
        --inspectionFuncs)
            inspection_funcs="$2"
            shift 2
            ;;
        --checkNetworkUrl)
            check_network_url="$2"
            shift 2
            ;;
        -h)
            help
            exit 0
            ;;
        --)
            shift
            break
            ;;
        *)
            echo "Parameters error!!!$1"
            exit 1
            ;;
        esac
    done
}

function main() {
    # 将当前脚本拷贝到 /opt/chpc/scripts 目录
    mkdir -p /opt/chpc/scripts
    cp -rf *.sh /opt/chpc/scripts

    echo "exec_func: $action"

    # *1. 下载巡检脚本
    mkdir -p /opt/chpc/download && rm -rf /opt/chpc/download/$inspection_file* && wget -q $bos_endpoint/release/pkg/$master_arch/${inspection_file}_${master_arch}.tar.gz -O /opt/chpc/download/${inspection_file}_${master_arch}.tar.gz && tar -zxvf /opt/chpc/download/${inspection_file}_${master_arch}.tar.gz -C /opt/chpc/download
    script_folder=/opt/chpc/download/${inspection_file}_${master_arch}
    chown root:root -R $script_folder

    # *2. 获取当前节点类型和时间戳（分钟级别）
    current_minute_timestamp=$(date +%Y%m%d%H%M)
    output_path="/home/<USER>/$task_id/$current_minute_timestamp"
    if [ ! -d "$output_path" ]; then
        mkdir -p "$output_path"
    fi

    current_hostname=$(hostname)
    cur_node_type=""
    if [[ $current_hostname = $master_host ]]; then
        cur_node_type="master"
    elif [[ $current_hostname = $login_host ]]; then
        cur_node_type="login"
    fi

    # *3. 遍历巡检脚本
    for func in "${!func_to_node_type[@]}"; do
        local target_node_type=${func_to_node_type[$func]}
        local args=${func_to_args[$func]}

        # *3.1 如果符合节点类型，则执行
        # 如果 target_node_type 包含 cur_node_type，则执行
        if [[ $target_node_type =~ (^$cur_node_type$|,$cur_node_type,|,$cur_node_type$) ]]; then
            # *3.2 执行结果输出
            cmd=$(echo "bash ${func}.sh ${args}")
            pushd $script_folder
            # result=$(eval $cmd)
            # echo "$result" >$output_path/${func}.result
            eval $cmd >$output_path/${func}.result
            popd
        fi

    done
}

parse_args $@

# *脚本版本以及脚本配置，可以调整
inspection_file="inspection_1.0.0"
func_to_node_type[NetworkConnectivity]="master"
func_to_node_type[MountPointStatus]="master"
func_to_node_type[HomeDirPermission]="master"
func_to_node_type[SchedulerPluginExist]="master"
func_to_node_type[SchedulerStatus]="master"
func_to_node_type[LoginComponentStatus]="login"
func_to_node_type[MasterNodeUsage]="master"

func_to_args[NetworkConnectivity]="${check_network_url}"
func_to_args[MountPointStatus]="${software_dir} ${shared_storage_list}"
func_to_args[HomeDirPermission]=""
func_to_args[SchedulerPluginExist]="${scheduler} ${scheduler_version} ${plugin_version}"
func_to_args[SchedulerStatus]="${master_ip} ${master_host} ${scheduler} ${cluster_type}"
func_to_args[LoginComponentStatus]="${master_ip} ${master_host} ${scheduler}"
func_to_args[MasterNodeUsage]=""

# *执行脚本
main
