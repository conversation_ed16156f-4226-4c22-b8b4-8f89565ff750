#!/bin/bash
set -x

source check_env.sh

function uninstall_module() {
    ret_code=$(check_env)
    if [[ $ret_code == $REDHAT ]]; then
        yum remove -y environment-modules
    else
        if [[ $ret_code == $UBUNTU2204 ]]; then
            # 禁止弹出交互窗口，默认不重启服务，仅列出可选项
            sed -i "s/\#\$nrconf{restart} = 'i'/\$nrconf{restart} = 'l'/" /etc/needrestart/needrestart.conf
        fi
        apt -y remove environment-modules
    fi

    return 0
}

uninstall_module $@