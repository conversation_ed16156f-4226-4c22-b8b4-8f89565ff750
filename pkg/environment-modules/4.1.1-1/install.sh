#!/bin/bash
set -x

source check_env.sh
source get_bos_endpoint.sh

function install_module() {
    region=$2
    echo $region
    endpoint=$(get_bos_endpoint $region)

    # 如果设置了软件安装目录，则使用设置的目录，否则使用默认目录
    if [[ ! -z $3 ]]; then
        BASE_INSTALL_DIR=$3
        MODULE_HOME=$BASE_INSTALL_DIR/share/modules/modulefiles
    fi

    ret_code=$(check_env)
    if [[ $ret_code == $REDHAT ]]; then
        # yum install -y environment-modules
        debFile=environment-modules-centos.tar.gz
        wget $endpoint/release/pkg/amd64/pkg/$debFile
        mkdir -p environment-modules
        tar -zxvf $debFile -C environment-modules
        rpm -ivh environment-modules/*.rpm

        ret_code=$?
        if [[ $ret_code -ne 0 ]]; then
            echo "install environment-modules failed, try again"
            yum install -y environment-modules
        fi
        ret_code=$?
        if [[ $ret_code -ne 0 ]]; then
            echo "install environment-modules failed"
            return 1
        fi

        mkdir -p $MODULE_HOME
        match_num=$(grep -c "^$MODULE_HOME" /usr/share/Modules/init/.modulespath)
        if [ $match_num == 0 ]; then
            echo $MODULE_HOME >>/usr/share/Modules/init/.modulespath
        fi
        if cat /etc/redhat-release | grep -q "8.4"; then
            source /etc/profile
            echo export MODULEPATH=$MODULE_HOME:'$MODULEPATH' >>/etc/bash.bashrc
            echo export MODULEPATH=$MODULE_HOME:'$MODULEPATH' >>/etc/profile
        fi

        echo 'source /usr/share/Modules/init/bash' >>/etc/bash.bashrc
        echo 'source /usr/share/Modules/init/bash' >>/etc/profile
    else
        UBUNTU=18
        if [[ $ret_code == $UBUNTU2204 ]]; then
            # 禁止弹出交互窗口，默认不重启服务，仅列出可选项
            sed -i "s/\#\$nrconf{restart} = 'i'/\$nrconf{restart} = 'l'/" /etc/needrestart/needrestart.conf
            UBUNTU=22
        elif [[ $ret_code == $UBUNTU2004 ]]; then
            UBUNTU=20
        fi

        # apt update
        # apt -y install environment-modules
        debFile=environment-modules-ubuntu-$UBUNTU.tar.gz
        wget $endpoint/release/pkg/amd64/pkg/$debFile
        mkdir -p environment-modules
        tar -zxvf $debFile -C environment-modules
        dpkg -i environment-modules/*.deb

        ret_code=$?
        if [[ $ret_code -ne 0 ]]; then
            echo "install environment-modules failed, try again"
            apt -y install environment-modules
        fi
        ret_code=$?
        if [[ $ret_code -ne 0 ]]; then
            echo "install environment-modules failed"
            return 1
        fi

        mkdir -p $MODULE_HOME
        match_num=$(grep -c "^$MODULE_HOME" /usr/share/modules/init/.modulespath)
        if [ $match_num == 0 ]; then
            echo $MODULE_HOME >>/usr/share/modules/init/.modulespath
        fi
        echo 'source /usr/share/modules/init/bash' >>/etc/bash.bashrc
        echo 'source /usr/share/modules/init/bash' >>/etc/profile
    fi

    return 0
}

install_module $@
