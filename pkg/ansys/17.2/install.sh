#!/bin/bash
set -x

source check_env.sh
source get_bos_endpoint.sh

function install() {
    region=$2
    echo $region
    endpoint=$(get_bos_endpoint $region)

    # 如果设置了软件安装目录，则使用设置的目录，否则使用默认目录
    if [[ ! -z $3 ]]; then
        BASE_INSTALL_DIR=$3
        MODULE_HOME=$BASE_INSTALL_DIR/share/modules/modulefiles
    fi

    ret_code=$(check_env)
    if [[ $ret_code == $REDHAT ]]; then
        binFile=ansys-17.2.tar.gz
        moduleFile=ansys-17.2-module-centos.tar.gz
    else
        if [[ $ret_code == $UBUNTU2204 ]]; then
            # 禁止弹出交互窗口，默认不重启服务，仅列出可选项
            sed -i "s/\#\$nrconf{restart} = 'i'/\$nrconf{restart} = 'l'/" /etc/needrestart/needrestart.conf
        fi

        binFile=ansys-17.2.tar.gz
        moduleFile=ansys-17.2-module-ubuntu.tar.gz
    fi

    wget $endpoint/release/pkg/amd64/pkg/$binFile
    wget $endpoint/release/pkg/amd64/pkg/$moduleFile

    mkdir -p $BASE_INSTALL_DIR
    tar -zxvf $binFile -C $BASE_INSTALL_DIR
    mkdir -p $MODULE_HOME
    tar -zxvf $moduleFile -C $MODULE_HOME

    cd $MODULE_HOME
    # 将 modulefile 中的 /home/<USER>
    find . -type f -exec sed -i "s|/home/<USER>" {} +

    return 0
}

install $@
