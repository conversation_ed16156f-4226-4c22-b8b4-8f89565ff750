#! /bin/bash
set -x

source check_env.sh
source get_bos_endpoint.sh

# 脚本当前所在目录
INSTALL_CLEAN_FILE="install_clean.sh"
CURRENT_DIR=$(
    cd $(dirname $0)
    pwd
)
TEMP_DOWNLOAD_DIR="/opt/downloads"
# sge配置文件
MC_TEMPLATE_FILE="$CURRENT_DIR/conf/mc_template"
MSCONF_TEMPLATE_FILE="$CURRENT_DIR/conf/msconf_template"
ME_GLOBAL_TEMPLATE_FILE="$CURRENT_DIR/conf/me_global_template"
QUEUE_TEMPLATE_FILE="$CURRENT_DIR/conf/queue_template"
DEFAULT_QUEUE_TEMPLATE_FILE="$CURRENT_DIR/conf/default_queue_template"
# sge安装目录
SGE_INSTALL_DIR="/opt/sge"
REDHAT=1
UBUNTU2004=2
UBUNTU2204=3

action=""
cluster_id=""
cluster_name=""
scheduler=""
scheduler_version=""
plugin_version=""
user_id=""
region=""
master_ip=false
master_host=false
login_host=""
mount_target=""
mount_dir=""
software_dir=""
shared_storage_list=""
mount_option=""
node_type=""
cluster_type=""
enable_ha=""
enable_monitor=""
backup_hostnames=""
queue_list=""
ssh_port=""
max_nodes=""
max_cpus=""

# 解析shell参数
function parse_args() {
    args=$(getopt -o h:o:t: -l action:,clusterId:,clusterName:,scheduler:,schedulerVersion:,pluginVersion:,userId:,region:,masterIp:,masterHost:,loginHost::,mountTarget:,mountDir:,softwareDir:,mountOption:,sharedStorageList:,nodeType:,clusterType:,enableHa:,enableMonitor:,backupHostnames:,queueList:,sshPort:,maxNodes:,maxCpus: -- "$@")
    if [ $? != 0 ]; then
        echo "Parse error! Terminating..." >&2
        exit 1
    fi
    echo $args
    echo ""
    eval set -- "$args"
    while true; do
        case "$1" in
        --action)
            action="$2"
            shift 2
            ;;
        --clusterId)
            cluster_id="$2"
            shift 2
            ;;
        --clusterName)
            cluster_name="$2"
            shift 2
            ;;
        --scheduler)
            scheduler="$2"
            # 如果 scheduler 为 openpbs，转换为 pbs
            if [[ $scheduler == "openpbs" ]]; then
                scheduler="pbs"
            fi
            shift 2
            ;;
        --schedulerVersion)
            scheduler_version="$2"
            shift 2
            ;;
        --pluginVersion)
            plugin_version="$2"
            shift 2
            ;;
        --userId)
            user_id="$2"
            shift 2
            ;;
        --region)
            region="$2"
            shift 2
            ;;
        --downloadScripts)
            download_scripts="$2"
            shift 2
            ;;
        --masterIp)
            master_ip="$2"
            shift 2
            ;;
        --masterHost)
            master_host="$2"
            shift 2
            ;;
        --loginHost)
            login_host="$2"
            shift 2
            ;;
        --mountTarget)
            mount_target="$2"
            shift 2
            ;;
        --mountDir)
            mount_dir="$2"
            shift 2
            ;;
        --softwareDir)
            software_dir="$2"
            shift 2
            ;;
        --sharedStorageList)
            shared_storage_list="$2"
            shift 2
            ;;
        --mountOption)
            # mount_option="$2"
            echo "=========> mount_option: skip"
            shift
            ;;
        --nodeType)
            node_type="$2"
            shift 2
            ;;
        --clusterType)
            cluster_type="$2"
            shift 2
            ;;
        --enableHa)
            enable_ha="$2"
            shift 2
            ;;
        --enableMonitor)
            enable_monitor="$2"
            shift 2
            ;;
        --backupHostnames)
            backup_hostnames="$2"
            shift 2
            ;;
        --queueList)
            queue_list="$2"
            shift 2
            ;;
        --sshPort)
            ssh_port="$2"
            shift 2
            ;;
        --maxNodes)
            max_nodes="$2"
            shift 2
            ;;
        --maxCpus)
            max_cpus="$2"
            shift 2
            ;;
        -h)
            help
            exit 0
            ;;
        --)
            shift
            break
            ;;
        *)
            echo "Parameters error!!!$1"
            exit 1
            ;;
        esac
    done
}

function main() {
    # 将当前脚本拷贝到 /opt/chpc/scripts 目录
    mkdir -p /opt/chpc/scripts
    cp -rf *.sh /opt/chpc/scripts
    echo "exec_func: $action"
    if [ x"$action" == x"installNode" ]; then
        echo $scheduler_version $plugin_version $cluster_id $cluster_name $user_id $region
        # 安装nfs
        echo "[step1] install_nfs"
        bash $CURRENT_DIR/install_clean.sh before-install
        if [ $? -ne 0 ]; then
            echo "Error: install_nfs failed"
            exit 1
        fi
        # TODO install_scheduler_plugin
        # 安装sge调度器环境
        echo "[step2] install_sge $scheduler_version $region $software_dir"
        bash install_sge.sh $scheduler_version $region $software_dir
        if [ $? -ne 0 ]; then
            echo "Error: install_sge.sh $scheduler $scheduler_version $region $software_dir failed"
            exit 1
        fi
        # 安装插件
        echo "[step3] install_scheduler_plugin $scheduler $scheduler_version $plugin_version $region"
        bash install_scheduler_plugin.sh $scheduler $scheduler_version $plugin_version $region
        if [ $? -ne 0 ]; then
           echo "Error: install_scheduler_plugin.sh $scheduler $scheduler_version $plugin_version $region failed"
           exit 1
        fi

        # 安装 cluster-api
        if [ x"$login_host" != x"" ]; then
            result=`ping -c 1 $login_host | awk 'NR==1 {match($0, /([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3})/); print substr($0, RSTART, RLENGTH)}'`
            login_ip=$result
        fi
        echo "[step3] install_chpc_server $cluster_id $cluster_name $user_id $region $max_nodes $max_cpus $login_ip"
        bash install_chpc_server.sh $cluster_id $cluster_name $user_id $region $max_nodes $max_cpus $login_ip
        if [ $? -ne 0 ]; then
            echo "Error: install_chpc_server.sh $cluster_id $cluster_name $user_id $region failed"
            exit 1
        fi

        # 安装 cluster_control
        echo "[step4] install cluster_control、cluster_control.sh $region"
        bash install_cluster_control.sh $region
        if [ $? -ne 0 ]; then
            echo "Error: install_cluster_control.sh $region failed"
            exit 1
        fi

        # 如果 sharedStorageList 不为空，则挂载 nfs
        if [ x"$shared_storage_list" != x"" ]; then
            echo "[step5] mount_nfs"
            echo $cluster_id $software_dir "$shared_storage_list"
            bash mount_nfs.sh $cluster_id $software_dir "$shared_storage_list"
            if [ x"$cluster_type" == x"cloud" ]; then
                # 公有云集群挂载 nfs 错误，直接报错，混合云可以后续修复
                if [ $? -ne 0 ]; then
                    echo "Error: mount_nfs $region failed"
                    exit 1
                fi
            fi
        fi

        # 启动节点
        echo "[step6] start_node $region $enable_ha $enable_monitor $scheduler $node_type $cluster_name $cluster_id
            $master_host $login_host $backup_hostnames $shared_storage_list $queue_list $max_nodes $max_cpus"
        bash start_node.sh "$region" "$enable_ha" "$enable_monitor" "$scheduler" "$node_type" "$cluster_name" "$cluster_id" \
            "$master_host" "$login_host" "$backup_hostnames" "[]" "$queue_list" "$max_nodes" "$max_cpus"
        if [ $? -ne 0 ]; then
            echo "Error: start_node $region $enable_ha $enable_monitor $scheduler $node_type $cluster_name $cluster_id
            $master_host $login_host $backup_hostnames $shared_storage_list $queue_list $max_nodes $max_cpus failed"
            exit 1
        fi
        # 修改ssh端口
        if [ x"$ssh_port" != x"" ]; then
            echo "[step7] modify_ssh_port $ssh_port"
            bash modify_ssh_port.sh $ssh_port
            if [ $? -ne 0 ]; then
                echo "Error:modify_ssh_port $ssh_port failed"
                exit 1
            fi
        fi
    fi
}

parse_args $@
main

# 手动执行
SGE_ROOT=/opt/sge
export SGE_ROOT
