#!/bin/bash

masterIp=$1
masterHost=$2
scheduler=$3

if [ -z "$masterHost" ]; then
    echo "args masterHost is empty."
    exit 1
fi

if [ -z "$masterIp" ]; then
    echo "args masterIp is empty."
    exit 1
fi

# 检查调度器服务状态
server_status=$(systemctl status chpc-server | grep Active | awk '{print $3}' | cut -d "(" -f2 | cut -d ")" -f1)
if [ "$server_status" != "running" ]; then
    echo "start chpc-server failed"
    return 1
fi

# 检查是否能连接到调度器并执行查询操作
ping -c 1 $masterHost >/dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "connect to $masterHost success"
else
    echo "connect to $masterHost failed"
    exit 1
fi

if [ "$scheduler" = "sge" ]; then
    timeout 5 /opt/sge/bin/lx-amd64/qhost
    if [[ $? -ne 0 ]]; then
        echo "Error: sge qhost command failed."
        exit 1
    fi
    # fi
fi
