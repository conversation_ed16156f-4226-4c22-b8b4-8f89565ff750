#!/bin/bash

set -x

region=$1
enable_ha=$2
enable_monitor=$3
scheduler_type=$4
node_type=$5
cluster_name=$6
cluster_id=$7
master_host=$8
login_host=$9
backup_hostnames=${10}
shared_storage_list=${11}
queue_list=${12}
max_nodes=${13}
max_cpus=${14}

if [ "$enable_ha" != "true" ]; then
    enable_ha=false
fi

if [ "$enable_monitor" != "true" ]; then
    enable_monitor=false
fi

if [ "$backup_hostnames" == "" ]; then
    backup_hostnames="[]"
fi

# 组装json格式的参数
json_param=$(cat <<EOF
{
    "region": "$region",
    "enable_ha": $enable_ha,
    "enable_monitor": $enable_monitor,
    "scheduler_type": "$scheduler_type",
    "node_type": "$node_type",
    "cluster_name": "$cluster_name",
    "cluster_id": "$cluster_id",
    "master_hostname": "$master_host",
    "login_hostname": "$login_host",
    "backup_hostnames": $backup_hostnames,
    "shared_storage_list": $shared_storage_list,
    "queue_list": $queue_list,
    "max_nodes": $max_nodes,
    "max_cpus": $max_cpus
}
EOF
)

echo "json_param: $json_param"

# 调用cluster_control
/opt/chpc/cluster_control -m createCluster -c "$json_param"
if [ $? -ne 0 ]; then
	echo "Error: cluster_control createCluster failed."
	exit 1
fi
