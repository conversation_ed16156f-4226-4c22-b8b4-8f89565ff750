#! /bin/bash
set -xe

# 下载 cluster-api 插件包
clusterId=$1
clusterName=$2
userId=$3
region=$4
maxNodes=$5
maxCpus=$6
loginIp=$7

if [ -z "$clusterId" ]; then
	echo "args clusterId is empty."
	exit 1
fi

if [ -z "$userId" ]; then
	echo "args userId is empty."
	exit 1
fi

if [ -z "$region" ]; then
	echo "args region is empty."
	exit 1
fi

if [ -z "$clusterName" ]; then
	echo "args clusterName is empty."
	exit 1
fi

scheduler="sge"

if [ "$region" = "sandbox" ]; then
	confFile="sandbox-$scheduler.yaml"
else
	confFile="prod-$scheduler.yaml"
fi

if [ "$scheduler" = "pbs" ]; then
	schedulerConfig="scheduler_plugin"
else
	schedulerConfig=$scheduler
fi

source get_bos_endpoint.sh
bos_endpoint="$(get_bos_endpoint $region)/release/pkg/prod"

# 下载 cluster-api 和配置
echo "=====>Download chpc server."
chpcFile="api-server.tar.gz"
downloadDir=/opt/chpc/download
mkdir -p $downloadDir
dstFile=$downloadDir/$chpcFile

if [ -f $dstFile ]; then
	rm $dstFile
fi
# wget $bos_endpoint/$chpcFile -O $dstFile
wget $(get_bos_endpoint $region)/release/cluster-api/prod/cluster-api -O $downloadDir/cluster-api

# 解压到 api-server 目录
if [ -d $downloadDir/chpc ]; then
	rm -rf $downloadDir/api-server
fi
# tar -zxvf $dstFile -C $downloadDir
cp -rf api-server $downloadDir

# 根据调度器生成配置
rm -rf $downloadDir/chpc
mkdir -p $downloadDir/chpc/cluster-api/conf
mkdir -p $downloadDir/chpc/cluster-api/template

cp $downloadDir/api-server/conf/$confFile $downloadDir/chpc/cluster-api/conf/online.yaml
cp $downloadDir/api-server/service/chpc-server.service.$scheduler $downloadDir/chpc/cluster-api/template/chpc-server.service.template
chmod +x $downloadDir/cluster-api
cp $downloadDir/cluster-api $downloadDir/chpc/cluster-api

# 修改配置，clusterid、userid、endpoint
echo "=====>Start to update conf."

sed -i "s/{region}/$region/g" $downloadDir/chpc/cluster-api/template/chpc-server.service.template

sed -i "s/{userId}/$userId/g" $downloadDir/chpc/cluster-api/conf/online.yaml
# 如果 region 以 test 结尾，将 test 去除
final_region=$region
final_region=$(echo "$final_region" | sed 's/test$//')
sed -i "s/{region}/$final_region/g" $downloadDir/chpc/cluster-api/conf/online.yaml
if [ -n "$loginIp" ]; then
  cat <<EOF >> "$downloadDir/chpc/cluster-api/conf/online.yaml"
login:
  mysql:
    host: ${loginIp}
    port: 3306
    db: chpc
    user: chpc
    password: Chpc@123
EOF
fi
echo "=====>Start to install chpc-server."

# 安装到系统服务
mkdir -p /opt/chpc
rm -rf /opt/chpc/cluster-api
rm -rf /opt/chpc/cluster.conf
if [ -d /opt ]; then
	cp -r $downloadDir/chpc/* /opt/chpc
	if [ $? -ne 0 ]; then
		echo "Error: Copy chpc-server files failed."
		exit 1
	fi
	cp $downloadDir/chpc/cluster-api/template/chpc-server.service.template /lib/systemd/system/chpc-server.service
	if [ $? -ne 0 ]; then
		echo "Error: Copy chpc-server.service failed."
		exit 1
	fi
	systemctl daemon-reload
else
	echo "Error: Directory /opt create failed."
	exit 1
fi
echo "=====>Install chpc-server success."
