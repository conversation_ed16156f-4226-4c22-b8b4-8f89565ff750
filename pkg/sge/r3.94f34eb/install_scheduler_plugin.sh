#!/bin/bash
set -x

# 下载指定版本的 plugin 插件
scheduler=$1
schedulerVersion=$2
pluginVersion=$3
region=$4


if [ -z "$scheduler" ]; then
	echo "args scheduler is empty."
	exit 1
fi

if [ -z "$schedulerVersion" ]; then
	echo "args schedulerVersion is empty."
	exit 1
fi

if [ -z "$pluginVersion" ]; then
	echo "args pluginVersion is empty."
	exit 1
fi

if [ -z "$region" ]; then
	echo "args region is empty."
	exit 1
fi

source get_bos_endpoint.sh
bos_endpoint="$(get_bos_endpoint $region)/release/pkg/plugin"

echo "=====>Download scheduler plugin $scheduler $schedulerVersion $pluginVersion"

pluginFile="scheduler_plugins-$scheduler-$schedulerVersion-$pluginVersion.tar.gz"
downloadDir=/opt/chpc/download
mkdir -p $downloadDir
dstFile=$downloadDir/$pluginFile

if [ -f $dstFile ]; then
	rm $dstFile
fi
wget $bos_endpoint/$pluginFile -O $dstFile

if [ -d $downloadDir/scheduler_plugins ]; then
	rm -rf $downloadDir/scheduler_plugins
fi
tar -zxvf $dstFile -C $downloadDir

# 安转到指定目录
echo "=====>Install plugin to /plugin/chpc/$scheduler/$schedulerVersion/$pluginVersion"

dst=/plugin/chpc/$scheduler/$schedulerVersion/$pluginVersion

mkdir -p $dst
rm -rf $dst/*

if [ -d $dst ]; then
	cp $downloadDir/scheduler_plugins/scheduler_plugin.conf /plugin/chpc
	if [ $? -ne 0 ]; then
		echo "Error: Copy scheduler_plugin.conf failed."
		exit 1
	fi
	cp -r $downloadDir/scheduler_plugins/* $dst
	if [ $? -ne 0 ]; then
		echo "Error: Copy scheduler_plugin files failed."
		exit 1
	fi
else
	echo "Error: Directory $dst create failed."
	exit 1
fi

echo "=====>Install scheduler plugin $scheduler $schedulerVersion $pluginVersion success."
