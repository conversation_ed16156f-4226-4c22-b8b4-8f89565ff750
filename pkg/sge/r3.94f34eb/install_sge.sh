#! /bin/bash
set -x

source check_env.sh
source get_bos_endpoint.sh
# 脚本当前所在目录
CURRENT_DIR=$(cd $(dirname $0); pwd)
TEMP_DOWNLOAD_DIR="/opt/downloads"
# sge配置文件
MC_TEMPLATE_FILE="$CURRENT_DIR/conf/mc_template"
MSCONF_TEMPLATE_FILE="$CURRENT_DIR/conf/msconf_template"
ME_GLOBAL_TEMPLATE_FILE="$CURRENT_DIR/conf/me_global_template"
QUEUE_TEMPLATE_FILE="$CURRENT_DIR/conf/queue_template"
DEFAULT_QUEUE_TEMPLATE_FILE="$CURRENT_DIR/conf/default_queue_template"
# sge安装目录
SGE_INSTALL_DIR="/opt/sge"
REDHAT=1
UBUNTU2004=2
UBUNTU2204=3
schedulerVersion=$1
region=$2
software_dir=$3

if [ -z "$schedulerVersion" ]; then
    echo "args schedulerVersion is empty."
    exit 1
fi

if [ -z "$region" ]; then
    echo "args region is empty."
    exit 1
fi

function install_sge() {

  # 安装基础软件
  ret_code=$(check_env)
  if [[ $ret_code == $REDHAT ]]; then
      os="centos7.9"
      if cat /etc/redhat-release | grep -q "8.4"; then
          os="centos8.4"
      fi
      # 安装nfs
      yum -y install nfs-utils
      systemctl stop rpcbind
      systemctl stop nfs
      systemctl disable nfs
      systemctl disable rpcbind

      if [[ $os == "centos7.9" ]]; then
          yum install -y nc lz4-devel numactl-devel hwloc-devel libevent libevent-devel environment-modules libyaml libyaml-devel libjwt libjwt-devel http-parser http-parser-devel munge munge-devel munge-libs hdf5 hdf5-devel json-c json-c-devel python3 gcc-c++ libtool-ltdl-devel
      elif [[ $os == "centos8.4" ]]; then
          yum install -y lz4-devel numactl-devel libevent libevent-devel environment-modules libjwt libjwt-devel munge munge-libs hdf5 hdf5-devel json-c json-c-devel python3 gcc-c++ libtool-ltdl-devel readline-devel --skip-broken
          chown -R munge: /etc/munge/ /var/log/munge/ /var/lib/munge/ /var/run/munge/

      fi
      # mysql安装
#      wget $bos_endpoint/mysql-community-client-8.0.31-1.el7.x86_64.rpm
#      wget $bos_endpoint/mysql-community-common-8.0.31-1.el7.x86_64.rpm
#      wget $bos_endpoint/mysql-community-libs-compat-8.0.31-1.el7.x86_64.rpm
#      wget $bos_endpoint/mysql-community-server-8.0.31-1.el7.x86_64.rpm
#      wget $bos_endpoint/mysql-community-client-plugins-8.0.31-1.el7.x86_64.rpm
#      wget $bos_endpoint/mysql-community-icu-data-files-8.0.31-1.el7.x86_64.rpm
#      wget $bos_endpoint/mysql-community-libs-8.0.31-1.el7.x86_64.rpm
#      yum install -y $downloadDir/mysql-*.rpm
#      # centos需要执行,ubuntu不需要
#      create-munge-key -f
  elif [ $ret_code == $UBUNTU2004 ]; then
      os="ubuntu20.04"
      # 安装nfs
      apt update && apt install -y nfs-common nfs-kernel-server
      systemctl stop rpcbind
      systemctl stop nfs-server
      systemctl disable rpcbind
      systemctl disable nfs-server
      apt update && apt-get -y install netcat vim unzip git build-essential libssl-dev libmysql++-dev libnuma-dev environment-modules cmake libpmi-pmix-dev hwloc libevent-dev libhwloc-dev libjwt-dev libpmix2 libpmix-dev libmunge-dev libjson-c-dev libhttp-parser-dev libyaml-dev libhdf5-dev munge
      # mysql安装
#      apt -yq install mysql-server
  elif [ $ret_code == $UBUNTU2204 ]; then
      os="ubuntu22.04"
      # 禁止弹出交互窗口，默认不重启服务，仅列出可选项
      sed -i "s/\#\$nrconf{restart} = 'i'/\$nrconf{restart} = 'l'/" /etc/needrestart/needrestart.conf
      # 安装nfs
      apt update && sudo DEBIAN_FRONTEND=noninteractive apt install -y netcat nfs-common nfs-kernel-server
      systemctl stop rpcbind
      systemctl stop nfs-server
      systemctl disable rpcbind
      systemctl disable nfs-server
      apt update && apt-get -y install git build-essential libssl-dev libmysql++-dev libnuma-dev environment-modules cmake hwloc libevent-dev libhwloc-dev libjwt-dev libpmix2 libpmix-dev libmunge-dev libjson-c-dev libhttp-parser-dev libyaml-dev libhdf5-dev munge
      # {slurm_base}/lib/slurm/cgroup_v2.so need libdbus-1-dev
      apt-get -y install libdbus-1-de
      # mysql安装
#      apt -yq install mysql-server
  else
      echo "unexpected os,only support centos or ubuntu,exit!!!"
      return 1
  fi

  # 将当前脚本拷贝到 /opt/chpc/scripts 目录
  mkdir -p /opt/chpc/scripts
  cp -rf *.sh /opt/chpc/scripts
  if [ ! -d $TEMP_DOWNLOAD_DIR ]; then
    mkdir -p $TEMP_DOWNLOAD_DIR
  fi
  endpoint=$(get_bos_endpoint $region)
  # TODO 下载调度器插件
  ret_code=$(check_env)
  if [[ $ret_code == $REDHAT ]]; then
    # centos7 安装
    sudo yum install nc -y
    wget $endpoint/release/pkg/amd64/pkg/sge-$schedulerVersion-centos79.tar.gz -O /opt/sge-$schedulerVersion-centos79.tar.gz
    tar -zxvf /opt/sge-$schedulerVersion-centos79.tar.gz -C /opt
    rm -rf /opt/sge-$schedulerVersion-centos79.tar.gz
    wget $endpoint/release/pkg/amd64/pkg/sge-$schedulerVersion-hwloc-2.8.0-centos79.tar.gz -O /usr/local/bin/sge-$schedulerVersion-hwloc-2.8.0-centos79.tar.gz
    tar -zxvf /usr/local/bin/sge-$schedulerVersion-hwloc-2.8.0-centos79.tar.gz -C /usr/local/bin
    rm -rf /usr/local/bin/sge-$schedulerVersion-hwloc-2.8.0-centos79.tar.gz
    wget $endpoint/release/pkg/amd64/pkg/sge-$schedulerVersion-libhwloc-2.8.0-centos79.tar.gz -O /usr/local/lib/sge-$schedulerVersion-libhwloc-2.8.0-centos79.tar.gz
    tar -zxvf /usr/local/lib/sge-$schedulerVersion-libhwloc-2.8.0-centos79.tar.gz -C /usr/local/lib
    rm -rf /usr/local/lib/sge-$schedulerVersion-libhwloc-2.8.0-centos79.tar.gz

  elif [[ $ret_code == $UBUNTU2204 ]]; then
    wget $endpoint/release/pkg/amd64/pkg/sge-$schedulerVersion-ubuntu22.04.tar.gz -O /opt/sge-$schedulerVersion-ubuntu22.04.tar.gz
    tar -zxvf /opt/sge-$schedulerVersion-ubuntu22.04.tar.gz -C /opt
    rm -rf /opt/sge-$schedulerVersion-ubuntu22.04.tar.gz
    wget $endpoint/release/pkg/amd64/pkg/sge-$schedulerVersion-hwloc-2.8.0-ubuntu22.04.tar.gz -O /usr/local/bin/sge-$schedulerVersion-hwloc-2.8.0-ubuntu22.04.tar.gz
    tar -zxvf /usr/local/bin/sge-$schedulerVersion-hwloc-2.8.0-ubuntu22.04.tar.gz -C /usr/local/bin
    rm -rf /usr/local/bin/sge-$schedulerVersion-hwloc-2.8.0-ubuntu22.04.tar.gz
    wget $endpoint/release/pkg/amd64/pkg/sge-$schedulerVersion-libhwloc-2.8.0-ubuntu22.04.tar.gz -O /usr/local/lib/sge-$schedulerVersion-libhwloc-2.8.0-ubuntu22.04.tar.gz
    tar -zxvf /usr/local/lib/sge-$schedulerVersion-libhwloc-2.8.0-ubuntu22.04.tar.gz -C /usr/local/lib
    rm -rf /usr/local/lib/sge-$schedulerVersion-libhwloc-2.8.0-ubuntu22.04.tar.gz

  elif [[ $ret_code == $UBUNTU2004 ]]; then
    wget $endpoint/release/pkg/amd64/pkg/sge-$schedulerVersion-ubuntu20.04.tar.gz -O /opt/sge-$schedulerVersion-ubuntu20.04.tar.gz
    tar -zxvf /opt/sge-$schedulerVersion-ubuntu20.04.tar.gz -C /opt
    rm -rf /opt/sge-$schedulerVersion-ubuntu20.04.tar.gz
    wget $endpoint/release/pkg/amd64/pkg/sge-$schedulerVersion-hwloc-2.8.0-ubuntu20.04.tar.gz -O /usr/local/bin/sge-$schedulerVersion-hwloc-2.8.0-ubuntu20.04.tar.gz
    tar -zxvf /usr/local/bin/sge-$schedulerVersion-hwloc-2.8.0-ubuntu20.04.tar.gz -C /usr/local/bin
    rm -rf /usr/local/bin/sge-$schedulerVersion-hwloc-2.8.0-ubuntu20.04.tar.gz
    wget $endpoint/release/pkg/amd64/pkg/sge-$schedulerVersion-libhwloc-2.8.0-ubuntu20.04.tar.gz -O /usr/local/lib/sge-$schedulerVersion-libhwloc-2.8.0-ubuntu20.04.tar.gz
    tar -zxvf /usr/local/lib/sge-$schedulerVersion-libhwloc-2.8.0-ubuntu20.04.tar.gz -C /usr/local/lib
    rm -rf /usr/local/lib/sge-$schedulerVersion-libhwloc-2.8.0-ubuntu20.04.tar.gz
  elif [[ $ret_code == 0 ]]; then
    echo "unexpected os,exit!!!"
    return 1
  fi

  # 拷贝配置文件
  mkdir -p $SGE_INSTALL_DIR/templates
  cp $CURRENT_DIR/conf/*template $SGE_INSTALL_DIR/templates/

  # 设置开机自启动项
  cp $SGE_INSTALL_DIR/util/rctemplates/systemd/sgemaster.service /usr/lib/systemd/system/
  cp $SGE_INSTALL_DIR/util/rctemplates/systemd/sgeexecd.service /usr/lib/systemd/system/
  echo "PATH=$SGE_INSTALL_DIR/bin/lx-amd64:\$PATH" >>/etc/profile
  echo "source $SGE_INSTALL_DIR/default/common/settings.sh" >>/etc/profile
  source $SGE_INSTALL_DIR/default/common/settings.sh
  source /etc/profile
#  echo "PATH=$SGE_INSTALL_DIR/bin/lx-amd64:\$PATH" >> /etc/bash.bashrc
#  echo "source $SGE_INSTALL_DIR/default/common/settings.sh" >> /etc/bash.bashrc
  echo "PATH=$SGE_INSTALL_DIR/bin/lx-amd64:\$PATH" >> ~/.bashrc
  echo "source $SGE_INSTALL_DIR/default/common/settings.sh" >> ~/.bashrc
  source ~/bash.bashrc
  # 删除默认的all.q队列
  # qconf -dq all.q
  # 关闭开机自启动并停止sge当前进程
  systemctl stop sgeexecd
  systemctl stop sgemaster
  systemctl disable sgeexecd
  systemctl disable sgemaster
  # 安装文件清理
  cat /dev/null >$SGE_INSTALL_DIR/default/common/act_qmaster
  rm -rf $SGE_INSTALL_DIR/default/common/install_logs
  host_name=$(hostname)
  if [ x"$host_name" != x"" ]; then
    rm -rf $SGE_INSTALL_DIR/default/spool/$host_name
    rm -rf $SGE_INSTALL_DIR/default/spool/qmaster/exec_hosts/$host_name
  fi
  cat /dev/null >$SGE_INSTALL_DIR/default/spool/qmaster/qmaster.pid
}

install_sge

# 安装 environment module
# wget https://chpc-online-bjtest.bj.bcebos.com/release/pkg/amd64/operate.sh -O operate.sh && bash operate.sh environment-modules_4.1.1-1_amd64 https://chpc-online-bjtest.bj.bcebos.com amd64 install.sh xxx bjtest /home/<USER>
operate_shell="$(get_bos_endpoint $region)/release/pkg/amd64/operate.sh"
wget $operate_shell && bash operate.sh environment-modules_4.1.1-1_amd64 $(get_bos_endpoint $region) amd64 install.sh xxx $region $software_dir