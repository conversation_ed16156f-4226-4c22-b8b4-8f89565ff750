#!/bin/bash
set -x

queue_name=$1

/opt/pbs/bin/qmgr -c "list queue $queue_name"
if [ $? -eq 0 ]; then
    echo "Queue $queue_name already exists"
else
    /opt/pbs/bin/qmgr -c "create queue $queue_name"
fi

/opt/pbs/bin/qmgr -c "set queue $queue_name queue_type=Execution"
/opt/pbs/bin/qmgr -c "set queue $queue_name enabled=True"
/opt/pbs/bin/qmgr -c "set queue $queue_name started=True"
/opt/pbs/bin/qmgr -c "set queue $queue_name acl_user_enable=True"
# /opt/pbs/bin/qmgr -c "set queue $queue_name from_route_only=True"
/opt/pbs/bin/qmgr -c "set queue route route_destinations += $queue_name"

/opt/pbs/bin/qmgr -c "set queue $queue_name default_chunk.pas_platform=$queue_name"
/opt/pbs/bin/qmgr -c "set queue $queue_name resources_default.pas_platform=$queue_name"
