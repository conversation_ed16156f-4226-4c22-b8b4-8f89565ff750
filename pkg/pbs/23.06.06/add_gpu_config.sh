#!/bin/bash
set -x

sched_config_path="/var/spool/pbs/sched_priv/sched_config"

# 配置文件中增加gpu资源
sed -i '/resources: ".*ngpus/!s/\(resources: ".*\)"/\1, ngpus"/' "$sched_config_path"
# 增加gpu资源类型
/opt/pbs/bin/qmgr -c "create resource ngpus type=long,flag=nh"
# 修改cgroup配置
/opt/pbs/bin/qmgr -c "import hook pbs_cgroups application/x-config default pbs_cgroups.json"
# 启用cgroup
/opt/pbs/bin/qmgr -c "set hook pbs_cgroups enabled=True"
# 重启pbs服务
/etc/init.d/pbs restart
if [[ $? -ne 0 ]]; then
    echo "Error: restart node failed."
    exit 1
fi
/etc/init.d/pbs status
if [[ $? -ne 0 ]]; then
    echo "Error: restatus node failed."
    exit 1
fi