#!/bin/bash
set -e

scheduler=$(grep 'scheduler_type' /plugin/chpc/scheduler_plugin.conf | awk -F'=' '{print $2}')

if [ "$scheduler" = "pbs" ]; then
	# 判断 qmgr 在 PATH 中
	echo "checking pbs command used by proxy..."
	pbs_env_found=0
	if [ -f /opt/pbs/etc/pbs.sh ]; then
		pbs_env_found=1
	fi
	if [[ $pbs_env_found -eq 0 ]]; then
		echo "Error: pbs env not found. "
		echo "Please ensure you install pbs_client package on this machine and /opt/pbs/etc/pbs.sh exists"
		echo "After that, you can use 'systemctl start chpc-server' command to start proxy"
	fi

	# *检查逻辑放到后面的脚本里面去
	# /opt/pbs/bin/qmgr -c "list server"
	# if [[ $? -ne 0 ]]; then
	# 	echo "Error: pbs qmgr command failed."
	# 	exit 1
	# fi
fi

systemctl enable chpc-server
systemctl restart chpc-server
if [[ $? -ne 0 ]]; then
	echo "Error: Start chpc-server failed."
	exit 1
fi
echo "chpc-server started."
