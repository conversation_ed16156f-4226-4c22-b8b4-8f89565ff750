#!/bin/bash

queue_list=$1

source check_env.sh

ret_code=$(check_env)
if [[ $ret_code == $REDHAT ]]; then
    yum install -y nfs-utils jq
else
    apt install -y nfs-kernel-server jq
fi

echo $queue_list | tr -d '\\' | jq -r '.[] | .name as $name | if .node then .node[] | {name: $name, ip: .ip, spec: .spec, hostname: .hostname} | "\(.name), \(.ip), \(.spec), \(.hostname)" else "\($name), , , " end' | while IFS=, read -r name ip spec hostname; do
    echo "queue: $name"
    echo "ip: $ip"
    echo "hostname: $hostname"
    echo "spec: $spec"

    bash add_queue.sh $name
    if [[ $? -ne 0 ]]; then
        echo "bash add_queue.sh $name"
        echo "Error: add queue failed."
        exit 1
    fi

    if [[ $ip != " " ]]; then
        echo "add node: [$name], [$ip], [$hostname], [$spec]"
        bash add_node.sh $name $ip $hostname $spec
        if [[ $? -ne 0 ]]; then
            echo "add_node.sh $name $ip $hostname $spec"
            echo "Error: add node failed."
            exit 1
        fi
    fi
    echo "-----------config queue & node done----------"
done
