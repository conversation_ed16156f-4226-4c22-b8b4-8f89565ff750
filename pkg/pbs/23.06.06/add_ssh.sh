#!/bin/bash
set -x

source check_env.sh
ret_code=$(check_env)
if [[ $ret_code == $REDHAT ]]; then
    sudo yum install sshpass
else
    sudo apt-get install sshpass
fi

# 机器B的用户名和地址
USER="test_user"
MASTER_HOST=$1
LOCAL_HOST=$2
LOCAL_IP=$3

# 本地（机器A）的公钥文件路径
LOCAL_PUB_KEY="/home/<USER>/.ssh/id_rsa.pub"

# authorized_keys文件路径
AUTH_KEYS="/home/<USER>/.ssh/authorized_keys"

# 机器B的登录信息
PASSWORD="123456"

# 临时文件用于存储机器B的authorized_keys内容
LOCAL_TEMP_FILE="/tmp/authorized_keys_from_B"

# 使用sshpass和scp将公钥复制到机器B，并添加到authorized_keys
sshpass -p "$PASSWORD" scp "$LOCAL_PUB_KEY" "${USER}@${MASTER_HOST}:/home/<USER>/.ssh/id_rsa_from_A.pub"

cmd1="mkdir -p ~/.ssh
if grep -qF -- '${LOCAL_HOST}' /home/<USER>/.ssh/authorized_keys; then
    echo '${LOCAL_HOST} is already in /home/<USER>/.ssh/authorized_keys'
else
    echo '${LOCAL_HOST} is not in /home/<USER>/.ssh/authorized_keys'
    cat /home/<USER>/.ssh/id_rsa_from_A.pub >> /home/<USER>/.ssh/authorized_keys
fi
rm /home/<USER>/.ssh/id_rsa_from_A.pub"

# 在机器B上创建~/.ssh目录（如果不存在），追加公钥到authorized_keys，然后删除临时公钥文件
sshpass -p "$PASSWORD" ssh "${USER}@${MASTER_HOST}" "$cmd1"

cmd2="if grep -qF -- '${LOCAL_IP}' /etc/hosts; then
    echo '${LOCAL_IP} is already in /etc/hosts'
else
    echo '${LOCAL_IP} ${LOCAL_HOST} ${LOCAL_HOST}'>>/etc/hosts
fi"

sshpass -p "baidu@123" ssh "root@${MASTER_HOST}" "$cmd2"

# 从机器B复制回更新后的authorized_keys到机器A的临时文件
sshpass -p "$PASSWORD" scp "${USER}@${MASTER_HOST}:${AUTH_KEYS}" "$AUTH_KEYS"

if [[ $PATH =~ "/opt/pbs/bin" ]]; then
    echo "/opt/pbs/bin already in PATH"
    exit 0
else
    echo "PATH=$PATH:/opt/pbs/bin" >>~/.bashrc
    exit 0
fi
