#!/bin/bash
set -x

action=""
cluster_id=""
cluster_name=""
scheduler=""
scheduler_version=""
plugin_version=""
user_id=""
region=""
master_ip=false
master_host=false
login_host=""
login_ip=""
mount_target=""
mount_dir=""
software_dir=""
shared_storage_list=""
mount_option=""
node_type=""
cluster_type=""
enable_ha=""
enable_monitor=""
backup_hostnames=""
queue_list=""
ssh_port=""
max_nodes=""
max_cpus=""

source /etc/profile

# 解析shell参数
function parse_args() {
    args=$(getopt -o h:o:t: -l action:,clusterId:,clusterName:,scheduler:,schedulerVersion:,pluginVersion:,userId:,region:,masterIp:,masterHost:,loginHost::,mountTarget:,mountDir:,softwareDir:,mountOption:,sharedStorageList:,nodeType:,clusterType:,enableHa:,enableMonitor:,backupHostnames:,queueList:,sshPort:,maxNodes:,maxCpus: -- "$@")
    if [ $? != 0 ]; then
        echo "Parse error! Terminating..." >&2
        exit 1
    fi
    echo $args
    echo ""
    eval set -- "$args"
    while true; do
        case "$1" in
        --action)
            action="$2"
            shift 2
            ;;
        --clusterId)
            cluster_id="$2"
            shift 2
            ;;
        --clusterName)
            cluster_name="$2"
            shift 2
            ;;
        --scheduler)
            scheduler="$2"
            # 如果 scheduler 为 openpbs，转换为 pbs
            if [[ $scheduler == "openpbs" ]]; then
                scheduler="pbs"
            fi
            shift 2
            ;;
        --schedulerVersion)
            scheduler_version="$2"
            shift 2
            ;;
        --pluginVersion)
            plugin_version="$2"
            shift 2
            ;;
        --userId)
            user_id="$2"
            shift 2
            ;;
        --region)
            region="$2"
            shift 2
            ;;
        --downloadScripts)
            download_scripts="$2"
            shift 2
            ;;
        --masterIp)
            master_ip="$2"
            shift 2
            ;;
        --masterHost)
            master_host="$2"
            shift 2
            ;;
        --loginHost)
            login_host="$2"
            shift 2
            ;;
        --mountTarget)
            mount_target="$2"
            shift 2
            ;;
        --mountDir)
            mount_dir="$2"
            shift 2
            ;;
        --softwareDir)
            software_dir="$2"
            shift 2
            ;;
        --sharedStorageList)
            shared_storage_list="$2"
            shift 2
            ;;
        --mountOption)
            # mount_option="$2"
            echo "=========> mount_option: skip"
            shift
            ;;
        --nodeType)
            node_type="$2"
            shift 2
            ;;
        --clusterType)
            cluster_type="$2"
            shift 2
            ;;
        --enableHa)
            enable_ha="$2"
            shift 2
            ;;
        --enableMonitor)
            enable_monitor="$2"
            shift 2
            ;;
        --backupHostnames)
            backup_hostnames="$2"
            shift 2
            ;;
        --queueList)
            queue_list="$2"
            shift 2
            ;;
        --sshPort)
            ssh_port="$2"
            shift 2
            ;;
        --maxNodes)
            max_nodes="$2"
            shift 2
            ;;
        --maxCpus)
            max_cpus="$2"
            shift 2
            ;;
        -h)
            help
            exit 0
            ;;
        --)
            shift
            break
            ;;
        *)
            echo "Parameters error!!!$1"
            exit 1
            ;;
        esac
    done
}

function main() {
    # 将当前脚本拷贝到 /opt/chpc/scripts 目录
    mkdir -p /opt/chpc/scripts
    cp -rf *.sh /opt/chpc/scripts

    echo "exec_func: $action"
    if [ x"$action" == x"installNode" ] && ([ x"$node_type" == x"master" ] && [ x"$cluster_type" == x"hybrid" ] || [ x"$node_type" == x"proxy" ]); then
        echo $scheduler $scheduler_version $plugin_version $cluster_id $cluster_name $user_id $region
        # 安装调度器 client
        echo "[step1] install_node $scheduler $scheduler_version $region $software_dir"
        bash install_node.sh $scheduler $scheduler_version $region $software_dir install_client.sh
        if [ $? -ne 0 ]; then
            echo "Error: install_node.sh $scheduler $scheduler_version $region $software_dir failed"
            exit 1
        fi
        # 安装插件
        echo "[step2] install_scheduler_plugin $scheduler $scheduler_version $plugin_version $region"
        bash install_scheduler_plugin.sh $scheduler $scheduler_version $plugin_version $region
        if [ $? -ne 0 ]; then
            echo "Error: install_scheduler_plugin.sh $scheduler $scheduler_version $plugin_version $region failed"
            exit 1
        fi
        # 安装 cluster-api
        if [ x"$login_host" != x"" ]; then
            result=`ping -c 1 $login_host | awk 'NR==1 {match($0, /([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3})/); print substr($0, RSTART, RLENGTH)}'`
            login_ip=$result
        fi
        echo "[step3] install_chpc_server $cluster_id $cluster_name $user_id $region $max_nodes $max_cpus $queue_list $login_ip $login_host"
        bash install_chpc_server.sh $cluster_id $cluster_name $user_id $region $max_nodes $max_cpus $queue_list $login_ip $login_host
        if [ $? -ne 0 ]; then
            echo "Error: install_chpc_server.sh $cluster_id $cluster_name $user_id $region $max_nodes $max_cpus $queue_list $login_ip $login_host failed"
            exit 1
        fi
        # 配置调度器信息
        echo "[step4] add_scheduler_info $master_ip $master_host $scheduler"
        bash add_scheduler_info.sh $master_ip $master_host $scheduler
        if [ $? -ne 0 ]; then
            echo "Error: add_scheduler_info.sh $master_ip $master_host $scheduler failed"
            return exit 1
        fi
        # 启动 chpc-server
        echo "[step5] start_chpc_server"
        bash start_chpc_server.sh
        ret_code=$?

        # 如果 sharedStorageList 不为空，则挂载 nfs
        if [ x"$shared_storage_list" != x"" ]; then
            echo "[step6] mount_nfs"
            echo $cluster_id $software_dir "$shared_storage_list"
            bash mount_nfs.sh $cluster_id $software_dir "$shared_storage_list"

            if [ $? -ne 0 ]; then
                # nfs 挂载出错，后面可以修正
                echo "Error: mount_nfs.sh $cluster_id $software_dir $shared_storage_list failed"
            fi

        fi

        return $ret_code
    fi

    if [ x"$action" == x"checkSchedulerInfo" ]; then
        echo $master_ip $master_host
        # echo "bash check_scheduler_info.sh $master_ip $master_host $scheduler"
        bash check_scheduler_info.sh $master_ip $master_host $scheduler

        if [ $? -ne 0 ]; then
            echo "Error: check_scheduler_info.sh $master_ip $master_host $scheduler failed"
        fi
        return $?
    fi

    if [ x"$action" == x"checkProxyRole" ]; then
        # echo "bash check_proxy_role.sh $scheduler"
        bash check_proxy_role.sh $scheduler

        if [ $? -ne 0 ]; then
            echo "Error: check_proxy_role.sh $scheduler failed"
        fi
        return $?
    fi

    if [ x"$action" == x"addMasterInfo" ]; then
        echo $master_ip $master_host
        # echo "bash add_scheduler_info.sh $master_ip $master_host $scheduler"
        bash add_scheduler_info.sh $master_ip $master_host $scheduler

        if [ $? -ne 0 ]; then
            echo "Error: add_scheduler_info.sh $master_ip $master_host $scheduler failed"
        fi
        return $?

    fi

    if [ x"$action" == x"mountNfs" ]; then
        echo $cluster_id $software_dir "$shared_storage_list"
        bash mount_nfs.sh $cluster_id $software_dir "$shared_storage_list"

        if [ $? -ne 0 ]; then
            echo "Error: mount_nfs.sh $cluster_id $software_dir $shared_storage_list failed"
        fi
        return $?
    fi

    if [ x"$action" == x"checkNfs" ]; then
        # todo@fengzhaoyang
        echo "checkNfs"
    fi

    if [ x"$action" == x"checkNetwork" ]; then
        # todo@fengzhaoyang
        echo "checkNetwork"
    fi

    if [ x"$action" == x"installNode" ] && [ x"$node_type" == x"master" ] && [ x"$cluster_type" == x"cloud" ]; then
        # 安装节点
        echo $scheduler $scheduler_version $region
        echo "[step1] install_node.sh $scheduler $scheduler_version $region $software_dir"
        bash install_node.sh $scheduler $scheduler_version $region $software_dir install_server.sh
        if [ $? -ne 0 ]; then
            echo "Error: install_node.sh $scheduler $scheduler_version $region $software_dir failed"
            exit 1
        fi

        # 配置调度器信息
        echo "[step1.1] add_scheduler_info $master_ip $master_host $scheduler"
        bash add_scheduler_info.sh $master_ip $master_host $scheduler
        if [ $? -ne 0 ]; then
            echo "Error: add_scheduler_info.sh $master_ip $master_host $scheduler failed"
            exit 1
        fi

        # 安装插件
        echo "[step2] install_scheduler_plugin $scheduler $scheduler_version $plugin_version $region"
        bash install_scheduler_plugin.sh $scheduler $scheduler_version $plugin_version $region
        if [ $? -ne 0 ]; then
            echo "Error: install_scheduler_plugin.sh $scheduler $scheduler_version $plugin_version $region failed"
            exit 1
        fi
        # 安装 cluster-api
        if [ x"$login_host" != x"" ]; then
            result=`ping -c 1 $login_host | awk 'NR==1 {match($0, /([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3})/); print substr($0, RSTART, RLENGTH)}'`
            login_ip=$result
        fi
        echo "[step3] install_chpc_server $cluster_id $cluster_name $user_id $region $max_nodes $max_cpus $queue_list $login_ip $login_host"
        bash install_chpc_server.sh $cluster_id $cluster_name $user_id $region $max_nodes $max_cpus $queue_list $login_ip $login_host
        if [ $? -ne 0 ]; then
            echo "Error: install_chpc_server.sh $cluster_id $cluster_name $user_id $region $max_nodes $max_cpus $queue_list $login_ip $login_host failed"
            exit 1
        fi
        # 启动 pbs 服务
        echo "[step4] start_node"
        bash start_node.sh $scheduler $login_host
        if [ $? -ne 0 ]; then
            echo "Error: start_node.sh failed"
            exit 1
        fi
        # 启动 chpc-server
        echo "[step5] start_chpc_server"
        bash start_chpc_server.sh
        if [ $? -ne 0 ]; then
            echo "Error: start_chpc_server.sh failed"
            exit 1
        fi
        # 配置集群自定义参数（gpu、pas_platform 等）
        echo "[step6] add_gpu_config"
        bash config_server.sh
        bash add_gpu_config.sh
        if [ $? -ne 0 ]; then
            echo "Error: add_gpu_config.sh failed"
            exit 1
        fi
        # 配置队列和节点
        echo "[step7] config_queue_node"
        bash config_queue_node.sh $queue_list
        ret_code=$?
        if [ $? -ne 0 ]; then
            echo "Error: config_queue_node.sh $queue_list failed"
            exit 1
        fi

        # 如果 sharedStorageList 不为空，则挂载 nfs
        if [ x"$shared_storage_list" != x"" ]; then
            echo "[step8] mount_nfs"
            echo $cluster_id $software_dir "$shared_storage_list"
            bash mount_nfs.sh $cluster_id $software_dir "$shared_storage_list"

            ret_code=$?
            if [ $? -ne 0 ]; then
                echo "Error: mount_nfs.sh $cluster_id $software_dir $shared_storage_list failed"
                exit 1
            fi
        fi

        # 修改ssh配置
        echo "        StrictHostKeyChecking no" >> /etc/ssh/ssh_config
        # 配置work用户ssh互信
        su - work -c 'cd ~ && mkdir -p .ssh'
        # 先备份
        su - work -c 'cd ~ && [ -f .ssh/id_rsa ] && mv -f .ssh/id_rsa .ssh/id_rsa.bak; cd ~ && [ -f .ssh/id_rsa.pub ] && mv -f .ssh/id_rsa.pub .ssh/id_rsa.pub.bak'
        su - work -c 'cd ~ && ssh-keygen -t rsa -N "" -f .ssh/id_rsa && \
            cat .ssh/id_rsa.pub >> .ssh/authorized_keys && chmod 700 .ssh && chmod 600 .ssh/authorized_keys'

        return $ret_code
    fi

    if [ x"$action" == x"installNode" ] && [ x"$node_type" == x"compute" ]; then
        # 安装节点
        echo $scheduler $scheduler_version $region
        echo "[step1] install_node.sh $scheduler $scheduler_version $region $software_dir"
        bash install_node.sh $scheduler $scheduler_version $region $software_dir install_mon.sh
        if [ $? -ne 0 ]; then
            echo "Error: install_node.sh $scheduler $scheduler_version $region $software_dir failed"
            exit 1
        fi

        echo $master_ip $master_host
        echo "[step2] add_scheduler_info.sh $master_ip $master_host $scheduler"
        bash add_scheduler_info.sh $master_ip $master_host $scheduler

        if [ $? -ne 0 ]; then
            echo "Error: add_scheduler_info.sh $master_ip $master_host $scheduler failed"
            exit 1
        fi
        # 启动 pbs 服务
        echo "[step3] start_node"
        bash start_node.sh $scheduler $login_host
        ret_code=$?
        if [ $? -ne 0 ]; then
            echo "Error: start_node.sh $scheduler failed"
            exit 1
        fi

        # 如果 sharedStorageList 不为空，则挂载 nfs
        if [ x"$shared_storage_list" != x"" ]; then
            echo "[step4] mount_nfs"
            echo $cluster_id $software_dir "$shared_storage_list"
            bash mount_nfs.sh $cluster_id $software_dir "$shared_storage_list"

            ret_code=$?
            if [ $? -ne 0 ]; then
                echo "Error: mount_nfs.sh $cluster_id $software_dir $shared_storage_list failed"
                exit 1
            fi
        fi

        # ebc 节点挂载本地盘，并配置 PBS_JOBDIR
        echo "[step5] config_ebc_jobdir"
        bash config_ebc_jobdir.sh
        ret_code=$?
        if [ $? -ne 0 ]; then
            echo "Error: config_ebc_jobdir.sh failed"
            exit 1
        fi

        # 修改ssh配置
        echo "        StrictHostKeyChecking no" >> /etc/ssh/ssh_config
        # 配置work用户ssh互信
        su - work -c ' cd ~ && mkdir -p .ssh && ssh-keygen -t rsa -N "" -f .ssh/id_rsa && \
            cat .ssh/id_rsa.pub >> .ssh/authorized_keys && chmod 700 .ssh && chmod 600 .ssh/authorized_keys'

        return $ret_code
    fi

    if [ x"$action" == x"installNode" ] && [ x"$node_type" == x"login" ] && [ x"$cluster_type" == x"cloud" ]; then
        # 安装节点
        echo $scheduler $scheduler_version $region
        echo "[step1] install_node.sh $scheduler $scheduler_version $region $software_dir"
        bash install_node.sh $scheduler $scheduler_version $region $software_dir install_client.sh
        if [ $? -ne 0 ]; then
            echo "Error: install_node.sh $scheduler $scheduler_version $region $software_dir failed"
            exit 1
        fi

        # 安装插件
        echo "[step2] install_scheduler_plugin $scheduler $scheduler_version $plugin_version $region"
        bash install_scheduler_plugin.sh $scheduler $scheduler_version $plugin_version $region
        if [ $? -ne 0 ]; then
            echo "Error: install_scheduler_plugin.sh $scheduler $scheduler_version $plugin_version $region failed"
            exit 1
        fi

        echo $master_ip $master_host
        echo "[step3] add_scheduler_info.sh $master_ip $master_host $scheduler"
        bash add_scheduler_info.sh $master_ip $master_host $scheduler

        ret_code=$?
        if [ $? -ne 0 ]; then
            echo "Error: add_scheduler_info.sh $master_ip $master_host $scheduler failed"
            exit 1
        fi

        # 如果 sharedStorageList 不为空，则挂载 nfs
        if [ x"$shared_storage_list" != x"" ]; then
            echo "[step4] mount_nfs"
            echo $cluster_id $software_dir "$shared_storage_list"
            bash mount_nfs.sh $cluster_id $software_dir "$shared_storage_list"

            if [ $? -ne 0 ]; then
                echo "Error: mount_nfs.sh $cluster_id $software_dir $shared_storage_list failed"
                exit 1
            fi
        fi

        # 修改ssh端口
        if [ x"$ssh_port" != x"" ]; then
            echo "[step6] modify_ssh_port $ssh_port"
            bash modify_ssh_port.sh $ssh_port
            ret_code=$?
            if [ $? -ne 0 ]; then
                echo "Error: modify_ssh_port.sh $ssh_port failed"
                exit 1
            fi
        fi

        # 修改ssh配置
        echo "        StrictHostKeyChecking no" >> /etc/ssh/ssh_config
        # 配置work用户ssh互信
        su - work -c ' cd ~ && mkdir -p .ssh && ssh-keygen -t rsa -N "" -f .ssh/id_rsa && \
            cat .ssh/id_rsa.pub >> .ssh/authorized_keys && chmod 700 .ssh && chmod 600 .ssh/authorized_keys'

        return $ret_code
    fi

    if [ x"$action" == x"addUser" ]; then
        # 安装节点
        echo $scheduler $scheduler_version $region $software_dir
        echo "bash install_node.sh $scheduler $scheduler_version $region $software_dir"
        bash install_node.sh $scheduler $scheduler_version $region $software_dir install_mon.sh
        if [ $? -ne 0 ]; then
            echo "Error: install_node.sh $scheduler $scheduler_version $region failed"
            exit 1
        fi

        echo $master_ip $master_host
        echo "bash add_scheduler_info.sh $master_ip $master_host $scheduler"
        bash add_scheduler_info.sh $master_ip $master_host $scheduler

        if [ $? -ne 0 ]; then
            echo "Error: add_scheduler_info.sh $master_ip $master_host $scheduler failed"
            return $?
        fi

        bash start_node.sh $scheduler $login_host

        ret_code=$?
        return $ret_code
    fi
}

parse_args $@
main
