#!/bin/bash
set -x

scheduler=$1
loginHost=$2

if [ "$scheduler" = "pbs" ]; then
    /etc/init.d/pbs start
    if [[ $? -ne 0 ]]; then
        echo "Error: start node failed."
        exit 1
    fi

    /etc/init.d/pbs status
    if [[ $? -ne 0 ]]; then
        echo "Error: status node failed."
        exit 1
    fi
fi
if [ x"$loginHost" != x"" ]; then
  /opt/pbs/bin/qmgr -c "set server operators = root@$loginHost"
fi

exit 0