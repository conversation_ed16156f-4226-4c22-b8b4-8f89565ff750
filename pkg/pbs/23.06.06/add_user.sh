#!/bin/bash
set -x

source check_env.sh
ret_code=$(check_env)
if [[ $ret_code == $REDHAT ]]; then
    sudo yum install -y expect
else
    sudo apt-get install -y expect
fi

# 检测test_user用户是否存在
if id "test_user" >/dev/null 2>&1; then
    echo "test_user 用户已存在"
else
    # 创建test_user用户
    useradd -m test_user -s /bin/bash

    # 设置test_user用户的密码（可选，你可以根据需要设置或稍后手动设置）
    echo "setting password for test_user user"
    echo "test_user:123456" | sudo chpasswd
fi

if [ -e "/home/<USER>/.ssh/id_rsa.pub" ]; then
    echo "/home/<USER>/.ssh/id_rsa.pub already exists"
    echo "RSA key pair generation for test_user user completed."
    exit 0
fi

# # 切换到test_user用户并执行ssh-keygen生成RSA证书
expect <<-EOF
# 切换到 test_user 用户
spawn su - test_user -c "ssh-keygen -t rsa"
expect {
    "Enter file in which to save the key" {
        send "\n"
        exp_continue
    }
    "Enter passphrase" {
        send "\n"
        exp_continue
    }
    "again" {
        send "\n"
        exp_continue
    }
    eof {
        exit 0
    }
}
EOF
# 将公钥保存到当前目录下的test_user_rsa.pub文件中
su - test_user -c 'cat /home/<USER>/.ssh/id_rsa.pub > /home/<USER>/.ssh/authorized_keys'

# 脚本结束
echo "RSA key pair generation for test_user user completed."
