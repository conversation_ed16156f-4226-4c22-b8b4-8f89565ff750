#!/bin/bash
set -x

queue_name=$1
node_ip=$2
node_host=$3
node_spec=$4

CONFIG="$node_ip $node_host"

# 检查/etc/hosts中是否已经有这个配置
if grep -qF -- "$CONFIG" /etc/hosts; then
    echo "$CONFIG is already in /etc/hosts"
else
    echo "$CONFIG not exist in /etc/hosts, add it"
    if grep -q "$node_ip" /etc/hosts; then
        # 如果存在，使用sed命令删除该行
        sed -i "/$node_ip/d" /etc/hosts
        echo "IP $node_ip removed from /etc/hosts"
    fi
    if grep -q "\b$node_host\b" /etc/hosts; then
        # 如果存在，使用sed命令删除该行
        sed -i "/\b$node_host\b/d" /etc/hosts
        echo "hostname $node_host removed from /etc/hosts"
    fi

    # 将新配置添加到/etc/hosts
    echo "$CONFIG" | sudo tee -a /etc/hosts >/dev/null
    echo "add $CONFIG to /etc/hosts"
fi

/opt/pbs/bin/qmgr -c "list node $node_host"
if [ $? -eq 0 ]; then
    echo "Node $node_host already exists"
else
    /opt/pbs/bin/qmgr -c "create node $node_host"
fi

/opt/pbs/bin/qmgr -c "set node $node_host resources_available.pas_platform=$queue_name"
/opt/pbs/bin/qmgr -c "set node $node_host resources_available.platform=$node_spec"

# 解析并配置gpu数量 bcc.gn3.c8m64.1v100-32g
IFS='.' read -r -a specParts <<< "$node_spec"
gpu_count=""
if [ ${#specParts[@]} -gt 3 ]; then
    re='([0-9]+)'
    if [[ ${specParts[3]} =~ $re ]]; then
        gpu_count="${BASH_REMATCH[1]}"
        # 如果数字后面跟这d字符则是ebc的cpu机型
        len=${#gpu_count}
        if [[ ${#specParts[3]} -gt $len ]]; then
           charD=${specParts[3]:$len:1}
           if [[ $charD == "d" ]]; then
             # 不添加gpu资源，这是cpu节点
             exit 0
            fi
        fi
        /opt/pbs/bin/qmgr -c "set node $node_host resources_available.ngpus=$gpu_count"
    fi
fi