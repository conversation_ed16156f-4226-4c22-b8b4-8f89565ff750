#!/bin/bash
set -x

masterIp=$1
masterHost=$2
scheduler=$3

if [ -z "$masterHost" ]; then
    echo "args masterHost is empty."
    exit 1
fi

if [ -z "$masterIp" ]; then
    echo "args masterIp is empty."
    exit 1
fi

CONFIG="$masterIp $masterHost"

# 检查/etc/hosts中是否已经有这个配置
if grep -qF -- "$CONFIG" /etc/hosts; then
    echo "$CONFIG is already in /etc/hosts"
else
    echo "$CONFIG not exist in /etc/hosts, add it"
    if grep -q "$masterIp" /etc/hosts; then
        # 如果存在，使用sed命令删除该行
        sed -i "/$masterIp/d" /etc/hosts
        echo "IP $masterIp removed from /etc/hosts"
    fi
    if grep -q "\b$masterHost\b" /etc/hosts; then
        # 如果存在，使用sed命令删除该行
        sed -i "/\b$masterHost\b/d" /etc/hosts
        echo "hostname $masterHost removed from /etc/hosts"
    fi

    # 将新配置添加到/etc/hosts
    echo "$CONFIG" | sudo tee -a /etc/hosts >/dev/null
    echo "add $CONFIG to /etc/hosts"
fi

PBS_CONF=/etc/pbs.conf
if [ -f "$PBS_CONF" ]; then
    echo "$PBS_CONF is exist"
else
    echo "$PBS_CONF does not exist. Exiting..."
    exit 1
fi

PBS_SERVER_CONFIG="PBS_SERVER=$masterHost"
# 检查/etc/pbs.conf中是否已经有这个配置
if grep -qF -- "$CONFIG" /etc/pbs.conf; then
    echo "$CONFIG is already in /etc/pbs.conf"
else
    echo "$CONFIG not exist in /etc/pbs.conf, add it"
    # 使用sed命令替换PBS_SERVER的值
    sudo sed -i "s/^PBS_SERVER=.*/PBS_SERVER=$masterHost/" "$PBS_CONF"
    echo "add $CONFIG to /etc/pbs.conf"
fi
