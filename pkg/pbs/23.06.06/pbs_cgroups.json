{"nvidia-smi": "/usr/bin/nvidia-smi", "cgroup_prefix": "pbs_jobs", "exclude_hosts": [], "exclude_vntypes": ["no_cgroups"], "run_only_on_hosts": [], "periodic_resc_update": true, "vnode_per_numa_node": false, "online_offlined_nodes": true, "use_hyperthreads": true, "ncpus_are_cores": false, "discover_gpus": true, "manage_rlimit_as": true, "cgroup": {"cpuacct": {"enabled": true, "exclude_hosts": [], "exclude_vntypes": []}, "cpuset": {"enabled": true, "exclude_cpus": [], "exclude_hosts": [], "exclude_vntypes": [], "mem_fences": false, "mem_hardwall": false, "memory_spread_page": false}, "devices": {"enabled": true, "exclude_hosts": [], "exclude_vntypes": [], "allow": ["b *:* rwm", "c *:* rwm", ["infiniband/issm0", "rwm"], ["infiniband/umad0", "rwm"], ["infiniband/uverbs0", "rwm"], ["infiniband/rdma_cm", "rwm"], ["fuse", "rwm"], ["net/tun", "rwm"], ["tty", "rwm"], ["ptmx", "rwm"], ["console", "rwm"], ["null", "rwm"], ["zero", "rwm"], ["full", "rwm"], ["random", "rwm"], ["urandom", "rwm"], ["nvidia-modeset", "rwm"], ["nvidia-uvm", "rwm"], ["nvidia-uvm-tools", "rwm"], ["nvidiactl", "rwm"]]}, "memory": {"enabled": false, "exclude_hosts": [], "exclude_vntypes": [], "soft_limit": false, "enforce_default": true, "exclhost_ignore_default": false, "default": "256MB", "reserve_percent": 0, "reserve_amount": "1GB"}, "memsw": {"enabled": false, "exclude_hosts": [], "exclude_vntypes": [], "enforce_default": true, "exclhost_ignore_default": false, "default": "0B", "reserve_percent": 0, "reserve_amount": "64MB", "manage_cgswap": false}, "hugetlb": {"enabled": false, "exclude_hosts": [], "exclude_vntypes": [], "enforce_default": true, "exclhost_ignore_default": false, "default": "0B", "reserve_percent": 0, "reserve_amount": "0B"}}}