#!/bin/bash

# 获取hostname
hostname=$(hostname)

# 执行qmgr命令并解析输出
output=$(/opt/pbs/bin/qmgr -c "list node $hostname")

echo "=====start to config ebc dir========"
echo "output: $output"

# 从输出中提取resources_available.platform
platform=$(echo "$output" | grep "resources_available.platform" | awk '{print $3}')

echo "node platform is: $platform"

# 如果$platform 以ebc 开头
if [[ $platform == ebc* ]]; then
    echo "This is an ebc node."
    sudo mkfs.ext4 /dev/nvme0n1
    sudo mkdir -p /mnt/data
    # 挂载设备到 /mnt/data
    sudo mount /dev/nvme0n1 /mnt/data
    sudo mkdir -p /mnt/data/jobdir

    # 将挂载信息写入 /etc/fstab
    echo '/dev/nvme0n1 /mnt/data ext4 defaults 0 0' | sudo tee -a /etc/fstab

    # 查看 /var/spool/pbs/mom_priv/config 中是否包含 $jobdir_root，如果不包含，则添加 $jobdir_root /mnt/data/jobdir
    if ! grep -q "jobdir_root" /var/spool/pbs/mom_priv/config; then
        echo "\$jobdir_root /mnt/data/jobdir" | sudo tee -a /var/spool/pbs/mom_priv/config
    fi

    echo "=============/var/spool/pbs/mom_priv/config============="
    cat /var/spool/pbs/mom_priv/config

    echo "=============restart pbs service============="
    /etc/init.d/pbs restart
    if [ $? -ne 0 ]; then
        echo "Error: restart pbs failed"
        exit 1
    fi

else
    echo "This is not an ebc node."
fi
