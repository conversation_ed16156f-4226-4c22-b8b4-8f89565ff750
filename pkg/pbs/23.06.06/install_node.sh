#!/bin/bash
set -x

scheduler=$1
schedulerVersion=$2
region=$3
software_dir=$4
scripts=$5

if [ -z "$scheduler" ]; then
    echo "args scheduler is empty."
    exit 1
fi

if [ -z "$schedulerVersion" ]; then
    echo "args schedulerVersion is empty."
    exit 1
fi

if [ -z "$region" ]; then
    echo "args region is empty."
    exit 1
fi

source check_env.sh
source get_bos_endpoint.sh
bos_endpoint="$(get_bos_endpoint $region)/release/pkg/amd64/pkg"

# 安装基础软件
ret_code=$(check_env)
if [[ $ret_code == $REDHAT ]]; then
    os="centos"
    # 如果 cat /etc/redhat-release 中包含 7.9
    if cat /etc/redhat-release | grep -q ".9"; then
        os="centos79"
    fi
    if cat /etc/redhat-release | grep -q "Baidu Linux release 2.0"; then
        os="centos79"
    fi
    if cat /etc/redhat-release | grep -q "8.4"; then
        sudo yum install -y hwloc-libs
        sudo yum install -y libICE
        sudo yum install -y libSM
    fi
    yum install -y libpq expat libedit postgresql-server postgresql-contrib python3 sendmail sudo tcl tk libical chkconfig cjson
else
    os="ubuntu"
    apt-get update
    apt --fix-broken install -y
    if [[ $ret_code == $UBUNTU2204 ]]; then
        # 禁止弹出交互窗口，默认不重启服务，仅列出可选项
        sed -i "s/\#\$nrconf{restart} = 'i'/\$nrconf{restart} = 'l'/" /etc/needrestart/needrestart.conf
        os="ubuntu22"
    else
        # ubuntu 20 安装
        apt install -y libhwloc-dev
    fi
    apt install -y libpq5 expat libedit2 python3 sendmail-bin sudo tcl tk libical3

    if echo $scripts | grep -q "server"; then
        apt install -y gcc make libtool libx11-dev \
            libxt-dev libedit-dev libical-dev ncurses-dev perl \
            postgresql-server-dev-all postgresql-contrib python3-dev tcl-dev tk-dev swig \
            libexpat-dev libssl-dev libxext-dev libxft-dev autoconf \
            automake g++
    fi
fi

# 下载节点安装包
echo "=====>Download install package $scheduler $schedulerVersion $os"

pkgFile="$scheduler-$schedulerVersion-$os"
downloadDir=/opt/chpc/download
mkdir -p $downloadDir
dstFile=$downloadDir/$pkgFile.tar.gz

if [ -f $dstFile ]; then
    rm $dstFile
fi
wget $bos_endpoint/$pkgFile.tar.gz -O $dstFile

if [ -d $downloadDir/$pkgFile ]; then
    rm -rf $downloadDir/$pkgFile
fi
tar -zxvf $dstFile -C $downloadDir

# 安装 hwloc
hwlocFile="hwloc-$os"
dstFile=$downloadDir/$hwlocFile.tar.gz
if [ -f $dstFile ]; then
    rm $dstFile
fi
wget $bos_endpoint/$hwlocFile.tar.gz -O $dstFile
if [ -d $downloadDir/hwloc ]; then
    rm -rf $downloadDir/hwloc
fi
tar -zxvf $dstFile -C $downloadDir

if [[ $os == "ubuntu" ]]; then
    cp -rf $downloadDir/hwloc/2.8.0/* /usr/local
elif [[ $os == "ubuntu22" ]]; then
    cp -rf $downloadDir/hwloc/2.8.0/* /usr
else
    cp -rf $downloadDir/hwloc/2.8.0/* /usr
    cp -rf $downloadDir/hwloc/2.8.0/lib/* /usr/lib64
fi

# 安装
cd $downloadDir/$pkgFile
bash $scripts
if [ $? -ne 0 ]; then
    echo "Error: Install client failed."
    exit 1
fi
echo "=====>Install install package $scheduler $schedulerVersion $os success."

# 安装 environment module
# wget https://chpc-online-bjtest.bj.bcebos.com/release/pkg/amd64/operate.sh -O operate.sh && bash operate.sh environment-modules_4.1.1-1_amd64 https://chpc-online-bjtest.bj.bcebos.com amd64 install.sh xxx bjtest /home/<USER>
operate_shell="$(get_bos_endpoint $region)/release/pkg/amd64/operate.sh"
wget $operate_shell && bash operate.sh environment-modules_4.1.1-1_amd64 $(get_bos_endpoint $region) amd64 install.sh xxx $region $software_dir

echo "=====>Start to install pbs track."

# 安装 /opt/pbs/bin/pbs_tmrsh.wrap
pbs_tmrsh="$(get_bos_endpoint $region)/release/pkg/amd64/pkg/pbs/pbs_tmrsh.wrap"
wget $pbs_tmrsh -O /opt/pbs/bin/pbs_tmrsh.wrap
chmod +x /opt/pbs/bin/pbs_tmrsh.wrap

# 增加 /opt/pbs/lib/libpbs.so 的软链接 libtorque.so.2
ln -sf /opt/pbs/lib/libpbs.so /opt/pbs/lib/libtorque.so.2