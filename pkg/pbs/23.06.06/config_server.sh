#!/bin/bash
set -x

sched_config_path="/var/spool/pbs/sched_priv/sched_config"

# 增加 pas_platform 和 platform 资源
sed -i '/resources: ".*platform/!s/\(resources: ".*\)"/\1, platform"/' "$sched_config_path"
resource=$(/opt/pbs/bin/qmgr -c "list resource platform")
if grep -qF -- "platform" "$resource"; then
    echo "platform resource already exists"
else
    /opt/pbs/bin/qmgr -c "create resource platform type=string,flag=h"
fi

sed -i '/resources: ".*pas_platform/!s/\(resources: ".*\)"/\1, pas_platform"/' "$sched_config_path"
resource=$(/opt/pbs/bin/qmgr -c "list resource pas_platform")
if grep -qF -- "pas_platform" "$resource"; then
    echo "pas_platform resource already exists"
else
    /opt/pbs/bin/qmgr -c "create resource pas_platform type=string,flag=h"
fi

/opt/pbs/bin/qmgr -c "s s job_history_enable=1"
/opt/pbs/bin/qmgr -c "s s flatuid=true"

queue=$(/opt/pbs/bin/qmgr -c "list queue workq")
if echo $queue | grep -q "workq"; then
    /opt/pbs/bin/qmgr -c "del q workq"
else
    echo "workq not exists"
fi

queue=$(/opt/pbs/bin/qmgr -c "list queue route")
if echo $queue | grep -q "route"; then
    /opt/pbs/bin/qmgr -c "del q route"
fi
/opt/pbs/bin/qmgr -c "create q route"
/opt/pbs/bin/qmgr -c "set q route queue_type = Route"
/opt/pbs/bin/qmgr -c "set queue route route_destinations += sdm"
# /opt/pbs/bin/qmgr -c "set queue route acl_user_enable=True"
/opt/pbs/bin/qmgr -c "set queue route started=True"
/opt/pbs/bin/qmgr -c "set queue route enabled=True"