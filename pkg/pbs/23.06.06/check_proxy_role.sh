#!/bin/bash

scheduler=$1

if [ -z "$scheduler" ]; then
    scheduler=$(grep 'scheduler_type' /plugin/chpc/scheduler_plugin.conf | awk -F'=' '{print $2}')
fi

if [ "$scheduler" = "pbs" ]; then
    # if grep -qF -- "PBS_START_MOM=1" /etc/pbs.conf; then
    managers=$(/opt/pbs/bin/qmgr -c "list server")
    if [[ $? -ne 0 ]]; then
        echo "Error: pbs qmgr command failed."
        exit 1
    fi
    if echo $managers|grep -q "root@$(hostname)"; then
        echo "proxy node is the manager of scheduler."
        exit 0
    else
        echo "Error: proxy node is not the manager of scheduler."
        exit 1
    fi
    # fi
fi
