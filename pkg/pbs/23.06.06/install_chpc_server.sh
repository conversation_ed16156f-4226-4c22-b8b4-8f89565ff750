# !/bin/bash
set -xe

# 下载 cluster-api 插件包
clusterId=$1
clusterName=$2
userId=$3
region=$4
maxNodes=$5
maxCpus=$6
queueList=$7
loginIp=$8
loginHost=$9

if [ -z "$clusterId" ]; then
	echo "args clusterId is empty."
	exit 1
fi

if [ -z "$userId" ]; then
	echo "args userId is empty."
	exit 1
fi

if [ -z "$region" ]; then
	echo "args region is empty."
	exit 1
fi

if [ -z "$clusterName" ]; then
	echo "args clusterName is empty."
	exit 1
fi

if [ -z "$queueList" ]; then
	# 将 $queueList 中的\去掉
	queueList=$(echo $queueList | sed 's/\\//g')
fi
echo $queueList

scheduler=$(grep 'scheduler_type' /plugin/chpc/scheduler_plugin.conf | awk -F'=' '{print $2}')

if [ "$region" = "sandbox" ]; then
	confFile="sandbox-$scheduler.yaml"
else
	confFile="prod-$scheduler.yaml"
fi

if [ "$scheduler" = "pbs" ]; then
	schedulerConfig="scheduler_plugin"
else
	schedulerConfig=$scheduler
fi

source get_bos_endpoint.sh
bos_endpoint="$(get_bos_endpoint $region)/release/pkg/prod"

# 下载 cluster-api 和配置
echo "=====>Download chpc server."
chpcFile="api-server.tar.gz"
downloadDir=/opt/chpc/download
mkdir -p $downloadDir
dstFile=$downloadDir/$chpcFile

if [ -f $dstFile ]; then
	rm $dstFile
fi
# wget $bos_endpoint/$chpcFile -O $dstFile
wget $(get_bos_endpoint $region)/release/cluster-api/prod/cluster-api -O $downloadDir/cluster-api

# 解压到 api-server 目录
if [ -d $downloadDir/chpc ]; then
	rm -rf $downloadDir/api-server
fi
# tar -zxvf $dstFile -C $downloadDir
cp -rf api-server $downloadDir

# 根据调度器生成配置
rm -rf $downloadDir/chpc
mkdir -p $downloadDir/chpc/cluster-api/conf
mkdir -p $downloadDir/chpc/cluster-api/template

cp $downloadDir/api-server/conf/$confFile $downloadDir/chpc/cluster-api/conf/online.yaml
cp $downloadDir/api-server/service/chpc-server.service.$scheduler $downloadDir/chpc/cluster-api/template/chpc-server.service
cp $downloadDir/api-server/cluster.conf $downloadDir/chpc/cluster.conf
chmod +x $downloadDir/cluster-api
cp $downloadDir/cluster-api $downloadDir/chpc/cluster-api

# 修改配置，clusterid、userid、endpoint
echo "=====>Start to update conf."
sed -i "s/{scheduler}/$schedulerConfig/g" $downloadDir/chpc/cluster.conf
sed -i "s/{clusterId}/$clusterId/g" $downloadDir/chpc/cluster.conf
sed -i "s/{region}/$region/g" $downloadDir/chpc/cluster.conf
sed -i "s/{clusterName}/$clusterName/g" $downloadDir/chpc/cluster.conf
sed -i "s/{maxNodes}/$maxNodes/g" $downloadDir/chpc/cluster.conf
sed -i "s/{maxCpus}/$maxCpus/g" $downloadDir/chpc/cluster.conf
sed -i "s/{queueList}/$queueList/g" $downloadDir/chpc/cluster.conf

sed -i "s/{region}/$region/g" $downloadDir/chpc/cluster-api/template/chpc-server.service

sed -i "s/{userId}/$userId/g" $downloadDir/chpc/cluster-api/conf/online.yaml
# 当登录节点不为空时候，写入登录节点的db配置
if [ -n "$loginIp" ]; then
  cat <<EOF >> "$downloadDir/chpc/cluster-api/conf/online.yaml"
login:
  mysql:
    host: ${loginIp}
    port: 3306
    db: chpc
    user: chpc
    password: Chpc@123
EOF

  CONFIG="$loginIp $loginHost"
  # 检查/etc/hosts中是否已经有这个配置
  if grep -qF -- "$CONFIG" /etc/hosts; then
    echo "$CONFIG is already in /etc/hosts"
  else
    echo "$CONFIG not exist in /etc/hosts, add it"
    if grep -q "$loginIp" /etc/hosts; then
      # 如果存在，使用sed命令删除该行
      sed -i "/$loginIp/d" /etc/hosts
      echo "IP $loginIp removed from /etc/hosts"
    fi
    if grep -q "\b$loginHost\b" /etc/hosts; then
      # 如果存在，使用sed命令删除该行
      sed -i "/\b$loginHost\b/d" /etc/hosts
      echo "hostname $loginHost removed from /etc/hosts"
    fi
    # 将新配置添加到/etc/hosts
    echo "$CONFIG" | sudo tee -a /etc/hosts >/dev/null
    echo "add $CONFIG to /etc/hosts"
  fi
fi
# 如果 region 以 test 结尾，将 test 去除
final_region=$region
final_region=$(echo "$final_region" | sed 's/test$//')
sed -i "s/{region}/$final_region/g" $downloadDir/chpc/cluster-api/conf/online.yaml

echo "=====>Start to install chpc-server."

# 安装到系统服务
mkdir -p /opt/chpc
rm -rf /opt/chpc/cluster-api
rm -rf /opt/chpc/cluster.conf
if [ -d /opt ]; then
	cp -r $downloadDir/chpc/* /opt/chpc
	if [ $? -ne 0 ]; then
		echo "Error: Copy chpc-server files failed."
		exit 1
	fi
	cp $downloadDir/chpc/cluster-api/template/chpc-server.service /lib/systemd/system/chpc-server.service
	if [ $? -ne 0 ]; then
		echo "Error: Copy chpc-server.service failed."
		exit 1
	fi
	systemctl daemon-reload
else
	echo "Error: Directory /opt create failed."
	exit 1
fi
echo "=====>Install chpc-server success."
