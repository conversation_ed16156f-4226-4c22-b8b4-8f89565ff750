#!/bin/bash
set -x

source check_env.sh
source get_bos_endpoint.sh

function install() {
    region=$2
    echo $region
    endpoint=$(get_bos_endpoint $region)

    # 如果设置了软件安装目录，则使用设置的目录，否则使用默认目录
    if [[ ! -z $3 ]]; then
        BASE_INSTALL_DIR=$3
        MODULE_HOME=$BASE_INSTALL_DIR/share/modules/modulefiles
    fi

    ret_code=$(check_env)
    if [[ $ret_code == $REDHAT ]]; then
        binFile=vnc_proxy-1.0.0.tar.gz
        moduleFile=vnc_proxy-1.0.0-module.tar.gz

        yum -y groupinstall "development tools"
        # 安装X server以支持图形话
        # yum instlal -y Xorg
        # 判断是centos7还是centos8
        CENTOSV=`cat /etc/redhat-release | awk -F ' +' '{print $4}'`
        CENTOSV="${CENTOSV:0:1}"
        if [[ "${CENTOSV}" == "8" ]]; then
            dnf groupinstall -y "Server with GUI"
        else
            yum groupinstall -y "GNOME Desktop"
        fi
        # 重启dbus.socket服务，这样ldap普通用户才能在不重启的情况下登录图形桌面
        systemctl restart dbus.socket
        # 重启systemd-logind服务，如果不重启有时候dbus无法连接到logind,导致黑屏
        systemctl restart systemd-logind
        # 设置默认启动图形界面
        systemctl set-default graphical.target
        # 启动gdm display manager
        systemctl enable gdm
        systemctl start gdm
        # 安装并启动display manager
        # yum install -y lightdm
        # systemctl enable lightdm
        # systemctl start lightdm
        # yum -y groupinstall "gnome desktop"
        yum -y install tigervnc tigervnc-server

        yum -y install python3 python3-devel
        pip3 install numpy
        pip3 install requests
    else
        if [[ $ret_code == $UBUNTU2204 ]]; then
            # 禁止弹出交互窗口，默认不重启服务，仅列出可选项
            sed -i "s/\#\$nrconf{restart} = 'i'/\$nrconf{restart} = 'l'/" /etc/needrestart/needrestart.conf
        fi

        binFile=vnc_proxy-1.0.0.tar.gz
        moduleFile=vnc_proxy-1.0.0-module.tar.gz

        apt-get -y update
        # 安装x server
        # apt-get install -y xorg
        sudo apt -y install vnc4server
        sudo apt -y install gnome-shell
        # 重启dbus.socket服务，让ldap普通用户在不重启的情况下完成登录
        systemctl restart dbus.socket
        # 重启systemd-logind服务
        systemctl restart systemd-logind
        # 设置默认为图形化界面
        systemctl set-default graphical.target
        systemctl enable gdm
        systemctl start gdm
    fi

    wget $endpoint/release/pkg/amd64/pkg/$binFile
    wget $endpoint/release/pkg/amd64/pkg/$moduleFile

    mkdir -p $BASE_INSTALL_DIR
    tar -zxvf $binFile -C $BASE_INSTALL_DIR
    mkdir -p $MODULE_HOME
    tar -zxvf $moduleFile -C $MODULE_HOME

    cd $MODULE_HOME
    # 将 modulefile 中的 /home/<USER>
    find . -type f -exec sed -i "s|/home/<USER>" {} +

    # 将 modulefile 中的 $proxy 替换成本机的主机名
    sed -i "s|\$proxy|$HOSTNAME|g" vnc_proxy/1.0.0

    source /etc/profile
    module load vnc_proxy/1.0.0
    # vnc_root="$BASE_INSTALL_DIR/vnc_proxy/1.0.0"
    # vnc_session="/usr/local/vnc/sessions"
    if [[ ! -z $vnc_session ]]; then
        mkdir -p $vnc_session
        chmod 777 $vnc_session
        mkdir -p /opt/novnc/logs
        touch /opt/novnc/logs/novnc.log
        chown -R /opt/novnc

        chown -R root:root $vnc_root/..
        cd $vnc_root
        bin/novnc_gen.sh
        bin/novnc_start.sh
        echo "vnc_session is not empty"
    else
        echo "vnc_session is empty"
        exit 1
    fi

    return 0
}

install $@

