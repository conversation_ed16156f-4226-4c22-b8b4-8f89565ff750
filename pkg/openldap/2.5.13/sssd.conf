[sssd]
services = nss, pam
config_file_version = 2
domains = LDAP

# Optional: Specify the attribute for home directories
ldap_home_directory = homeDirectory

# Optional: Specify the attribute for user login shells
ldap_user_shell = loginShell

# Enable enumeration (optional, can impact performance)
enumerate = true

# Enable automatic creation of home directories
fallback_homedir = /home/<USER>

[domain/LDAP]
id_provider = ldap
auth_provider = ldap
ldap_uri = ldap://192.168.80.41
ldap_search_base = dc=baiduhpc,dc=com
#ldap_tls_cacert = /etc/openldap/certs/ca.pem
