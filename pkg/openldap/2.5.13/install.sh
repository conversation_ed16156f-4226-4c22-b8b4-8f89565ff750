#! /bin/bash
set -x

source check_env.sh
source get_bos_endpoint.sh

CURRENT_DIR=$(cd $(dirname $0); pwd)
NSLCD_CONF_FILE="/etc/nslcd.conf"
NSSWITCH_CONF_FILE="/etc/nsswitch.conf"
SSSD_CONF_FILE="$CURRENT_DIR/sssd.conf"
LDAP_INSTALL_DIR="/opt/ldap/2.5.13"
SLAPD_SEVICE_FILE="$CURRENT_DIR/slapd.service"
LAPD_LOGROTATE="$CURRENT_DIR/ldap"
readonly CHECK_ALIVE_INTERVAL="5s"
# slapd占用端口
readonly SLAPD_PORT=389

REDHAT=1
UBUNTU2004=2
UBUNTU2204=3
master_host=""
node_type=""
region=""

function parse_args() {
    args=`getopt -o h,m:,n:,r: -l help,master_host:,node_type:,region: -- "$@"`
    if [ $? != 0 ] ; then
        echo "Parse error! Terminating..." >&2 ;
        exit 1 ;
    fi
    #echo $args
    eval set -- "$args"
    while true ; do
        case "$1" in
            -m) master_host="$2"; shift 2;;
            -n) node_type="$2"; shift 2;;
            -r) region="$2"; shift 2;;
            --master_host) master_host="$2"; shift 2;;
            --node_type) node_type="$2"; shift 2;;
            --region) region="$2"; shift 2;;
            -h) help; exit 0 ;;
            --help) help; exit 0 ;;
            --) shift ; break ;;
            *) echo "Parameters error!!!$1" ; exit 1 ;;
        esac
    done
}

function install() {
    if [ $node_type == "master" ]; then
        install_server
        start_slapd_no_ha
    fi
    install_client
    start_nslcd $master_host
}

# 安装ldap服务端
function install_server() {

    endpoint=$(get_bos_endpoint $region)

    ret_code=$(check_env)
    if [[ $ret_code == $REDHAT ]]; then
        os="centos"
        if cat /etc/redhat-release | grep -q "8.4"; then
            os="centos84"
        fi
        # centos 安装
        yum install gcc libtool-ltdl-devel nc -y
        wget $endpoint/release/pkg/amd64/pkg/openldap_2.5.13_amd64_$os.tar.gz -O /opt/openldap_2.5.13_amd64_$os.tar.gz
        tar -zxvf /opt/openldap_2.5.13_amd64_$os.tar.gz -C /opt
        rm -rf /opt/openldap_2.5.13_amd64_$os.tar.gz
    elif [[ $ret_code == $UBUNTU2204 ]]; then
        wget $endpoint/release/pkg/amd64/pkg/openldap_2.5.13_amd64_ubuntu2204.tar.gz -O  /opt/openldap_2.5.13_amd64_ubuntu2204.tar.gz
        tar -zxvf /opt/openldap_2.5.13_amd64_ubuntu2204.tar.gz -C /opt
        rm -rf /opt/openldap_2.5.13_amd64_ubuntu2204.tar.gz
    elif [[ $ret_code == $UBUNTU2004 ]]; then
        wget $endpoint/release/pkg/amd64/pkg/openldap_2.5.13_amd64_ubuntu2004.tar.gz -O  /opt/openldap_2.5.13_amd64_ubuntu2004.tar.gz
        tar -zxvf /opt/openldap_2.5.13_amd64_ubuntu2004.tar.gz -C /opt
        rm -rf /opt/openldap_2.5.13_amd64_ubuntu2004.tar.gz
    fi

    # 环境变量修改
    echo "PATH=$LDAP_INSTALL_DIR/bin:$LDAP_INSTALL_DIR/sbin:$LDAP_INSTALL_DIR/libexec:\$PATH" >> /etc/profile
    source /etc/profile
    cp $SLAPD_SEVICE_FILE /usr/lib/systemd/system/
    mv $LAPD_LOGROTATE /etc/logrotate.d
    return 0
}

# 安装ldap客户端
function install_client() {
    if [ -f "/etc/redhat-release" ]
    then
        yum -y install openldap-clients nss-pam-ldapd
        yum -y install nc
    elif [ -f "/etc/lsb-release" ]
    then
        apt-get update
        DEBIAN_FRONTEND=noninteractive apt -yq install libnss-ldapd libpam-ldapd ldap-utils
        pam-auth-update --enable mkhomedir
    else
        echo "unexpected os,only support centos or ubuntu,exit!!!"
        return 1
    fi
    sed -i '/uri/d' $NSLCD_CONF_FILE
    sed -i '/base/d' $NSLCD_CONF_FILE
    echo "base dc=baiduhpc,dc=com" >> $NSLCD_CONF_FILE

    sed -i '/^passwd:/{N;s/\n/ ldap\n/}' $NSSWITCH_CONF_FILE
    sed -i '/^group:/{N;s/\n/ ldap\n/}' $NSSWITCH_CONF_FILE
    sed -i '/^shadow:/{N;s/\n/ ldap\n/}' $NSSWITCH_CONF_FILE
    sed -i '/^gshadow:/{N;s/\n/ ldap\n/}' $NSSWITCH_CONF_FILE

    systemctl stop nscd
    systemctl stop nslcd
    systemctl disable nslcd
    systemctl disable nscd
    return 0
}

# 计算节点启动
function start_nslcd() {
    master_host=$1
    # centos7.9配置修改
    if [ -f "/etc/redhat-release" ]
    then
        ip=`nslookup $master_host|sed -n '6p'|awk '{print $2}'`
        authconfig --enableldap --enableldapauth --ldapserver=$master_host --ldapbasedn="dc=baiduhpc,dc=com" --enablemkhomedir --update
        ret_code=$?
        if [ $ret_code -ne 0 ]
        then
            echo "start sgeexecd failed"
            return 1
        fi

        match_num=$(grep -c "uri ldap" /etc/nslcd.conf)
        if [ $match_num = 0 ]; then
            echo "uri ldap://$ip/">>/etc/nslcd.conf
        fi

    # ubuntu20.04配置修改
    elif [ -f "/etc/lsb-release" ]
    then
        # 根据hostname解析ip地址
        ip=`nslookup $master_host|sed -n '6p'|awk '{print $2}'`
        ret_code=$?
        if [ $ret_code -ne 0 ]; then
        echo "get master ip failed"
        return 1
        fi
        match_num=$(grep -c "session optional pam_mkhomedir.so skel=/etc/skel umask=077" /etc/pam.d/common-session)
        if [ $match_num = 0 ]; then
            echo "session optional pam_mkhomedir.so skel=/etc/skel umask=077">>/etc/pam.d/common-session
        fi

        match_num=$(grep -c "pam_authc_search NONE" /etc/nslcd.conf)
        if [ $match_num = 0 ]; then
            echo "pam_authc_search NONE">>/etc/nslcd.conf
        fi

        match_num=$(grep -c "uri ldap" /etc/nslcd.conf)
        if [ $match_num = 0 ]; then
            echo "uri ldap://$ip/">>/etc/nslcd.conf
        else
            sed -i "s#^uri ldap:.*#uri ldap://$ip/#" /etc/nslcd.conf
        fi

    fi
    # 关闭nscd服务,nscd缓存会导致登录失败
    systemctl disable nscd
    systemctl enable nslcd
    ret_code=$?
    if [ $ret_code -ne 0 ]
    then
        echo "set nslcd autostart failed"
        return 1
    fi

    systemctl stop nslcd
    sleep $CHECK_ALIVE_INTERVAL
    systemctl start nslcd
    ret_code=$?
    if [ $ret_code -ne 0 ]
    then
        echo "start nslcd failed"
        return 1
    fi
    nslcd_status=`systemctl status nslcd | grep Active | awk '{print $3}' | cut -d "(" -f2 | cut -d ")" -f1`
    if [ "$nslcd_status" != "running" ]
      then
      echo "start nslcd failed"
      return 1
	  fi
    centos_info=$(cat /etc/*release | grep 'CentOS Linux release 8.4')
    if [[ ! -z "$centos_info" ]]; then
        # 根据hostname解析ip地址
        ip=`nslookup $master_host|sed -n '6p'|awk '{print $2}'`
        ret_code=$?
        if [ $ret_code -ne 0 ]; then
          echo "get master ip failed"
          return 1
        fi
        echo "ldap_uri = ldap://$ip">>$SSSD_CONF_FILE
        cp $SSSD_CONF_FILE /etc/sssd/
        chmod 600 /etc/sssd/sssd.conf
        echo "session required pam_mkhomedir.so skel=/etc/skel/ umask=0077">>/etc/pam.d/sshd
        authselect select sssd with-mkhomedir -y
        systemctl enable oddjobd.service && systemctl restart oddjobd.service
        oddjobd_status=`systemctl status oddjobd | grep Active | awk '{print $3}' | cut -d "(" -f2 | cut -d ")" -f1`
        if [ "$oddjobd_status" != "running" ]
          then
          echo "start oddjobd failed"
          return 1
        fi
        systemctl enable sssd && systemctl restart sssd
        sssd_status=`systemctl status sssd | grep Active | awk '{print $3}' | cut -d "(" -f2 | cut -d ")" -f1`
        if [ "$sssd_status" != "running" ]
          then
          echo "start sssd failed"
          return 1
        fi
        match_num=$(grep -c "uri ldap" /etc/nslcd.conf)
        if [ $match_num = 0 ]; then
            echo "uri ldap://$ip/">>/etc/nslcd.conf
        fi
  fi
    return 0
}

# 启动ldap主进程,主节点执行
function start_slapd_no_ha() {
    # ldap日志
    echo "local4.*                                                /var/log/ldap.log" >> /etc/rsyslog.conf
    service rsyslog restart
    systemctl enable slapd
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "set slapd autostart failed"
        return 1
    fi
    systemctl start slapd
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "start slapd failed"
        return 1
    fi
    slapd_status=`systemctl status slapd | grep Active | awk '{print $3}' | cut -d "(" -f2 | cut -d ")" -f1`
    if [ "$slapd_status" != "running" ]
    then
		echo "start slapd failed"
		return 1
	fi
    check_port_is_open $SLAPD_PORT
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "start slapd failed"
        return 1
    fi
    return 0
}

# 检测端口是否被占用 每个5s检测一次,循环5次
function check_port_is_open() {
    port=$1
    # 先sleep 5s,再检测端口占用
    sleep $CHECK_ALIVE_INTERVAL
    for((i=0;i<5;i++));
    do
        nc -zv localhost $port
        ret_code=$?
        if [ $ret_code -ne 0 ]; then
            sleep $CHECK_ALIVE_INTERVAL
            continue
        else
            return 0
        fi
    done
    echo "port $port not open,process start failed"
    return 1
}

parse_args $@
install