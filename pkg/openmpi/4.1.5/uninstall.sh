#!/bin/bash
set -x

source check_env.sh

function uninstall() {
    ret_code=$(check_env)
    if [[ $ret_code == $REDHAT ]]; then
        echo "uninstall openmpi-4.1.5 in centos"
    else
        if [[ $ret_code == $UBUNTU2204 ]]; then
            # 禁止弹出交互窗口，默认不重启服务，仅列出可选项
            sed -i "s/\#\$nrconf{restart} = 'i'/\$nrconf{restart} = 'l'/" /etc/needrestart/needrestart.conf
        fi
        echo "uninstall openmpi-4.1.5 in ubuntu $ret_code"

    fi

    # 如果设置了软件安装目录，则使用设置的目录，否则使用默认目录
    if [[ ! -z $3 ]]; then
        BASE_INSTALL_DIR=$3
        MODULE_HOME=$BASE_INSTALL_DIR/share/modules/modulefiles
    fi

    rm -rf $BASE_INSTALL_DIR/openmpi/4.1.5
    rm -rf $BASE_INSTALL_DIR/ucx/1.14.1

    module unload mpi/openmpi-4.1.5
    rm -rf $MODULE_HOME/mpi/openmpi-4.1.5
    rm -rf $MODULE_HOME/mpi/.version

    return 0
}

uninstall $@
