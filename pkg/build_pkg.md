## 编译 openmpi

```sh
WITH_CUDA=0
BASE_INSTALL_DIR=/home/<USER>
MODULE_HOME=/home/<USER>/modules/modulefiles

wget "https://chpc.bj.bcebos.com/packages/build_shell/build_openmpi.sh"

if [[ $WITH_CUDA > 0 ]]; then
    bash build_openmpi.sh -i -m --default --with-ucx --with-cuda --base_install_dir="$BASE_INSTALL_DIR" --modulefile_home="$MODULE_HOME"
else
    bash build_openmpi.sh -i -m --default --with-ucx --base_install_dir="$BASE_INSTALL_DIR" --modulefile_home="$MODULE_HOME"
fi

cd /home/<USER>/modules/modulefiles/
tar -zcvf mpi-4.1.5-module-ubuntu.tar.gz mpi

cd /home/<USER>/
tar -zcvf openapi-4.1.5-ubuntu.tar.gz openmpi ucx
```

## 编译 gromacs
centos 需要先安装 gcc-9
```sh
sudo yum install -y centos-release-scl
sudo yum install -y devtoolset-9-gcc devtoolset-9-gcc-c++ devtoolset-9-binutils
scl enable devtoolset-9 bash
```

```sh
wget "https://chpc.bj.bcebos.com/packages/build_shell/build_gromacs.sh"

# ubuntu 编译的时候，要将 build_gromacs.sh 中的make -j 改为make -j4

WITH_CUDA=0
BASE_INSTALL_DIR=/home/<USER>
MODULE_HOME=/home/<USER>/modules/modulefiles

./build_gromacs.sh -i -m --with-mpi --download-fftw --base_install_dir="$BASE_INSTALL_DIR" --modulefile_home="$MODULE_HOME"

# 打包
cd /home/<USER>/modules/modulefiles/
tar -zcvf gromacs-2023.1-module-centos.tar.gz gromacs

cd /home/<USER>/
tar -zcvf gromacs-2023.1-centos.tar.gz gromacs

# 测试
module load gromacs
module load openmpi
gmx_mpi --version
```

## 安装 cuda
```sh
wget https://developer.download.nvidia.com/compute/cuda/12.1.0/local_installers/cuda_12.1.0_530.30.02_linux.run
sudo sh cuda_12.1.0_530.30.02_linux.run --toolkitpath=/home/<USER>/cuda/12.1 --toolkit

===========
= Summary =
===========

Driver:   Installed
Toolkit:  Installed in /home/<USER>/cuda/12.1/

Please make sure that
 -   PATH includes /home/<USER>/cuda/12.1/bin
 -   LD_LIBRARY_PATH includes /home/<USER>/cuda/12.1/lib64, or, add /home/<USER>/cuda/12.1/lib64 to /etc/ld.so.conf and run ldconfig as root

To uninstall the CUDA Toolkit, run cuda-uninstaller in /home/<USER>/cuda/12.1/bin
To uninstall the NVIDIA Driver, run nvidia-uninstall
Logfile is /var/log/cuda-installer.log



wget https://chpc.bj.bcebos.com/packages/build_shell/build_cuda_modulefile.sh
CUDA_DIR="/home/<USER>/cuda/12.1"
MODULEFILE_VERSION="12.1"
MODULE_HOME=/home/<USER>/modules/modulefiles
MODULEFILE_DIR="$MODULE_HOME/cuda"

mkdir -p $MODULEFILE_DIR
bash build_cuda_modulefile.sh "$CUDA_DIR" "$MODULEFILE_VERSION" > $MODULEFILE_DIR/$MODULEFILE_VERSION
echo -e "#%Module\n\nset ModulesVersion $MODULEFILE_VERSION" > $MODULEFILE_DIR/.version

# 测试
module load cuda
nvcc -V

# 打包
cd /home/<USER>/modules/modulefiles/
tar -zcvf cuda-12.1-module-ubuntu.tar.gz cuda

cd /home/<USER>/
tar -zcvf cuda-12.1-ubuntu.tar.gz cuda


cd /home/<USER>/modules/modulefiles/
tar -zcvf cuda-12.1-module-centos.tar.gz cuda

cd /home/<USER>/
tar -zcvf cuda-12.1-centos.tar.gz cuda

```

# 安装 isaac-gym

```sh
ret_code=$(check_env)
if [[ $ret_code == $REDHAT ]]; then
else
    if [[ $ret_code == $UBUNTU2204 ]]; then
        pip install --force-reinstall numpy==1.20.3  -i https://pypi.mirrors.ustc.edu.cn/simple
    else fi [[ $ret_code == $UBUNTU2004 ]]; then
        pip install --force-reinstall numpy==1.20.0  -i https://pypi.mirrors.ustc.edu.cn/simple
        pip install pandas==1.4.4 -i https://pypi.mirrors.ustc.edu.cn/simple
    fi
fi
# 安装 conda
wget https://chpc-dev.bj.bcebos.com/robotsim/Miniconda3-latest-Linux-x86_64.sh
bash Miniconda3-latest-Linux-x86_64.sh -p $BASE_INSTALL_DIR/miniconda3

# 创建 conda 环境
conda create -n rlgpu python=3.8.18
# 新开一个 shell
conda activate rlgpu

# 安装 pytorch
conda install -f pytorch==2.2.2 torchvision==0.17.2 torchaudio==2.2.2 pytorch-cuda=12.1 -c pytorch -c nvidia
pip install pyyaml scipy tensorboard -i https://pypi.mirrors.ustc.edu.cn/simple
pip install pybind11>=2.5.0

# 安装 isaacgym
wget https://chpc-dev.bj.bcebos.com/robotsim/isaacgym.tar.pigz -C $BASE_INSTALL_DIR/isaacgym.tar.pigz 
cd $BASE_INSTALL_DIR
tar -zxf isaacgym.tar.pigz
cd isaacgym/python && pip install -e .

# 安装 isaac gym envs
wget https://chpc-dev.bj.bcebos.com/robotsim/IsaacGymEnvs.tar.pigz -C $BASE_INSTALL_DIR/IsaacGymEnvs.tar.pigz
cd $BASE_INSTALL_DIR
tar -zxf IsaacGymEnvs.tar.pigz
cd IsaacGymEnvs-release-1.5.1 && pip install -i https://pypi.mirrors.ustc.edu.cn/simple -e .

cd /home/<USER>/IsaacGymEnvs-release-1.5.1/isaacgymenvs && python train.py task=ShadowHand headless=True max_iterations=10 num_envs=32768 
```

```sh
#%Module 1.0
#
#  isaac-gym module for use with 'environment-modules' package:

proc ModulesHelp { } {
    puts stderr "\t isaac-gym/1.0rc4"
}

module-whatis "For more information, $ module help isaac-gym/1.0rc4"

setenv load_gym_env "source /home/<USER>/miniconda3/conda.sh && conda activate rlgpu"

```

## pbs 编译
```sh
# hwloc 打包
wget https://chpc-dev.bj.bcebos.com/image-build/hwloc-hwloc-2.8.0.tar.gz
tar -xf hwloc-hwloc-2.8.0.tar.gz
cd hwloc-hwloc-2.8.0
./autogen.sh
./configure --prefix=/opt/hwloc/2.8.0
make
make install

cd /opt
os=ubuntu
tar -zcvf hwloc-$os.tar.gz hwloc
mv hwloc-$os.tar.gz ~/
cp -rf /opt/hwloc/2.8.0/* /usr
```

```sh
sed -i "s/\#\$nrconf{restart} = 'i'/\$nrconf{restart} = 'l'/" /etc/needrestart/needrestart.conf
apt-get update --fix-missing
apt install -y gcc make libtool libx11-dev \
      libxt-dev libedit-dev libical-dev ncurses-dev perl \
      postgresql-server-dev-all postgresql-contrib python3-dev tcl-dev tk-dev swig \
      libexpat-dev libssl-dev libxext-dev libxft-dev autoconf \
      automake g++
apt install -y expat libedit2 postgresql python3 postgresql-contrib sendmail-bin \
      sudo tcl tk libical3 postgresql-server-dev-all
```
```sh
yum install -y gcc make rpm-build libtool \
      libX11-devel libXt-devel libedit-devel libical-devel \
      ncurses-devel perl postgresql-devel postgresql-contrib python3-devel tcl-devel \
      tk-devel swig expat-devel openssl-devel libXext libXft \
      autoconf automake gcc-c++

yum install -y expat libedit postgresql-server postgresql-contrib python3 \
      sendmail sudo tcl tk libical chkconfig cjson

wget https://github.com/openpbs/openpbs/archive/refs/tags/v23.06.06.tar.gz
tar -zxvf v23.06.06.tar.gz
cd openpbs-23.06.06
./autogen.sh
./configure --prefix=/opt/pbs
make
make install
/opt/pbs/libexec/pbs_postinstall
# 打包/opt/pbs 和 /etc/init.t/pbs
```

install-client.sh
```sh
#!/bin/bash
cp -rf pbs /opt
cp pbs-client.conf /etc/pbs.conf
echo "PATH=$PATH:/opt/pbs/bin" >> ~/.bashrc
```

intall-server.sh
```sh
#!/bin/bash
cp -rf pbs /opt
cp pbs-server.conf /etc/pbs.conf
cp pbs-script /etc/init.d/pbs
chmod +x /etc/init.d/pbs

/opt/pbs/libexec/pbs_postinstall
systemctl daemon-reload
systemctl enable pbs
systemctl start pbs

echo "PATH=$PATH:/opt/pbs/bin" >> ~/.bashrc
```

intall-mon.sh
```sh
#!/bin/bash
cp -rf pbs /opt
cp pbs-mon.conf /etc/pbs.conf
cp pbs-script /etc/init.d/pbs
chmod +x /etc/init.d/pbs

/opt/pbs/libexec/pbs_postinstall
systemctl daemon-reload
systemctl enable pbs
systemctl start pbs

echo "PATH=$PATH:/opt/pbs/bin" >> ~/.bashrc
```

### pbs 安装
```sh
wget https://chpc-online.bj.bcebos.com/release/pkg/amd64/operate.sh -O operate.sh && bash operate.sh pbs_23.06.06_amd64 https://chpc-online.bj.bcebos.com amd64 install.sh pbs bj --action installProxyNode --clusterId c-6NpiOZYR --clusterName pbs_test --scheduler pbs --schedulerVersion 23.06.06 --pluginVersion 0.1.0 --userId 1545b9ab1a0f46b2bf5ebf32461987fe --region bj --masterIp ************* --masterHost pbs-manager --mountTarget cfs-fiQIyLZE3i.lb-5be24753.cfs.bj.baidubce.com --mountDir /home --softwareDir /home/<USER>//chpc-online.bj.bcebos.com/release/pkg/amd64/operate.sh -O operate.sh && bash operate.sh environment-modules_4.1.1-1_amd64 https://chpc-online.bj.bcebos.com amd64 install.sh xxx bj
```

```sh
wget https://chpc-online.bj.bcebos.com/release/pkg/amd64/operate.sh -O operate.sh && bash operate.sh pbs_23.06.06_amd64 https://chpc-online.bj.bcebos.com amd64 install.sh pbs bj --action installProxyNode --clusterId c-6NpiOZYR --clusterName pbs_test --scheduler pbs --schedulerVersion 23.06.06 --pluginVersion 0.1.0 --userId 1545b9ab1a0f46b2bf5ebf32461987fe --region bj --masterIp ************* --masterHost pbs-manager --mountTarget cfs-fiQIyLZE3i.lb-5be24753.cfs.bj.baidubce.com --mountDir /opt/data/home --softwareDir /opt/data/home/<USER>//chpc-online.bj.bcebos.com/release/pkg/amd64/operate.sh -O operate.sh && bash operate.sh environment-modules_4.1.1-1_amd64 https://chpc-online.bj.bcebos.com amd64 install.sh xxx bj /opt/data/home/<USER>

# 使用自定义路径安装 openmpi
mkdir -p /opt/chpc/download && rm -rf /opt/chpc/download/openmpi_4.1.5_amd64* && wget http://chpc-online.bj.bcebos.com/release/pkg/amd64/openmpi_4.1.5_amd64.tar.gz -O /opt/chpc/download/openmpi_4.1.5_amd64.tar.gz && tar -zxvf /opt/chpc/download/openmpi_4.1.5_amd64.tar.gz -C /opt/chpc/download && pushd /opt/chpc/download/openmpi_4.1.5_amd64 && bash install.sh pbs bj /opt/data/home/<USER>

wget https://chpc-online.bj.bcebos.com/release/pkg/amd64/operate.sh -O operate.sh && bash operate.sh openmpi_4.1.5_amd64 https://chpc-online.bj.bcebos.com amd64 install.sh xxx bj /opt/data/home/<USER>

wget https://chpc-online.bj.bcebos.com/release/pkg/amd64/operate.sh -O operate.sh && bash operate.sh openmpi_4.1.5_amd64 https://chpc-online.bj.bcebos.com amd64 uninstall.sh xxx bj /opt/data/home/<USER>

```


# sge 编译
```sh
mkdir -p /opt/downloads
tar -zxvf 代码库.tar.gz
cd image && bash build_sge_image.sh

```
## sge 打包
```
cd /opt/sge
tar -zcvf sge-r3.94f34eb-centos79.tar.gz sge

cd /usr/local/bin
tar -zcvf sge-hwloc-2.8.0-centos79.tar.gz *


cd /usr/local/lib
tar -zcvf sge-libhwloc-2.8.0-centos79.tar.gz *

```


## sge 安装

```环境安装
wget https://chpc-online.bj.bcebos.com/release/pkg/amd64/operate.sh -O operate.sh && bash operate.sh sge_r3.94f34eb_amd64 https://chpc-online.bj.bcebos.com amd64 install.sh sge bj --region bj```
```

# openldap 编译
```sh
mkdir -p /opt/downloads
tar -zxvf 代码库.tar.gz
cd ldap && bash build_ldap.sh

```
## openldap 打包
```
cd /opt/
tar -czvf openldap_2.5.13_amd64_ubuntu2204.tar.gz ldap cyrus-sasl

```


## openldap 安装

```管理节点
wget https://chpc-online.bj.bcebos.com/release/pkg/amd64/operate.sh -O operate.sh && bash operate.sh openldap_2.5.13_amd64 https://chpc-online.bj.bcebos.com amd64 install.sh sge bj --master_host sgeimagetest --node_type master  --region bj```

```计算节点
wget https://chpc-online.bj.bcebos.com/release/pkg/amd64/operate.sh -O operate.sh && bash operate.sh openldap_2.5.13_amd64 https://chpc-online.bj.bcebos.com amd64 install.sh sge bj --master_host sgeimagetest --node_type compute  --region bj```

```登录节点
wget https://chpc-online.bj.bcebos.com/release/pkg/amd64/operate.sh -O operate.sh && bash operate.sh openldap_2.5.13_amd64 https://chpc-online.bj.bcebos.com amd64 install.sh sge bj --master_host sgeimagetest --node_type login  --region bj```
```

# chpc-app-server 编译
```sh
https://console.cloud.baidu-int.com/devops/icode/repos/baidu/hpc/chpc-app-server/tree/master代码库编译的二进制产出

```
## chpc-app-server 打包
```
将chpc-app-server下的内容和二进制一块打包

```

## chpc-app-server 安装启动

```sh
# 安装
wget https://chpc-online.bj.bcebos.com/release/pkg/amd64/operate.sh -O operate.sh && bash operate.sh chpc-app-server_1.0.1_amd64 https://chpc-online.bj.bcebos.com amd64 install.sh pbs bj --region bj --scheduler pbs --port 12012 --cluster_id c-uUXD5ZYh --version 1.0.1 --eip *************
# 卸载
wget https://chpc-online.bj.bcebos.com/release/pkg/amd64/operate.sh -O operate.sh && bash operate.sh chpc-app-server_1.0.1_amd64 https://chpc-online.bj.bcebos.com amd64 uninstall.sh pbs bj
```
# slurm 编译
centos7 直接使用 build_slurm.sh 编译
centos8 要先装下面的依赖，然后用 build_slurm_centos8.sh 编译

```sh
git clone --depth 1 --single-branch -b v2.9.4 https://github.com/nodejs/http-parser.git http_parser
cd http_parser
make
sudo make install

git clone --depth 1 --single-branch -b 0.2.5 https://github.com/yaml/libyaml libyaml
cd libyaml
./bootstrap
./configure
make
sudo make install

yum install -y openssl-devel openssl
wget https://github.com/dun/munge/archive/refs/tags/munge-0.5.15.tar.gz
tar -xzvf munge-0.5.15.tar.gzcd
cd munge-0.5.15
./bootstrap
./configure
make && make install
# ./configure --prefix=/usr/local/munge \
# --sysconfdir=/etc/munge \
# --localstatedir=/usr/local/munge/local \
# --with-runstatedir=/usr/local/munge/run \
# --libdir=/usr/local/munge/lib64
# make && make install

# ln -fs /usr/local/munge/etc/rc.d/init.d/munge /etc/init.d/munge
# ln -fs /usr/local/munge/sbin/munged /usr/sbin/munged

# useradd -s /sbin/nologin -u 601 munge
# chown -R root.root /usr/local
# chmod -R 755 /usr/local
# mkdir -p /usr/local/munge/run/munge
# chown -R munge.munge /usr/local/munge/
# chown -R munge.munge /etc/munge/cd
# chmod 700 /etc/munge/
# chmod 711 /usr/local/munge/local/
# chmod 755 /usr/local/munge/run
# chmod 711 /usr/local/munge/lib

# sudo -u munge /usr/local/munge/sbin/mungekey --verbose
# chmod 600 /etc/munge/munge/munge.key

# cp -rf /usr/local/munge/lib/systemd/system/munge.service /usr/lib/systemd/system/
# systemctl daemon-reload
# systemctl start munge
# systemctl status munge
```