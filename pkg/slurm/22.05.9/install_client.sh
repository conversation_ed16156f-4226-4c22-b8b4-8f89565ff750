#!/bin/bash
set -x

CURRENT_DIR=$(cd $(dirname $0); pwd)

# slurm安装路径
SLURM_INSTALL_DIR="/opt/slurm/22.05.9"
# slurm conf path
SLURM_SERVICE_FILES="$CURRENT_DIR/service/*.service"
#munge.key path
MUNGE_KEY_FILE="$CURRENT_DIR/conf/munge.key"
# pmix安装路径
PMIX_INSTALL_DIR="/opt/pmix/3.1.6"

# # slurm安装
cp -r $CURRENT_DIR/pmix /opt/
cp -r $CURRENT_DIR/hwloc /opt/
cp -r $CURRENT_DIR/slurm /opt/
echo PATH=$PMIX_INSTALL_DIR/bin:$SLURM_INSTALL_DIR/bin:$SLURM_INSTALL_DIR/sbin:\$PATH >> /etc/profile
echo PATH=$SLURM_INSTALL_DIR/bin:$SLURM_INSTALL_DIR/sbin:\$PATH >> ~/.bashrc
source /etc/profile
source ~/.bashrc
cp $SLURM_SERVICE_FILES /usr/lib/systemd/system/
# munge.key 替换, md5: 34c98e1170201c57e9faca1bafa9344c
# slurm 集群依赖munge做权限校验，只有munge.key一致才能成功将新节点加入到集群中
mkdir -p /etc/munge
cp -f $MUNGE_KEY_FILE /etc/munge/
chown munge:munge /etc/munge/munge.key
chmod 600 /etc/munge/munge.key