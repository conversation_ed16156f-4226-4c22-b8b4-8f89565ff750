#!/bin/bash
set -x

schedulerVersion=$1
region=$2
software_dir=$3
scripts=$4

# hwloc安装路径
HWLOC_INSTALL_DIR="/opt/hwloc/2.8.0"
# pmix安装路径
PMIX_INSTALL_DIR="/opt/pmix/3.1.6"

scheduler="slurm"

if [ -z "$schedulerVersion" ]; then
    echo "args schedulerVersion is empty."
    exit 1
fi

if [ -z "$region" ]; then
    echo "args region is empty."
    exit 1
fi

source check_env.sh
source get_bos_endpoint.sh
bos_endpoint="$(get_bos_endpoint $region)/release/pkg/amd64/pkg"

downloadDir=/opt/chpc/download
mkdir -p $downloadDir
cd $downloadDir

# 安装基础软件
ret_code=$(check_env)
if [[ $ret_code == $REDHAT ]]; then
    os="centos7.9"
    if cat /etc/redhat-release | grep -q "8.4"; then
        os="centos8.4"
    fi
    # 安装nfs
    yum -y install nfs-utils
    systemctl stop rpcbind
    systemctl stop nfs
    systemctl disable nfs
    systemctl disable rpcbind

    if [[ $os == "centos7.9" ]]; then
        yum install -y nc lz4-devel numactl-devel hwloc-devel libevent libevent-devel environment-modules libyaml libyaml-devel libjwt libjwt-devel http-parser http-parser-devel munge munge-devel munge-libs hdf5 hdf5-devel json-c json-c-devel python3 gcc-c++ libtool-ltdl-devel
    elif [[ $os == "centos8.4" ]]; then
        yum install -y lz4-devel numactl-devel libevent libevent-devel environment-modules libjwt libjwt-devel munge munge-libs hdf5 hdf5-devel json-c json-c-devel python3 gcc-c++ libtool-ltdl-devel readline-devel --skip-broken
        chown -R munge: /etc/munge/ /var/log/munge/ /var/lib/munge/ /var/run/munge/
        
    fi
    # mysql安装
    wget $bos_endpoint/mysql-community-client-8.0.31-1.el7.x86_64.rpm
    wget $bos_endpoint/mysql-community-common-8.0.31-1.el7.x86_64.rpm
    wget $bos_endpoint/mysql-community-libs-compat-8.0.31-1.el7.x86_64.rpm
    wget $bos_endpoint/mysql-community-server-8.0.31-1.el7.x86_64.rpm
    wget $bos_endpoint/mysql-community-client-plugins-8.0.31-1.el7.x86_64.rpm
    wget $bos_endpoint/mysql-community-icu-data-files-8.0.31-1.el7.x86_64.rpm
    wget $bos_endpoint/mysql-community-libs-8.0.31-1.el7.x86_64.rpm
    yum install -y $downloadDir/mysql-*.rpm
    # centos需要执行,ubuntu不需要
    create-munge-key -f
elif [ $ret_code == $UBUNTU2004 ]; then
    os="ubuntu20.04"
    # 安装nfs
    apt update && apt install -y nfs-common nfs-kernel-server
    systemctl stop rpcbind
    systemctl stop nfs-server
    systemctl disable rpcbind
    systemctl disable nfs-server
    apt update && apt-get -y install netcat vim unzip git build-essential libssl-dev libmysql++-dev libnuma-dev environment-modules cmake libpmi-pmix-dev hwloc libevent-dev libhwloc-dev libjwt-dev libpmix2 libpmix-dev libmunge-dev libjson-c-dev libhttp-parser-dev libyaml-dev libhdf5-dev munge
    # mysql安装
    apt -yq install mysql-server
elif [ $ret_code == $UBUNTU2204 ]; then
    os="ubuntu22.04"
    # 禁止弹出交互窗口，默认不重启服务，仅列出可选项
    sed -i "s/\#\$nrconf{restart} = 'i'/\$nrconf{restart} = 'l'/" /etc/needrestart/needrestart.conf
    # 安装nfs
    apt update && sudo DEBIAN_FRONTEND=noninteractive apt install -y netcat nfs-common nfs-kernel-server
    systemctl stop rpcbind
    systemctl stop nfs-server
    systemctl disable rpcbind
    systemctl disable nfs-server
    apt update && apt-get -y install git build-essential libssl-dev libmysql++-dev libnuma-dev environment-modules cmake hwloc libevent-dev libhwloc-dev libjwt-dev libpmix2 libpmix-dev libmunge-dev libjson-c-dev libhttp-parser-dev libyaml-dev libhdf5-dev munge
    # {slurm_base}/lib/slurm/cgroup_v2.so need libdbus-1-dev
    apt-get -y install libdbus-1-de
    # mysql安装
    apt -yq install mysql-server
else
    echo "unexpected os,only support centos or ubuntu,exit!!!"
    return 1
fi

# 安装slurm
# 下载安装包
cd $downloadDir
echo "=====>Download install package $scheduler $schedulerVersion $os"

pkgFile="$scheduler-$schedulerVersion-$os"
dstFile=$downloadDir/$pkgFile.tar.gz

if [ -f $dstFile ]; then
    rm $dstFile
fi
wget $bos_endpoint/$pkgFile.tar.gz -O $dstFile

if [ -d $downloadDir/$pkgFile ]; then
    rm -rf $downloadDir/$pkgFile
fi
tar -zxvf $dstFile -C $downloadDir

# 安装
cd $downloadDir/$pkgFile
bash $scripts
if [ $? -ne 0 ]; then
    echo "Error: Install client failed."
    exit 1
fi
echo "=====>Install install package $scheduler $schedulerVersion $os success."

if [ $ret_code == $REDHAT ]; then
    systemctl disable mysqld
    systemctl stop mysqld
elif [ $ret_code == $UBUNTU2004 ]; then
    systemctl disable mysql
    systemctl stop mysql
fi
systemctl stop mysqlrouter
systemctl stop munge
systemctl disable mysqlrouter
systemctl disable slurmctld
systemctl disable slurmd
systemctl disable slurmdbd
systemctl disable slurmrestd
systemctl disable munge

# 安装 environment module
# wget https://chpc-online-bjtest.bj.bcebos.com/release/pkg/amd64/operate.sh -O operate.sh && bash operate.sh environment-modules_4.1.1-1_amd64 https://chpc-online-bjtest.bj.bcebos.com amd64 install.sh xxx bjtest /home/<USER>
operate_shell="$(get_bos_endpoint $region)/release/pkg/amd64/operate.sh"
wget $operate_shell && bash operate.sh environment-modules_4.1.1-1_amd64 $(get_bos_endpoint $region) amd64 install.sh xxx $region $software_dir