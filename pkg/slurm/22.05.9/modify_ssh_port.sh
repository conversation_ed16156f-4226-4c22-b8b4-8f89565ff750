#!/bin/bash

set -x

# 新的SSH端口号
newPort=$1

# 检查是否以root身份运行
if [ "$EUID" -ne 0 ]; then
  echo "请以root身份运行此脚本"
  exit 1
fi

# 检查是否提供了新的端口号
if [ -z "$newPort" ]; then
  echo "用法: $0 <新的SSH端口号>"
  exit 1
fi

# 备份原始的sshd_config文件
cp /etc/ssh/sshd_config /etc/ssh/sshd_config.bak

# 修改sshd_config文件中的端口号
sed -i "s/^#Port 22/Port $newPort/" /etc/ssh/sshd_config
sed -i "s/^Port [0-9]*/Port $newPort/" /etc/ssh/sshd_config

# 重启SSH服务以应用更改
if systemctl is-active --quiet sshd; then
  systemctl restart sshd
else
  service ssh restart
fi