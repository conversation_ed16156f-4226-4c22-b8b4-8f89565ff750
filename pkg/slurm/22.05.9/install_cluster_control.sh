#!/bin/bash
set -x

region=$1

source get_bos_endpoint.sh
bos_endpoint="$(get_bos_endpoint $region)/release/pkg/amd64"

downloadDir=/opt/chpc
mkdir -p $downloadDir
# 下载 cluster_control
dstFile=$downloadDir/cluster_control
if [ -f $dstFile ]; then
	rm $dstFile
fi
wget $bos_endpoint/cluster_control -O $dstFile
chmod +x $dstFile

# 下载 cluster_control.sh
dstFile=$downloadDir/cluster_control.sh
if [ -f $dstFile ]; then
	rm $dstFile
fi
wget $bos_endpoint/cluster_control.sh -O $dstFile
chmod +x $dstFile