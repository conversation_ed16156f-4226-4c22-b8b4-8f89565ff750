#!/bin/bash
set -x

source check_env.sh

# cfs挂载
function mount_cfs() {
    cluster_name_id=$1
    mount_target=$2
    mount_dir=$3
    software_dir=$4

    # 如果 mount_dir 为空，mount_dir 默认为 /home
    if [ -z "$mount_dir" ]; then
        mount_dir="/home"
    fi

    if [ ! -d "$mount_dir" ]; then
        mkdir -p "$mount_dir"
    fi
    umount "$mount_dir"
    chmod -R 777 "$mount_dir"

    # 先将共享存储挂载到/mnt目录，用来在共享存储中创建/chpc/cluster_name#cluster_id/app 和 /chpc/cluster_name#cluster_id/home目录
    temp_mount_dir="/mnt"
    if [ ! -d $temp_mount_dir ]; then
        mkdir -p $temp_mount_dir
    fi
    umount $temp_mount_dir
    mount -t nfs4 -o minorversion=1,rsize=1048576,wsize=1048576,hard,timeo=600,retrans=2,noresvport $mount_target:/ $temp_mount_dir
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "mount cfs failed"
        return 1
    fi

    # 创建共享存储目录
    # local_app_dir="$temp_mount_dir/chpc/$cluster_name_id/app"
    # remote_app_dir="/chpc/$cluster_name_id/app"
    local_home_dir="$temp_mount_dir/chpc/$cluster_name_id/home"
    remote_home_dir="/chpc/$cluster_name_id/home"
    # mkdir -p "$local_app_dir"
    mkdir -p "$local_home_dir"

    # 卸载临时挂载在/mnt的共享存储
    umount $temp_mount_dir

    # 挂载共享存储目录
    # do_mount_cfs "$mount_target":"$remote_app_dir" "/app"
    # ret_code=$?
    # if [ $ret_code -ne 0 ]; then
    #     echo "mount /app cfs failed"
    #     return 1
    # fi
    do_mount_cfs "$mount_target":"/" "/chpcdata"
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "mount /chpcdata cfs failed"
        return 1
    fi
    # *自定义的挂载目录，默认为 /home
    # do_mount_cfs "$mount_target" "$remote_home_dir" "/home"
    do_mount_cfs "$mount_target":"$remote_home_dir" "$mount_dir"
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "mount $mount_dir cfs failed"
        return 1
    fi
    # 在共享存储中创建软件安装目录
    if [ ! -d "$software_dir" ]; then
        mkdir -p "$software_dir"
    fi

    # 为其他用户添加cfs的写权限
    chmod 777 "$mount_dir"
    # chmod 777 "/app"
    # chmod 777 "$local_app_dir"
    # chmod 755 "$local_home_dir"
    chmod 777 "/chpcdata"

}

function do_mount_cfs() {
    remote_mount_target=$1
    local_dir=$2
    shift 2
    do_mount_option=$@

    if [ ! -d "$local_dir" ]; then
        mkdir -p "$local_dir"
    fi
    umount "$local_dir"

    # 挂载 option 为空，则使用默认的挂载参数
    if [ -z "$do_mount_option" ]; then
        do_mount_option="-t nfs4 -o minorversion=1,rsize=1048576,wsize=1048576,hard,timeo=600,retrans=2,noresvport"
    fi
    mount $do_mount_option $remote_mount_target $local_dir

    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "mount $local_dir cfs failed"
        return 1
    fi
    write_mount_info "$remote_mount_target" "$local_dir" "$do_mount_option"
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "write_mount_info $local_dir failed"
        return 1
    fi
}

function write_mount_info() {
    remote_mount_target=$1
    local_mount_dir=$2
    shift 2
    local_mount_option=$@

    match_num=$(grep -c "$remote_mount_target:/  $local_mount_dir" /etc/fstab)
    if [ "$match_num" = 0 ]; then
        if [ -z "$local_mount_option" ]; then
            local_mount_option="minorversion=1,rsize=1048576,wsize=1048576,hard,timeo=600,retrans=2,noresvport"
        else
            # 解析出 "-t nfs4 -o minorversion=1,rsize=1048576,wsize=1048576,hard,timeo=600,retrans=2,noresvport" 中 -o 后面的参数
            local_mount_option=$(echo "$local_mount_option" | awk -F '-o ' '{print $2}')
        fi
        echo "$remote_mount_target  $local_mount_dir  nfs  $local_mount_option  0   0" >>/etc/fstab
    else
        sed -i "s#^$remote_mount_target  $local_mount_dir.*#$remote_mount_target  $local_mount_dir  nfs  minorversion=1,rsize=1048576,wsize=1048576,hard,timeo=600,retrans=2,noresvport  0   0#" /etc/fstab
    fi
    return $?
}

# 非 cfs 挂载
function mount_nfs() {
    cluster_name_id=$1
    mount_target=$2
    mount_dir=$3
    software_dir=$4
    shift 4
    mount_option=$@

    # 如果 mount_dir 为空，mount_dir 默认为 /home
    if [ -z "$mount_dir" ]; then
        mount_dir="/home"
    fi

    if [ ! -d "$mount_dir" ]; then
        mkdir -p "$mount_dir"
    fi
    umount "$mount_dir"
    chmod -R 777 "$mount_dir"

    # 检查mount_target是否包含cfs
    if [[ $mount_target == *"cfs"* ]]; then
        # 使用bash内置字符串替换和参数扩展来提取地址和路径
        # 使用IFS（内部字段分隔符）和read来分割字符串
        IFS=':' read -ra ADDR_PATH <<<"$mount_target"
        cfs_address=${ADDR_PATH[0]}
        cfs_path=${ADDR_PATH[1]}

        echo "CFS地址是: $cfs_address"
        echo "CFS路径是: $cfs_path"
    else
        echo "mount_target中不包含cfs"
    fi
    # *cfs-dWPeNuHgm3.lb-b9f673e8.cfs.bd.baidubce.com:/home/<USER>/data，如果 cfs 上 /home/<USER>/data 没有创建，会失败，这里自动为其创建
    if [ ! -z "$cfs_address" ]; then
        # 先将共享存储挂载到/mnt目录，用来在共享存储中创建/chpc/cluster_name#cluster_id/app 和 /chpc/cluster_name#cluster_id/home目录
        temp_mount_dir="/mnt"
        if [ ! -d $temp_mount_dir ]; then
            mkdir -p $temp_mount_dir
        fi
        umount $temp_mount_dir
        mount -t nfs4 -o minorversion=1,rsize=1048576,wsize=1048576,hard,timeo=600,retrans=2,noresvport $cfs_address:/ $temp_mount_dir
        ret_code=$?
        if [ $ret_code -ne 0 ]; then
            echo "mount cfs failed"
            return 1
        fi

        # 创建共享存储目录
        local_home_dir="$temp_mount_dir/$cfs_path"
        remote_home_dir="$cfs_path"
        mkdir -p "$local_home_dir"

        # 卸载临时挂载在/mnt的共享存储
        umount $temp_mount_dir

    fi
    # *自定义的挂载目录，默认为 /home
    do_mount_cfs "$mount_target" "$mount_dir" "$mount_option"
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "mount $mount_dir cfs failed"
        return 1
    fi
    # 在共享存储中创建软件安装目录
    if [ ! -d "$software_dir" ]; then
        mkdir -p "$software_dir"
    fi

    # 为其他用户添加cfs的写权限
    chmod 777 "$mount_dir"
}

# 判读 mount_target 是否为 cfs
function is_cfs() {
    # 如果 mount_target 以cfs开头，以 baidubce.com 或者 bce.sandbox.baidu.com 结尾，则认为 mount_target 为 cfs
    if [[ $1 =~ ^cfs.*(baidubce\.com|bce\.sandbox\.baidu\.com)$ ]]; then
        return 0
    else
        return 1
    fi
}

ret_code=$(check_env)
if [[ $ret_code == $REDHAT ]]; then
    yum install -y nfs-utils jq
else
    sudo DEBIAN_FRONTEND=noninteractive apt install -y nfs-kernel-server jq
    # 删除has
    pkill -f "/opt/avalokita/bin/avalokita --update-url=http://has-master-a.sdns.baidu.com/download/qa"
    bash /home/<USER>/has/has_control stop
    sed -i --follow-symlinks "/has_control restart/d" /etc/rc.d/rc.local
    sed -i --follow-symlinks "/has_control restart/d" /etc/rc.local
    update-rc.d -f has-agent remove
    rm -rf /home/<USER>/has
    rm -rf /home/<USER>/has-agent
fi

cluster_id=$1
software_dir=$2
shared_storage_list=$3
echo $shared_storage_list | tr -d '\\' | jq -r '.[] | "\(.storage_protocol)\t\(.mount_target)\t\(.mount_dir)\t\(.mount_option | rtrimstr("\""))"' | while IFS=$'\t' read -r protocol target dir option; do
    echo "MountProtocol: $protocol"
    echo "MountTarget: $target"
    echo "MountDir: $dir"
    echo "MountOption: $option"
    # 如果 $option 为 "null"，则将其设置为空字符串
    if [ "$option" == "null" ]; then
        option=""
    fi
    # 将 option 中的^替换为空格
    option=$(echo "$option" | tr '^' ' ')
    echo "MountOption: $option"
    if [ "$protocol" == "nfs" ]; then
        is_cfs "$target"
        if [ $? -eq 0 ]; then
            echo "mount target is cfs"
            mount_cfs $cluster_id $target $dir $software_dir
            ret_code=$?
            if [ $ret_code -ne 0 ]; then
                echo "mount $mount_dir cfs failed"
                exit 1
            fi
        else
            echo "mount target is not cfs"
            mount_nfs $cluster_id $target $dir $software_dir $option
            if [ $ret_code -ne 0 ]; then
                echo "mount $mount_dir nfs failed"
                exit 1
            fi
        fi
    fi
    echo "-----------mount done----------"
done
