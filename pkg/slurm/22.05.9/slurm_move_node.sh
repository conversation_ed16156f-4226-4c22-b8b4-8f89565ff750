#!/bin/bash

set -x

newQueue=$1
node_name=$(hostname)

# 检查节点是否存在，如果存在先删除
if /opt/slurm/22.05.9/bin/sinfo -N | grep -q "$node_name"; then
    # 停止节点调度
    /opt/slurm/22.05.9/bin/scontrol update NodeName=$node_name State=DRAIN Reason="move node"
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "scontrol update node drain failed"
        exit 1
    fi

    # 取消节点任务
    /opt/slurm/22.05.9/bin/scancel --nodelist=$node_name
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "scancel job failed"
        /opt/slurm/22.05.9/bin/scontrol update NodeName=$node_name State=RESUME
        exit 1
    fi

    # 等待任务取消
    job_count=0
    for((i=0;i<5;i++));
    do
        job_count=$(squeue --nodelist=$node_name | wc -l)
        if [[ $job_count -le 1 ]]; then
            break
        fi
        sleep 1
    done
    if [[ $job_count -gt 1 ]]; then
        echo "Jobs on node did not cancel in time."
        /opt/slurm/22.05.9/bin/scontrol update NodeName=$node_name State=RESUME
        exit 1
    fi

    # 删除节点
    /opt/slurm/22.05.9/bin/scontrol delete NodeName=$node_name
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "delete node failed"
        /opt/slurm/22.05.9/bin/scontrol update NodeName=$node_name State=RESUME
        exit 1
    fi
fi

# 停止服务
ret_code=0
for((i=0;i<5;i++));
do
    systemctl stop slurmd
    systemctl is-active --quiet slurmd
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        break
    fi
    sleep 1
done
if [ $ret_code -eq 0 ]; then
    echo "stop slurmd over time"
    systemctl start slurmd
    /opt/slurm/22.05.9/bin/scontrol update NodeName=$node_name State=RESUME
    exit 1
fi

# 删除旧的slurm文件
rm -rf /var/spool/slurm /var/spool/slurmd

# 修改slurmd服务文件的Feature
sed -i "s/\(Feature=\)[^\" ]*/\1feature_$newQueue/" /usr/lib/systemd/system/slurmd.service

# 重启服务
systemctl daemon-reload
systemctl start slurmd
exit 0