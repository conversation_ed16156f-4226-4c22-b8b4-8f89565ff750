#!/bin/bash

if [ "$#" -lt 1 ]; then
    echo "args is empty."
    exit 1
fi

args=("$@")

for arg in "${args[@]}"; do
    # 查看是否能 ping 通
    ping -c 1 $arg >/dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "connect to $arg success"
    else
        echo "Error: connect to $arg failed"
        exit 1
    fi
    # 查看是否内网解析
    real_domain="$arg"
    while true; do
        # 查询域名的CNAME记录
        cname=$(dig +short "$real_domain" CNAME)
        # 如果没有CNAME记录，则跳出循环
        if [ -z "$cname" ]; then
            break
        fi
        # 更新实际解析的域名
        real_domain="$cname"
    done
    # 判断域名中是否有 shifen
    if echo "$real_domain" | grep -q "shifen"; then
        echo "Error: 非内网解析"
        exit 1
    fi
done