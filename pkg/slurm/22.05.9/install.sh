#!/bin/bash
set -xe

action=""
cluster_id=""
cluster_name=""
scheduler=""
scheduler_version=""
plugin_version=""
user_id=""
region=""
master_ip=false
master_host=false
login_host=""
login_ip=""
mount_target=""
mount_dir=""
software_dir=""
shared_storage_list=""
mount_option=""
node_type=""
cluster_type=""
enable_ha=""
enable_monitor=""
backup_hostnames=""
queue_list=""
ssh_port=""
max_nodes=""
max_cpus=""

source /etc/profile

# 解析shell参数
function parse_args() {
    args=$(getopt -o h:o:t: -l action:,clusterId:,clusterName:,scheduler:,schedulerVersion:,pluginVersion:,userId:,region:,masterIp:,masterHost:,loginHost::,mountTarget:,mountDir:,softwareDir:,mountOption:,sharedStorageList:,nodeType:,clusterType:,enableHa:,enableMonitor:,backupHostnames:,queueList:,sshPort:,maxNodes:,maxCpus: -- "$@")
    if [ $? != 0 ]; then
        echo "Parse error! Terminating..." >&2
        exit 1
    fi
    echo $args
    echo ""
    eval set -- "$args"
    while true; do
        case "$1" in
        --action)
            action="$2"
            shift 2
            ;;
        --clusterId)
            cluster_id="$2"
            shift 2
            ;;
        --clusterName)
            cluster_name="$2"
            shift 2
            ;;
        --scheduler)
            scheduler="$2"
            shift 2
            ;;
        --schedulerVersion)
            scheduler_version="$2"
            shift 2
            ;;
        --pluginVersion)
            plugin_version="$2"
            shift 2
            ;;
        --userId)
            user_id="$2"
            shift 2
            ;;
        --region)
            region="$2"
            shift 2
            ;;
        --downloadScripts)
            download_scripts="$2"
            shift 2
            ;;
        --masterIp)
            master_ip="$2"
            shift 2
            ;;
        --masterHost)
            master_host="$2"
            shift 2
            ;;
        --loginHost)
            login_host="$2"
            shift 2
            ;;
        --mountTarget)
            mount_target="$2"
            shift 2
            ;;
        --mountDir)
            mount_dir="$2"
            shift 2
            ;;
        --softwareDir)
            software_dir="$2"
            shift 2
            ;;
        --sharedStorageList)
            shared_storage_list="$2"
            shift 2
            ;;
        --mountOption)
            # mount_option="$2"
            echo "=========> mount_option: skip"
            shift
            ;;
        --nodeType)
            node_type="$2"
            shift 2
            ;;
        --clusterType)
            cluster_type="$2"
            shift 2
            ;;
        --enableHa)
            enable_ha="$2"
            shift 2
            ;;
        --enableMonitor)
            enable_monitor="$2"
            shift 2
            ;;
        --backupHostnames)
            backup_hostnames="$2"
            shift 2
            ;;
        --queueList)
            queue_list="$2"
            shift 2
            ;;
        --sshPort)
            ssh_port="$2"
            shift 2
            ;;
        --maxNodes)
            max_nodes="$2"
            shift 2
            ;;
        --maxCpus)
            max_cpus="$2"
            shift 2
            ;;
        -h)
            help
            exit 0
            ;;
        --)
            shift
            break
            ;;
        *)
            echo "Parameters error!!!$1"
            exit 1
            ;;
        esac
    done
}

function main() {
    # 将当前脚本拷贝到 /opt/chpc/scripts 目录
    mkdir -p /opt/chpc/scripts
    cp -rf *.sh /opt/chpc/scripts

    echo "exec_func: $action"
    if [ x"$action" == x"installNode" ]; then
        echo $scheduler_version $plugin_version $cluster_id $cluster_name $user_id $region
        # 安装调度器 client
        echo "[step1] install_slurm $scheduler_version $region $software_dir"
        bash install_slurm.sh $scheduler_version $region $software_dir install_client.sh
        if [ $? -ne 0 ]; then
            echo "Error: install_slurm.sh $scheduler_version $region $software_dir failed"
            exit 1
        fi
        # 安装插件
        echo "[step2] install_scheduler_plugin $scheduler $scheduler_version $plugin_version $region"
        bash install_scheduler_plugin.sh $scheduler $scheduler_version $plugin_version $region
        if [ $? -ne 0 ]; then
            echo "Error: install_scheduler_plugin.sh $scheduler $scheduler_version $plugin_version $region failed"
            exit 1
        fi
        # 安装 cluster-api
        if [ x"$login_host" != x"" ]; then
            result=`ping -c 1 $login_host | awk 'NR==1 {match($0, /([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3})/); print substr($0, RSTART, RLENGTH)}'`
            login_ip=$result
        fi
        echo "[step3] install_chpc_server $cluster_id $cluster_name $user_id $region $max_nodes $max_cpus $login_ip"
        bash install_chpc_server.sh $cluster_id $cluster_name $user_id $region $max_nodes $max_cpus $login_ip
        if [ $? -ne 0 ]; then
            echo "Error: install_chpc_server.sh $cluster_id $cluster_name $user_id $region $login_ip failed"
            exit 1
        fi
        # 安装 cluster_control
        echo "[step4] install_cluster_control $region"
        bash install_cluster_control.sh $region
        if [ $? -ne 0 ]; then
            echo "Error: install_cluster_control.sh $region failed"
            exit 1
        fi
        # 如果 sharedStorageList 不为空，则挂载 nfs
        if [ x"$shared_storage_list" != x"" ]; then
            echo "[step5] mount_nfs"
            echo $cluster_id $software_dir "$shared_storage_list"
            bash mount_nfs.sh $cluster_id $software_dir "$shared_storage_list"
            if [ x"$cluster_type" == x"cloud" ]; then
                # 公有云集群挂载 nfs 错误，直接报错，混合云可以后续修复
                if [ $? -ne 0 ]; then
                    echo "Error: mount_nfs $region failed"
                    exit 1
                fi
            fi
        fi
        # 启动节点
        echo "[step6] start_node $region $enable_ha $enable_monitor $scheduler $node_type $cluster_name $cluster_id 
            $master_host $backup_hostnames $shared_storage_list $queue_list $max_nodes $max_cpus"
        bash start_node.sh "$region" "$enable_ha" "$enable_monitor" "$scheduler" "$node_type" "$cluster_name" "$cluster_id" \
            "$master_host" "$login_host" "$backup_hostnames" "[]" "$queue_list" "$max_nodes" "$max_cpus"
        if [ $? -ne 0 ]; then
            echo "Error: start_node.sh failed"
            exit 1
        fi
        # 修改ssh端口
        if [ x"$ssh_port" != x"" ]; then
            echo "[step7] modify_ssh_port $ssh_port"
            bash modify_ssh_port.sh $ssh_port
            if [ $? -ne 0 ]; then
                echo "Error: modify_ssh_port.sh failed"
                exit 1
            fi
        fi
    fi
}

parse_args $@
main
