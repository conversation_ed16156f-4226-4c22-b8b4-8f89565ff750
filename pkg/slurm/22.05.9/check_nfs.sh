#!/bin/bash

mountTarget=$1
mountDir=$2
softwareDir=$3

if [ -z "$mountTarget" ]; then
    echo "args mountTarge is empty."
    exit 1
fi
if [ -z "$mountDir" ]; then
    echo "args mountDir is empty."
    exit 1
fi
if [ -z "$softwareDir" ]; then
    echo "args softwareDir is empty."
    exit 1
fi

if mountpoint -q "$mountDir"; then
    actualMountTarget=$(findmnt -n -o SOURCE --target "$mountDir")
    if echo "$actualMountTarget" | grep -q "$mountTarget"; then
        echo "The directory $mountDir is correctly mounted."
    else
        echo "The directory $mountDir is mounted, but the source is $actualMountTarget instead of $mountTarget."
        exit 1
    fi
else
    echo "The directory $mountDir is not a mountpoint."
    exit 1
fi

if [ ! -d "$softwareDir" ]; then
    # 如果目录不存在，则创建目录
    mkdir -p "$softwareDir"
    echo "Directory '$softwareDir' created."
else
    echo "Directory '$softwareDir' already exists."
fi