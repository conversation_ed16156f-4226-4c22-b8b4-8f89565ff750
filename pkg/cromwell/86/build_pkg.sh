#! /bin/bash
mkdir -p opt/cromwell
mkdir -p opt/netdisk/bin 

cp -rf ../../../../cromwell/* opt/cromwell

pushd opt/cromwell
wget https://chpc-dev.bj.bcebos.com/cromwell/cromwell-86.jar
# wget https://chpc-dev.bj.bcebos.com/cromwell/cromwell_start.sh
# wget https://chpc-dev.bj.bcebos.com/cromwell/cromwell.conf
# wget https://chpc-dev.bj.bcebos.com/cromwell/cromwell.service
# wget https://chpc-dev.bj.bcebos.com/cromwell/cromwell.sh
# wget https://chpc-dev.bj.bcebos.com/cromwell/install.sh
# wget https://chpc-dev.bj.bcebos.com/cromwell/logrorate.sh
popd

pushd opt
wget https://chpc-dev.bj.bcebos.com/cromwell/apaasutil && chmod 777 /opt/apaasutil
popd

pushd opt/netdisk
wget https://chpc-dev.bj.bcebos.com/cromwell/libbndsdk.so
popd

pushd opt/netdisk/bin
wget https://chpc-dev.bj.bcebos.com/cromwell/bndsdk_demo && chmod 777 /opt/netdisk/bin/bndsdk_demo
popd


