#!/bin/bash
set -x

source check_env.sh
source get_bos_endpoint.sh

if [ -f /etc/lsb-release ]; then
    # Ubuntu 系统
    # sudo apt update
    # sudo apt install -y zip
    # sudo apt install -y openjdk-16-jdk
    # sudo apt install -y docker.io

    region=$2
    echo $region
    endpoint=$(get_bos_endpoint $region)

    # 安装jdk
    wget $endpoint/release/pkg/amd64/pkg/jdk-11.0.23_linux-x64_bin.deb
    dpkg -i jdk-11.0.23_linux-x64_bin.deb
    # # 安装dokcer
    # debFile=docker-ubuntu.tar.gz
    # wget $endpoint/release/pkg/amd64/pkg/$debFile
    # tar -zxvf $debFile
    # dpkg -i docker-ubuntu/*.deb

    # echo '{"registry-mirrors":["https://mirror.baidubce.com"]}' | sudo tee /etc/docker/daemon.json
    # sudo gpasswd -a work docker
    # systemctl enable docker
    # systemctl start docker
elif [ -f /etc/redhat-release ]; then
    # CentOS 系统
    # sudo yum install -y zip
    # sudo yum install -y java-11-openjdk
    # yum install -y docker docker-ce docker-ce-cli containerd.io docker-compose-plugin
    # echo '{"registry-mirrors":["https://mirror.baidubce.com"]}' | sudo tee /etc/docker/daemon.json
    # groupadd docker
    # gpasswd -a work docker
    # systemctl enable docker
    # systemctl start docker

    region=$2
    echo $region
    endpoint=$(get_bos_endpoint $region)

    # 安装jdk
    wget $endpoint/release/pkg/amd64/pkg/jdk-11.0.23_linux-x64_bin.rpm
    rpm -ivh jdk-11.0.23_linux-x64_bin.rpm
    
fi
# cromwell通用安装。cromwell.zip内包括:cromwell-86.jar、cromwell_start.sh、cromwell.conf、cromwell_service、cromwell.sh、install.sh、logrorate.sh
# mkdir /opt/cromwell && cd /opt/cromwell && wget https://chpc-dev.bj.bcebos.com/cromwell/cromwell.zip && unzip cromwell.zip && rm cromwell.zip

# cd /opt && wget https://chpc-dev.bj.bcebos.com/cromwell/apaasutil&& chmod 777 /opt/apaasutil
# cd /opt && mkdir netdisk && mkdir netdisk/bin
# cd /opt/netdisk && wget https://chpc-dev.bj.bcebos.com/cromwell/libbndsdk.so
# cd /opt/netdisk/bin && wget https://chpc-dev.bj.bcebos.com/cromwell/bndsdk_demo && chmod 777 /opt/netdisk/bin/bndsdk_demo

cp -rf opt/* /opt
cd /opt/cromwell
bash cromwell.sh install_cromwell $@
