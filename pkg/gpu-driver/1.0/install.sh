#! /bin/bash
set -x
set -e


DRIVER_VERSION=""
CUDA_VERSION=""
CUDNN_VERSION=""


args=`getopt -l gpuDriverVersion:,cudaVersion:,cudnnVersion: -- "$@"`
if [ $? != 0 ] ; then
    echo "Parse error! Terminating..." >&2 ;
    exit 1 ;
fi
#echo $args
eval set -- "$args"
while true ; do
    case "$1" in
        --gpuDriverVersion) DRIVER_VERSION="$2"; shift 2;;
        --cudaVersion) CUDA_VERSION="$2"; shift 2;;
        --cudnnVersion) CUDNN_VERSION="$2"; shift 2;;
        --) shift ; break ;;
        *) echo "Parameters error!!!$1" ; exit 1 ;;
    esac
done

WORK_DIR="/opt/chpc/download/auto_install"
SCRIPT_URL="http://mirrors.baidubce.com/nvidia-binary-driver/api/auto_install.sh"

mkdir -p ${WORK_DIR}
pushd ${WORK_DIR}
for ((i=0; i<120; i++))
do
    wget --timeout=10 -t 10 ${SCRIPT_URL}
    if [ $? -eq 0 ]; then
        break
    else
        sleep 1
    fi
done
bash ${WORK_DIR}/auto_install.sh ${DRIVER_VERSION} ${CUDA_VERSION} ${CUDNN_VERSION}
popd
rm -rf ${WORK_DIR}

cmdline=$(cat /proc/cmdline)
if [[ "${cmdline}" =~ "pci=realloc" ]]; then
    echo "remove 'pci=realloc' cmdline arg and update grub"
    default_grub_arg="/etc/default/grub"
    sed -i 's/pci=realloc//g' ${default_grub_arg}
    if command -v grub2-mkconfig; then
        efi_grub_cfg=/boot/efi/EFI/centos/grub.cfg
        if [ -f /boot/efi/EFI/rocky/grub.cfg ]; then
            efi_grub_cfg=/boot/efi/EFI/rocky/grub.cfg
        fi
        grub2-mkconfig -o $efi_grub_cfg
    fi
    if command -v update-grub; then
        update-grub
    fi
    reboot
else
    echo "there is no 'pci=realloc' arg in current cmdline, do nothing"
fi