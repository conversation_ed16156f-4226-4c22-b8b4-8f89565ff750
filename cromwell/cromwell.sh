#!/bin/bash
set -x

APP_NAME="cromwell-86.jar"
FILE="/sys/fs/cgroup/memory/cromwell"
readonly CHECK_ALIVE_INTERVAL="5s"
CROMWELL="/opt/cromwell"
IS_SGE="$2"

install_cromwell() {
    # 检查是否存在指定的任务
    if ! grep -q '/bin/sh /opt/cromwell/logrorate.sh' /etc/crontab; then
        # 如果任务不存在，添加它
        echo "*/30 * * * * root /bin/sh /opt/cromwell/logrorate.sh" | sudo tee -a /etc/crontab > /dev/null
    fi
    if is_active
        then
            return 0
        fi
    if [ "$IS_SGE" = "sge" ];
        then
            sed -i -e 's/default = "SLURM"/default = "SGE"/g' /opt/cromwell/cromwell.conf
        fi
    # 设置开机自启动
    cp -a $CROMWELL/cromwell.service /usr/lib/systemd/system/
    systemctl enable cromwell
    systemctl start cromwell
    sleep $CHECK_ALIVE_INTERVAL
    server_status=`systemctl status cromwell | grep Active | awk '{print $3}' | cut -d "(" -f2 | cut -d ")" -f1`
    if [ "$server_status" != "running" ]
    then
      echo "start cromwell failed"
      return 1
    fi
    return 0
    if [ -d "$FILE" ]; then
        return 0
    else
        mkdir $FILE
        # 设置 cgroup 生效的 pid
        CUR_PIDS=$(ps -ef | grep -w "$APP_NAME" |  grep -v grep | awk '{print $2}')
        echo CUR_PIDS | tee $FILE/tasks
        # 设置内存限制 12*1024*1024*1024=12884901888
        echo 12884901888| tee $FILE/memory.limit_in_bytes
        return 0
    fi
}

#是否已经启动cromwell
is_active() {
    ACT_PIDS=$(ps -ef | grep -w "$APP_NAME" |  grep -v grep | awk '{print $2}')
	ARR_PIDS=($ACT_PIDS)
	COUNT=${#ARR_PIDS[@]}
	if [ $COUNT -gt 0 ]
	then
	    return 0
	fi
	return 1
}

uninstall_cromwell() {
    # 关闭开机自启动
    systemctl stop cromwell
    systemctl disable cromwell
    sleep $CHECK_ALIVE_INTERVAL
    server_status=`systemctl is-active cromwell`
    enabled_status=`systemctl is-enabled cromwell`
    sleep $CHECK_ALIVE_INTERVAL
    if [ "$server_status" != "inactive" ] || [ "$enabled_status" != "disabled" ]
    then
      echo "stop cromwell failed"
      return 1
    fi
    return 0
}


# 判断传入的参数并执行相应的函数
if [ "$1" = "install_cromwell" ]; then
    install_cromwell
elif [ "$1" = "uninstall_cromwell" ]; then
    uninstall_cromwell
elif [ "$1" = "is_active" ]; then
    is_active
else
    echo "Invalid option"
fi