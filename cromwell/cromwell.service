[Unit]
Description=cromwell
After=network-online.target
Wants=network-online.target
ConditionPathExists=/opt/cromwell/cromwell.conf

[Service]
Type=simple
ExecStart=/bin/bash /opt/cromwell/cromwell_start.sh
ExecReload=/bin/kill -HUP $MAINPID
LimitNOFILE=65536
TasksMax=infinity
Restart=always
RestartSec=3

# Uncomment the following lines to disable logging through journald.
# NOTE: It may be preferable to set these through an override file instead.
#StandardOutput=null
#StandardError=null

[Install]
WantedBy=multi-user.target