version 1.0
workflow myWorkflow {
    input {
        String guest = "input"
    }
    call myTask {
        input:
            who = guest 
    }
}
task myTask {
    input {
        String who
        # Int i = 0
        # Float f = 27.3
        # Boolean b = true
    }
    command {
        echo "hello world"
    }
    runtime {
        memory: "100 MB"
        cpu: "1"
        queue: "default_queue"
        local_upload_address: "/chpcdata/output.fasta"
        netdisk_appId: "42098601"
        upload_address: "netdisk://test/output.fasta"
        remote_address: "netdisk://test/input.json"
        local_address: "/chpcdata/"
        netdisk_spaceToken: "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
    }
    output {
        String out = "${who}"
    }
}