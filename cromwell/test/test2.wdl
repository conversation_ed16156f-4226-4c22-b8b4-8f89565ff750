version 1.0
workflow myWorkflow {
    # 工作流输入参数
    # https://github.com/openwdl/wdl/blob/main/versions/1.0/SPEC.md#types
    input {
        String guest = "input"
    }

    # 工作流调用任务或者子流程, 提供对应任务或者子流程的输入参数
    # https://github.com/openwdl/wdl/blob/main/versions/1.0/SPEC.md#call-statement
    call myTask {
        input:
            who = guest 
    }
}

task myTask {
    input {
        String who
        # Int i = 1
        # Float f = 33.5
        # Boolean b = false
    }
    # 任务命令行，请替换为您实际执行脚本
    command {
        echo "hello world"
    }
    runtime {
        memory: "1 GB"
        cpu: "1"
        queue: "default_queue"
        # local_upload_address: "/chpcdata/xxxxx.txt"
        # netdisk_appId: "xxxx"
        # upload_address: "netdisk://test/xxxxxxx.txt"
        # remote_address: "netdisk://test/xx.txt"
        # local_address: "/chpcdata/"
        # netdisk_spaceToken: "xxxx"
    }

    # 任务运行最终的输出
    # https://github.com/openwdl/wdl/blob/main/versions/1.0/SPEC.md#outputs-section
    output {
        String out = "${who}"
    }
}