version 1.0
workflow myWorkflow {
    call myTask
}

task myTask {
    input {
        String input_fasta
        String output_fasta
    }
    command {
        clustalo -i "${input_fasta}" -o "${output_fasta}"
    }
    runtime {
        memory: "1 GB"
        cpu: "1"
        queue: "default_queue"
        docker: "registry.baidubce.com/kun-public/clustalomega:latest"
        docker_user: "root"
        local_upload_address: "/chpcdata/output.fasta"
        netdisk_appId: "42098601"
        upload_address: "netdisk://test/output.fasta"
        remote_address: "netdisk://test/input.json"
        local_address: "/chpcdata/"
        netdisk_spaceToken: "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
    }
    output {
        String out = "${output_fasta}"
    }
}