#/bin/sh
curdate=`date -d day +%Y%m%d%H`
source=/opt/cromwell/cromwell.out
target=/opt/cromwell/log/cromwell.out."$curdate"_
fileSize=`ls -l $source |awk '{print $5}'`
# nohup.log 超过200m 日志切割
#threshSize=209715200
threshSize=209715200
if [ "$fileSize" -gt "$threshSize" ];then
        echo "lograte cut log..."
        split -C 200m -d -a 5 $source $target
        cat /dev/null > $source
        #清理2天前nohup 文件
        find /opt/cromwell/log -type f -mtime +2  -name /opt/cromwell/log/cromwell.out.* -exec rm -f {} \;
fi