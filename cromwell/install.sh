#!/bin/bash
if [ -f /etc/lsb-release ]; then
    # Ubuntu 系统
    sudo apt update
    sudo apt install -y zip
    sudo apt install -y openjdk-16-jdk
    sudo apt install -y docker.io
    echo '{"registry-mirrors":["https://mirror.baidubce.com"]}' | sudo tee /etc/docker/daemon.json
    sudo gpasswd -a work docker
    systemctl enable docker
    systemctl start docker
elif [ -f /etc/redhat-release ]; then
    # CentOS 系统
    sudo yum install -y zip
    sudo yum install -y java-11-openjdk
    yum install -y docker docker-ce docker-ce-cli containerd.io docker-compose-plugin
    echo '{"registry-mirrors":["https://mirror.baidubce.com"]}' | sudo tee /etc/docker/daemon.json
    groupadd docker
    gpasswd -a work docker
    systemctl enable docker
    systemctl start docker
fi
# cromwell通用安装。cromwell.zip内包括:cromwell-86.jar、cromwell_start.sh、cromwell.conf、cromwell_service、cromwell.sh、install.sh、logrorate.sh
mkdir /opt/cromwell && cd /opt/cromwell && wget https://chpc-dev.bj.bcebos.com/cromwell/cromwell.zip && unzip cromwell.zip && rm cromwell.zip
cd /opt && wget https://chpc-dev.bj.bcebos.com/cromwell/apaasutil&& chmod 777 /opt/apaasutil
cd /opt && mkdir netdisk && mkdir netdisk/bin
cd /opt/netdisk && wget https://chpc-dev.bj.bcebos.com/cromwell/libbndsdk.so
cd /opt/netdisk/bin && wget https://chpc-dev.bj.bcebos.com/cromwell/bndsdk_demo && chmod 777 /opt/netdisk/bin/bndsdk_demo