#! /bin/bash

# set -u
set -x
# slurm和sge配置文件路径
readonly SLURM_HOST="/opt/slurm/22.05.9"
readonly SLURM_CONF_PATH="$SLURM_HOST/etc/slurm.conf"
readonly SLURMDBD_CONF_PATH="$SLURM_HOST/etc/slurmdbd.conf"
readonly SLURMD_SERVICE_PATH="/usr/lib/systemd/system/slurmd.service"
readonly SLURM_CONF_TEMPLATE_PATH=$SLURM_HOST"/template/slurm.conf.template"
readonly SLURMDBD_CONF_TEMPLATE_PATH=$SLURM_HOST"/template/slurmdbd.conf.template"
readonly SLURMD_SERVICE_TEMPLATE_PATH=$SLURM_HOST"/template/slurmd.service.template"
readonly SLURM_TMP_DIR_PATH=$SLURM_HOST"/template/tmp"

SLURM_CONF_TMP_PATH=""
SLURMDBD_CONF_TMP_PATH=""
SLURMD_SERVICE_TMP_PATH=""

readonly SGE_QUEUE_TEMPLATE_PATH="/opt/sge/templates/queue_template"
readonly SGE_ACL_TEMPLATE_PATH="/opt/sge/templates/acluser.template"
readonly SGE_USER_TEMPLATE_PATH="/opt/sge/templates/user.template"
readonly SGE_DEFAULT_QUEUE_TEMPLATE_PATH="/opt/sge/templates/default_queue_template"
# slurm进程占用端口
readonly SLURMCTLD_PORT=6817
readonly SLURMD_PORT=6818
readonly SLURMDBD_PORT=6819
readonly SLURMRESTD_PORT=6820
# slapd占用端口
readonly SLAPD_PORT=389
# sge占用端口
readonly SGEMASTER_PORT=6444
readonly SGEEXECD_PORT=6445
readonly CHECK_ALIVE_INTERVAL="5s"

# chpc server
readonly CHPC_SERVICE_TEMPLATE_PATH="/opt/chpc/cluster-api/template/chpc-server.service.template"
readonly CHPC_SERVICE_PATH="/usr/lib/systemd/system/chpc-server.service"

# 变量
cluster_name=""
partition_names=""
master_host=""
login_host=""
compute_hosts=""
backup_hosts=""
exec_func=""
region=""
clusterNameId=""
#
source /etc/profile
# 解析shell参数
function parse_args() {
    args=`getopt -o c:p:o:h:t: -l clusterNameId:,masterHost:,computeHosts:,loginHost::,backupHosts:,mountDir:,region: -- "$@"`
    if [ $? != 0 ] ; then
        echo "Parse error! Terminating..." >&2 ;
        exit 1 ;
    fi
    echo $args
    eval set -- "$args"
    while true ; do
        case "$1" in
            -c) cluster_name="$2"; shift 2 ;;
            -p) partition_names="$2"; shift 2;;
            -o) operation_system="$2"; shift 2;;
            -t) mount_target="$2"; shift 2;;
            --clusterNameId) cluster_name_id="$2"; shift 2;;
            --masterHost) master_host="$2"; shift 2;;
            --computeHosts) compute_hosts="$2"; shift 2;;
            --loginHost) login_host="$2"; shift 2;;
            --backupHosts) backup_hosts="$2"; shift 2;;
            --mountDir) mount_dir="$2"; shift 2;;
            --region) region="$2"; shift 2;;
            -h) help; exit 0 ;;
            --) shift ; break ;;
            *) echo "Parameters error!!!$1" ; exit 1 ;;
        esac
    done
    exec_func=$1
}

function help() {
    echo "Usage:\n"
    return 0
}

# 处理入口
function main() {
    add_work_for_hosts
    if [ x"$1" == x"startSlurmMasterNoHA" ]; then
        start_slurm_master_no_ha $cluster_name $master_host
        ret_code=$?
        return $ret_code
    elif [ x"$1" == x"modifySlurmMasterConfNoHA" ]; then
        modify_master_conf_no_ha $cluster_name $master_host
        ret_code=$?
        return $ret_code
    elif [ x"$1" == x"startSlurmComputeNoHA" ]; then
        partition_names=(`echo $partition_names | tr ',' ' '`)
        start_slurm_compute_no_ha $master_host $partition_names $cluster_name
        ret_code=$?
        return $ret_code
    elif [ x"$1" == x"startSlurmLoginNoHA" ]; then
        start_slurm_login_no_ha $master_host $cluster_name
        ret_code=$?
        return $ret_code
    elif [ x"$1" == x"createSlurmQueues" ]; then
        partition_names=(`echo $partition_names | tr ',' ' '`)
        add_slurm_queue $partition_names
        ret_code=$?
        return $ret_code
    elif [ x"$1" == x"createSlumDefaultQueue" ]; then
        partition_names=(`echo $partition_names | tr ',' ' '`)
        add_slurm_default_queue $partition_names
        ret_code=$?
        return $ret_code
    elif [ x"$1" == x"startSgeMasterNoHA" ]; then
        start_sge_master_no_ha $cluster_name $master_host $login_host
        ret_code=$?
        return $ret_code
    elif [ x"$1" == x"startSgeLoginHost" ]; then
        start_sge_login $master_host
        ret_code=$?
        return $ret_code
    elif [ x"$1" == x"createSgeQueues" ]; then
        partition_names=(`echo $partition_names | tr ',' ' '`)
        create_sge_queues $partition_names
        ret_code=$?
        return $ret_code
    elif [ x"$1" == x"createSgeDefaultQueue" ]; then
        partition_names=(`echo $partition_names | tr ',' ' '`)
        create_sge_default_queue $partition_names
        ret_code=$?
        return $ret_code
    elif [ x"$1" == x"addSgeComputeToQueue" ]; then
        partition_names=(`echo $partition_names | tr ',' ' '`)
        compute_hosts=(`echo $compute_hosts | tr ',' ' '`)
        add_sge_compute_to_queue $compute_hosts $partition_names
        ret_code=$?
        return $ret_code
    elif [ x"$1" == x"startSgeExecdNoHA" ]; then
        start_sge_execd_no_ha $master_host $partition_names
        ret_code=$?
        return $ret_code
    elif [ x"$1" == x"startSlapdNoHA" ]; then
        #start_slapd_no_ha
        ret_code=0
        return $ret_code
    elif [ x"$1" == x"startNslcd" ]; then
        #start_nslcd $master_host
        ret_code=0
        return $ret_code
    # sge 移除节点,api-server调用
    elif [ x"$1" == x"removeSgeComputeFromQueue" ]; then
        partition_names=(`echo $partition_names | tr ',' ' '`)
        compute_hosts=(`echo $compute_hosts | tr ',' ' '`)
        remove_slave $partition_names $compute_hosts
        ret_code=$?
        return $ret_code
    # sge 删除队列,api-server调用
    elif [ x"$1" == x"deleteSgeQueues" ]; then
        partition_names=(`echo $partition_names | tr ',' ' '`)
        delete_sge_queues $partition_names
        ret_code=$?
        return $ret_code
    # slurm 删除队列,测试脚本使用
    elif [ x"$1" == x"deleteSlurmQueues" ]; then
        partition_names=(`echo $partition_names | tr ',' ' '`)
        delete_slurm_queues $partition_names
        ret_code=$?
        return $ret_code
    elif [ x"$1" == x"mountCfs" ]; then
        mount_cfs $mount_target $mount_dir $cluster_name_id
        ret_code=$?
        return $ret_code
    elif [ x"$1" == x"startNfs" ]; then
        start_nfs
        ret_code=$?
        return $ret_code
    elif [ x"$1" == x"startChpcServer" ]; then
        start_chpc_server $region
        ret_code=$?
        return $ret_code
    elif [ x"$1" == x"removeSlurmNode" ]; then
        remove_slurm_slave $compute_hosts
        ret_code=$?
        return $ret_code
    elif [ x"$1" == x"updateSlurmStateDrain" ]; then
        update_slurm_slave_state_to_drain $compute_hosts
        ret_code=$?
        return $ret_code
    elif [ x"$1" == x"updateSlurmStateIdle" ]; then
        update_slurm_slave_state_to_idle $compute_hosts
        ret_code=$?
        return $ret_code
    fi
}

# 初始化前关闭已有服务
function disable_all_slurm_service_and_mysql(){
    systemctl stop slurmctld
    systemctl disable slurmctld

    systemctl stop slurmdbd
    systemctl disable slurmdbd

    systemctl stop slurmd
    systemctl disable slurmd

    mysql_service=""
    mysql_log_path="/var/log/mysqld.log"
    if [ -f $mysql_log_path ]
    then
        mysql_service="mysqld"
    else
        mysql_service="mysql"
    fi
    systemctl stop $mysql_service
    systemctl disable $mysql_service
}

# 添加slurm非默认队列
function add_slurm_queue() {
    partition_names=$1
    for partition_name in ${partition_names[@]}
    do
        match_num=$(grep -c "Nodeset=set_$partition_name" $SLURM_CONF_PATH)
        if [ $match_num = 0 ]; then
            cat>>$SLURM_CONF_PATH<<EOF
Nodeset=set_$partition_name Feature=feature_$partition_name
PartitionName=$partition_name Nodes=set_$partition_name
EOF
            ret_code=$?
            if [ $ret_code -ne 0 ]
            then
                echo "add slurm queue failed"
                return 1
            fi
        fi
    done
    return 0
}
# 添加slurm默认队列
function add_slurm_default_queue() {
    partition_names=$1
    for partition_name in ${partition_names[@]}
    do
        match_num=$(grep -c "Nodeset=set_$partition_name" $SLURM_CONF_PATH)
        if [ $match_num = 0 ]; then
            cat>>$SLURM_CONF_PATH<<EOF
Nodeset=set_$partition_name Feature=feature_$partition_name
PartitionName=$partition_name Default=YES Nodes=set_$partition_name
EOF
            ret_code=$?
            if [ $ret_code -ne 0 ]
            then
                echo "add slurm default queue failed"
                return 1
            fi
        fi
    done
    return 0
}
# 启动slurmd
function start_slurmd() {
    systemctl enable slurmd
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "set slurmd autostart failed"
        return 1
    fi
    systemctl start slurmd
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "start slurmd failed"
        return 1
    fi

    slurmd_status=` systemctl status slurmd | grep 'Active:' | awk '{print $3}' | cut -d "(" -f2 | cut -d ")" -f1`
    if [ "$slurmd_status" != "running" ]
    then
		echo "start slurmd failed"
		return 1
	fi
    check_port_is_open $SLURMD_PORT
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "start slurmd failed"
        return 1
    fi
    return 0
}
# 添加slurm、slurmapi、munge用户和用户组
function add_slurm_user() {
    groupadd -g 1001 slurm
    useradd -u 1001 -g slurm slurm
    usermod -G slurm slurm

    groupadd -g 1002 munge
    useradd -u 1002 -g munge munge
    usermod -G munge munge

    groupadd -g 1003 slurmapi
    useradd -u 1003 -g slurmapi slurmapi
    usermod -G slurmapi slurmapi

    return 0
}
# slurm jwt授权,所有节点执行
function add_jwt_auth() {
    mkdir -p /var/spool/slurm/statesave && \
    dd if=/dev/random of=/var/spool/slurm/statesave/jwt_hs256.key bs=32 count=1 && \
    chmod 0600 /var/spool/slurm/statesave/jwt_hs256.key && \
    chmod 0755 /var/spool/slurm/statesave && \
    chown -R slurm:slurm /var/spool/slurm/statesave
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "add jwt auth failed"
        return 1
    fi
    return 0
}
# 创建slurm临时配置和服务文件，用来完成参数替换
function build_tmp_slurm_conf_and_service() {
    random_value=$RANDOM
    SLURM_CONF_TMP_PATH=$SLURM_TMP_DIR_PATH"/slurm.conf."$random_value
    cp $SLURM_CONF_TEMPLATE_PATH $SLURM_CONF_TMP_PATH

    SLURMDBD_CONF_TMP_PATH=$SLURM_TMP_DIR_PATH"/slurmdbd.conf."$random_value
    cp $SLURMDBD_CONF_TEMPLATE_PATH $SLURMDBD_CONF_TMP_PATH

    SLURMD_SERVICE_TMP_PATH=$SLURM_TMP_DIR_PATH"/slurmd.service."$random_value
    cp $SLURMD_SERVICE_TEMPLATE_PATH $SLURMD_SERVICE_TMP_PATH

    return 0
}

# 创建slurm临时配置和服务文件，用来完成参数替换
function mv_tmp_slurm_file_to_real_target() {
    cp $SLURM_CONF_TMP_PATH $SLURM_CONF_PATH
    cp $SLURMDBD_CONF_TMP_PATH $SLURMDBD_CONF_PATH
    cp $SLURMD_SERVICE_TMP_PATH $SLURMD_SERVICE_PATH
    return 0
}

# slurm通用设置,所有节点都需要执行
function do_slurm_commonsettings() {
    add_slurm_user && add_jwt_auth
    ret_code=$?
    if [ $ret_code -ne 0 ]
    then
        echo "do slurm commonsettings failed"
        return 1
    fi
    return 0
}
# 创建slumrd日志目录,并授权
function set_slurm_compute_auth() {
    mkdir -p /var/spool/slurmd && \
    mkdir -p /var/log/slurm && \
    chown -R slurm:slurm /var/spool/slurmd && \
    chown -R slurm:slurm /var/log/slurm
    ret_code=$?
    if [ $ret_code -ne 0 ]
    then
        echo "set slurm conf failed"
        return 1
    fi
    return 0
}

# 创建slumrd日志目录,并授权
function set_slurm_login_auth() {
    mkdir -p /var/spool/slurmd && \
    mkdir -p /var/log/slurm && \
    chown -R slurm:slurm /var/spool/slurmd && \
    chown -R slurm:slurm /var/log/slurm 
    ret_code=$?
    if [ $ret_code -ne 0 ] 
    then
        echo "set slurm conf failed"
        return 1
    fi
    return 0
}

# 修改slurm.conf,非高可用场景,计算节点执行
function modify_slurm_compute_conf_no_ha() {
    cat>>$SLURM_CONF_TMP_PATH<<EOF
ClusterName=$cluster_name
SlurmctldHost=$master_host
AccountingStorageHost=$master_host
EOF
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "edit slurm conf failed"
        return 1
    fi
    return 0
}

# 修改slurm.conf,非高可用场景,登录节点执行
function modify_slurm_login_conf_no_ha() {
    cat>>$SLURM_CONF_TMP_PATH<<EOF
ClusterName=$cluster_name
SlurmctldHost=$master_host
AccountingStorageHost=$master_host
EOF
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "edit slurm conf failed"
        return 1
    fi
    return 0
}

# 启动slurm计算节点,非高可用场景
function start_slurm_compute_no_ha() {
    master_host=$1
    partition_name=$2
    cluster_name=$3

    systemctl stop slapd
    systemctl disable slapd
    disable_all_slurm_service_and_mysql

    build_tmp_slurm_conf_and_service
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "build_tmp_slurm_conf_and_service failed"
        return 1
    fi

    do_slurm_commonsettings
    ret_code=$?
    if [ $ret_code -ne 0 ]
    then
        echo "add slurm user failed"
        return 1
    fi

    modify_slurm_compute_conf_no_ha
    ret_code=$?
    if [ $ret_code -ne 0 ]
    then
        echo "modify slurm compute conf failed"
        return 1
    fi

    set_slurm_compute_auth
    ret_code=$?
    if [ $ret_code -ne 0 ]
    then
        echo "set slurm auth failed"
        return 1
    fi
    # gpu检测
    gpuoption=""
    gpu_info=`check_gpu_model_and_num`
    ret_code=$?
    if [[ $ret_code -eq 0 ]]; then
        model=`echo $gpu_info|awk '{print $1}'`
        num=`echo $gpu_info|awk '{print $2}'`
	    gpuoption="Gres=gpu:${model}:${num}"
    fi
    sed -i "11c ExecStart=$SLURM_HOST/sbin/slurmd -Z --conf \"Feature=feature_$partition_name $gpuoption CpuSpecList=0-15 MemSpecLimit=24576\" --conf-server $master_host -D -s" $SLURMD_SERVICE_TMP_PATH
    start_munge
    ret_code=$?
    if [ $ret_code -ne 0 ]
    then
        echo "start munge failed"
        return 1
    fi

    mv_tmp_slurm_file_to_real_target
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "mv_tmp_slurm_file_to_real_target failed"
        return 1
    fi

    for i in $(seq 1 5)
    do
      start_slurmd
      ret_code=$?
      if [ $ret_code -ne 0 ]; then
        echo "start slurmd failed: ${i}"
        if [ ${i} -eq 5 ];then
            return 1
        fi
        continue
      fi
      break
    done

    systemctl enable resume_slurm_node.service
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "set resume_slurm_node autostart failed"
        return 1
    fi

    return 0
}

# 启动slurm登录节点,非高可用场景
function start_slurm_login_no_ha() {
    master_host=$1
    cluster_name=$2

    systemctl stop slapd
    systemctl disable slapd
    disable_all_slurm_service_and_mysql

    build_tmp_slurm_conf_and_service
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "build_tmp_slurm_conf_and_service failed"
        return 1
    fi

    do_slurm_commonsettings
    ret_code=$?
    if [ $ret_code -ne 0 ] 
    then
        echo "add slurm user failed"
        return 1
    fi

    modify_slurm_login_conf_no_ha
    ret_code=$?
    if [ $ret_code -ne 0 ]
    then
        echo "modify slurm login conf failed"
        return 1
    fi

    set_slurm_login_auth
    ret_code=$?
    if [ $ret_code -ne 0 ] 
    then
        echo "set slurm auth failed"
        return 1
    fi

    sed -i "11c ExecStart=$SLURM_HOST/sbin/slurmd -Z --conf-server $master_host -D -s" $SLURMD_SERVICE_TMP_PATH

    start_munge
    ret_code=$?
    if [ $ret_code -ne 0 ] 
    then
        echo "start munge failed"
        return 1
    fi

    mv_tmp_slurm_file_to_real_target
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "mv_tmp_slurm_file_to_real_target failed"
        return 1
    fi

    return 0
}

# slurm conf file授权和创建目录日志,主备节点执行
function set_slurm_conf_auth() {
    # 权限修改,创建日志目录
    chmod 600 $SLURM_HOST/etc/slurmdbd.conf && \
    chown -R slurm:slurm $SLURM_HOST/etc/slurmdbd.conf && \
    chown -R slurm:slurm $SLURM_HOST/etc/slurmrestd.conf && \
    chown -R slurm:slurm $SLURM_HOST/etc/slurm.conf && \
    chown -R root:root $SLURM_HOST/etc/slurm.conf && \
    mkdir -p /var/log/slurm && \
    mkdir -p /var/spool/slurmctld && \
    chown -R slurm:slurm /var/log/slurm && \
    chown -R slurm:slurm /var/spool/slurmctld
    ret_code=$?
    if [ $ret_code -ne 0 ]
    then
        echo "set slurm conf failed"
        return 1
    fi
    return 0
}
# mysql密码修改,主备
function set_slurm_db_auth() {
    mysql_log_path="/var/log/mysqld.log"
    # 获取mysql密码，ubuntu为空
    if [ -f $mysql_log_path ]
    then
        rootPassword=`grep "temporary password" /var/log/mysqld.log | awk -F ': ' '{print $NF}'`
        mysqladmin -uroot -p$rootPassword password Baidu@123
        ret_code=$?
        if [ $ret_code -ne 0 ]
        then
            echo "change mysql root passwd failed"
            return 1
        fi
    else
        mysqladmin -uroot password Baidu@123
        ret_code=$?
        if [ $ret_code -ne 0 ]
        then
            echo "change mysql root passwd failed"
            return 1
        fi
    fi
    mysql -uroot -pBaidu@123 -e "use mysql;update user set host='%' where user ='slurm';create user if not exists 'slurm'@'%' identified by 'Baidu@123';" && \
    mysql -uroot -pBaidu@123 -e "create database if not exists slurm_acct_db;"  && \
    mysql -uroot -pBaidu@123 -e "create database if not exists slurm_jobcomp_db;"  && \
    mysql -uroot -pBaidu@123 -e "use mysql;grant all privileges on slurm_acct_db.* TO 'slurm'@'%';grant all privileges on slurm_jobcomp_db.* TO 'slurm'@'%';"  && \
    mysql -uroot -pBaidu@123 -e "flush privileges;"
    ret_code=$?
    if [ $ret_code -ne 0 ]
    then
        echo "change mysql privileges failed"
        return 1
    fi
    return 0
}
# 修改slurm.conf,非高可用场景,主节点执行
function modify_slurm_conf_no_ha() {
    cluster_name=$1
    slurmcltd_host=$2
    cat>>$SLURM_CONF_TMP_PATH<<EOF
ClusterName=$cluster_name
SlurmctldHost=$slurmcltd_host
AccountingStorageHost=$slurmcltd_host
AccountingStoreFlags=job_comment,job_env
StateSaveLocation=/var/spool/slurmctld
SlurmctldTimeout=120
JobAcctGatherType=jobacct_gather/cgroup
EOF
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "edit slurm conf failed"
        return 1
    fi
    # 未开启监控采集，删除该文件，否则slurmcltd启动失败
    rm -f $SLURM_HOST/etc/acct_gather.conf
    return 0
}
# slurm api token生成
function generate_token() {
    if [ ! -d "/opt/chpc/cluster-api/conf" ]
    then
        mkdir -p /opt/chpc/cluster-api/conf
    fi
    $SLURM_HOST/bin/scontrol token lifespan=*********** username=slurmapi | awk -F '=' '{print $2}' > /opt/chpc/cluster-api/conf/slurmapi.token
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "generate slurm api token failed"
        return 1
    fi
    return 0
}
# 修改slurmdbd.conf,非高可用场景,主节点执行
function modify_slurm_dbconf_no_ha() {
    echo "StoragePort=3306" >> $SLURMDBD_CONF_TMP_PATH
    return 0
}
# 启动mysqld
function start_mysqld() {
    mysql_service=""
    mysql_log_path="/var/log/mysqld.log"
    if [ -f $mysql_log_path ]
    then
        mysql_service="mysqld"
    else
        mysql_service="mysql"
    fi
    systemctl enable $mysql_service
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "set $mysql_service autostart failed"
        return 1
    fi
    systemctl start $mysql_service
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "start $mysql_service failed"
        return 1
    fi
    mysqld_status=`systemctl status $mysql_service | grep Active | awk '{print $3}' | cut -d "(" -f2 | cut -d ")" -f1`
    if [ "$mysqld_status" != "running" ]
    then
		echo "start $mysql_service failed"
		return 1
	fi
    return 0
}
# 启动munge
function start_munge() {
    systemctl enable munge
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "set munge autostart failed"
        return 1
    fi
    systemctl start munge
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "start munge failed"
        return 1
    fi
    munge_status=`systemctl status munge | grep Active | awk '{print $3}' | cut -d "(" -f2 | cut -d ")" -f1`
    if [ "$munge_status" != "running" ]
    then
		echo "start munge failed"
		return 1
	fi
    return 0
}
# 启动slurmdbd
function start_slurmdbd() {
    systemctl enable slurmdbd
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "set slurmdbd autostart failed"
        return 1
    fi
    systemctl start slurmdbd
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "start slurmdbd failed"
        return 1
    fi
    slurmdbd_status=`systemctl status slurmdbd | grep Active | awk '{print $3}' | cut -d "(" -f2 | cut -d ")" -f1`
    if [ "$slurmdbd_status" != "running" ]; then
      echo "start slurmdbd failed"
      return 1
    fi
    check_port_is_open $SLURMDBD_PORT
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "start slurmdbd failed"
        return 1
    fi
    return 0
}
# 启动slurmctld
function start_slurmctld() {
    systemctl enable slurmctld
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "set slurmctld autostart failed"
        return 1
    fi
    systemctl start slurmctld
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "start slurmctld failed"
        return 1
    fi
    slumctld_status=`systemctl status slurmctld | grep Active | awk '{print $3}' | cut -d "(" -f2 | cut -d ")" -f1`
    if [ "$slumctld_status" != "running" ]
      then
      echo "start slurmctld failed"
      return 1
    fi
    check_port_is_open $SLURMCTLD_PORT
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "start slurmctld failed"
        return 1
    fi
    return 0
}

# 为slurm 添加 worker account 和 user
function add_work_account_and_user_for_slurm() {
    result=$(sacctmgr show account where account=work)
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "check slurm account=work failed"
        return 1
    fi
    linenum=$(echo "$result"|wc -l)
    if [[ $linenum -ne 3 ]]; then
        err=$(sacctmgr -i add account work)
        ret_code=$?
        if [[ $ret_code -ne 0 ]]; then
            echo "sacctmgr add account work failed, error: "$err
            return 1
        fi

    fi

    result=$(sacctmgr show user withasso where user=work and acct=work)
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "check slurm user where account=work failed"
        return 1
    fi
    linenum=$(echo "$result"|wc -l)
    if [[ $linenum -ne 3 ]]; then
        err=$(sacctmgr -i add user work Account=work)
        ret_code=$?
        if [[ $ret_code -ne 0 ]]; then
            echo "sacctmgr add user work with account=work failed, error: "$err
            return 1
        fi

    fi
    return 0
}

# 启动slurmrestd
function start_slurmrestd() {
    systemctl enable slurmrestd
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "set slurmrestd autostart failed"
        return 1
    fi
    systemctl start slurmrestd
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "start slurmrestd failed"
        return 1
    fi
    slumrestd_status=`systemctl status slurmrestd | grep Active | awk '{print $3}' | cut -d "(" -f2 | cut -d ")" -f1`
    if [ "$slumrestd_status" != "running" ]
      then
      echo "start slumrestd failed"
      return 1
    fi
    check_port_is_open $SLURMRESTD_PORT
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "start slurmrestd failed"
        return 1
    fi
    return 0
}

# 修改 master conf,非高可用
function modify_master_conf_no_ha(){
    build_tmp_slurm_conf_and_service
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "build_tmp_slurm_conf_and_service failed"
        return 1
    fi

    modify_slurm_conf_no_ha $1 $2
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "modify_slurm_conf_no_ha failed"
        return 1
    fi
    modify_slurm_dbconf_no_ha
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "modify_slurm_dbconf_no_ha failed"
        return 1
    fi

    mv_tmp_slurm_file_to_real_target
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "mv_tmp_slurm_file_to_real_target failed"
        return 1
    fi
    return 0
}

# 启动slurm mater,非高可用
function start_slurm_master_no_ha() {
    disable_all_slurm_service_and_mysql
    do_slurm_commonsettings
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "do_slurm_commonsettings failed"
        return 1
    fi
    set_slurm_conf_auth
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "set_slurm_conf_auth failed"
        return 1
    fi

    start_mysqld
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "start_mysqld failed"
        return 1
    fi
    set_slurm_db_auth
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "set_slurm_db_auth failed"
        return 1
    fi
    start_munge
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "start_munge failed"
        return 1
    fi
    start_slurmdbd
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "start_slurmdbd failed"
        return 1
    fi
    sleep $CHECK_ALIVE_INTERVAL
    start_slurmctld
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "start_slurmctld failed"
        return 1
    fi

    add_work_account_and_user_for_slurm
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "add_work_account_and_user_for_slurm failed"
        return 1
    fi

    start_slurmrestd
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "start_slurmrestd failed"
        return 1
    fi
    # 主节点 slurm api token生成
    generate_token
    ret_code=$?
    if [ $ret_code -ne 0 ]
    then
        echo "generate slurm api token failed"
        return 1
    fi
    return 0
}
# 启动sge主节点,非高可用场景
function start_sge_master_no_ha() {
    cluster_name=$1
    hostname=$2
    login_hostname=$3
    add_sge_user
    echo "$cluster_name" > /opt/sge/default/common/cluster_name
    echo "$hostname" > /opt/sge/default/common/act_qmaster
    systemctl enable sgemaster
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "set sgemaster autostart failed"
        return 1
    fi
    systemctl start sgemaster
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "start sgemaster failed"
        return 1
    fi
    sgemaster_status=`systemctl status sgemaster | grep Active | awk '{print $3}' | cut -d "(" -f2 | cut -d ")" -f1`
    if [ "$sgemaster_status" != "running" ]
    then
		echo "start sgemaster failed"
		return 1
	fi
    check_port_is_open $SGEMASTER_PORT
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "start sge master failed"
        return 1
    fi
    qconf -as $hostname
    qconf -ah $hostname
    if [ -n "$login_hostname" ]; then
      # 添加登录节点提交作业权限
        qconf -as $login_hostname
      # 添加登录节点管理员权限-登录节点才能查看集群信息
        qconf -ah $login_hostname
    fi
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "authorize $hostname as submit host failed"
        return 1
    fi
    qconf -Msconf /opt/sge/templates/msconf_template
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "qconf msconf failed"
        return 1
    fi
    # 修改全局执行节点配置，修改 complex_values 为 m_thread=9999,mem_free=9999G
    qconf -Me /opt/sge/templates/me_global_template
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "qconf me_global conf failed"
        return 1
    fi
    # gpu支持
    # echo "gpu                 gpu        INT       <=    YES         YES        0        0" >>/opt/sge/templates/mc_template
    # 修改资源为 consumable, m_thread,mem_free,slots 为 YES
    qconf -Mc /opt/sge/templates/mc_template
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "qconf mc conf failed"
        return 1
    fi
   # add_work_account_and_user_for_sge
    return 0
}

#function add_work_account_and_user_for_sge(){
#  result=$(qconf -suser work 2>&1)
#  if [[ $result != *"work is not known as a user"* ]]; then
#    echo "user work already exist"
#    return 0
#  fi
#  # 此处说明sge集群中还没有添加work用户
#  temp_work_config_file=/tmp/work.$$
#  sed "s/template/work/" $SGE_USER_TEMPLATE_PATH > ${temp_work_config_file}
#  qconf -Auser ${temp_work_config_file}
#  ret_code=$?
#  if [[ $ret_code -ne 0 ]]; then
#    rm -f ${temp_work_config_file}
#    echo "add user work failed"
#    return 1
#  fi
#  result=$(qconf -suser work 2>&1)
#  if [[ $result == *"work is not known as a user"* ]]; then
#    rm -f ${temp_work_config_file}
#    echo "add work failed"
#    return 1
#  fi
#  rm -f ${temp_work_config_file}
#  return 0
#}

# 启动sge计算节点,非高可用场景
function start_sge_execd_no_ha() {
    master_host=$1
    queue_name=$2
    add_sge_user
    echo "$master_host" > /opt/sge/default/common/act_qmaster
    systemctl enable sgeexecd
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "set sgeexecd autostart failed"
        return 1
    fi
    # 多次尝试启动sgeexecd，因为启动时有概率失败
    for i in $(seq 1 5)
    do
      systemctl start sgeexecd
      ret_code=$?
      if [ $ret_code -ne 0 ]; then
          echo "start sgeexecd failed: ${i}"
          if [ ${i} -eq 5 ]; then
            return 1
          fi
          continue
      fi
      sgeexecd_status=`systemctl status sgeexecd | grep Active | awk '{print $3}' | cut -d "(" -f2 | cut -d ")" -f1`
      if [ "$sgeexecd_status" != "running" ];then
		    echo "start sgeexecd failed: ${i}"
		    if [ ${i} -eq 5 ]; then
		      return 1
		    fi
		    continue
	    fi
	    break
	  done
    check_port_is_open $SGEEXECD_PORT
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "start sge execd failed"
        return 1
    fi
    set_sge_slave_resource
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "qconf set sge execd resources failed"
        return 1
    fi
    # 向master节点发送启动节点命令，之前注册的时候默认为不可调度状态
    # qmod -e queueName@hostname
    cur_hostname=`hostname`
    qmod -e ${queue_name}@${cur_hostname}
    return 0
}

# 启动sge登录节点，只需要写入master_hots
function start_sge_login() {
    master_host=$1
    echo "$master_host" > /opt/sge/default/common/act_qmaster
}

# 创建多个sge队列
function create_sge_queues() {
    queue_names=$1
    for queue_name in ${queue_names[@]}
    do
        create_sge_queue $queue_name
        ret_code=$?
        if [[ $ret_code -ne 0 ]]; then
            echo "create sge queue failed"
            return 1
        fi
    done
}
# 创建sge队列
function create_sge_queue() {
    queue_name=$1
    if qconf -sql| grep -qw "$queue_name"; then
        return 0
    fi
    temp_queue_config_file=/tmp/${queue_name}.$$
    sed "s/template/${queue_name}/" $SGE_QUEUE_TEMPLATE_PATH > ${temp_queue_config_file}
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "generate queue config of ${queue_name} failed"
        return 1
    fi
    qconf -Aq ${temp_queue_config_file}
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        rm -f ${temp_queue_config_file}
        echo "add queue using config failed"
        return 1
    fi
    rm -f ${temp_queue_config_file}
    # 创建acl并绑定队列 queueNameACL
    aclName="${queue_name}ACL"
    temp_acl_config_file=/tmp/${aclName}.$$
    sed "s/defaultdepartment/${aclName}/" $SGE_ACL_TEMPLATE_PATH > ${temp_acl_config_file}
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
      rm -f ${temp_acl_config_file}
      echo "create acl file ${aclName} failed"
      return 1
    fi
    qconf -Au ${temp_acl_config_file}
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
      rm -f ${temp_acl_config_file}
      echo "add acl object to cluster failed"
      return 1
    fi
    # 绑定acl和队列
    qconf -mattr queue user_lists ${aclName} ${queue_name}
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
      rm -f ${temp_acl_config_file}
      echo "bind acl object:${aclName} witch queue:${queue_name} failed"
      return 1
    fi
    rm -f ${temp_acl_config_file}
    return 0
}
# 创建sge默认队列
function create_sge_default_queue() {
    queue_name=$1
    if qconf -sql| grep -qw "$queue_name"; then
        return 0
    fi
    temp_queue_config_file=/tmp/${queue_name}.$$
    sed "s/template/${queue_name}/" $SGE_DEFAULT_QUEUE_TEMPLATE_PATH > ${temp_queue_config_file}
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "generate queue config of ${queue_name} failed"
        return 1
    fi
    qconf -Aq ${temp_queue_config_file}
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        rm -f ${temp_queue_config_file}
        echo "add queue using config failed"
        return 1
    fi
    rm -f ${temp_queue_config_file}
    # 创建acl并绑定队列 queueNameACL
    aclName="${queue_name}ACL"
    temp_acl_config_file=/tmp/${aclName}.$$
    sed "s/defaultdepartment/${aclName}/" $SGE_ACL_TEMPLATE_PATH > ${temp_acl_config_file}
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
      rm -f ${temp_acl_config_file}
      echo "create acl file ${aclName} failed"
      return 1
    fi
    qconf -Au ${temp_acl_config_file}
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
      rm -f ${temp_acl_config_file}
      echo "add acl object to cluster failed"
      return 1
    fi
    # 绑定acl和队列
    qconf -mattr queue user_lists ${aclName} ${queue_name}
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
      rm -f ${temp_acl_config_file}
      echo "bind acl object:${aclName} witch queue:${queue_name} failed"
      return 1
    fi
    rm -f ${temp_acl_config_file}
    return 0
}
# 添加sge计算节点到指定队列
function add_sge_compute_to_queue() {
    compute_hosts=$1
    queue_name=$2
    for compute_host in ${compute_hosts[@]}
    do
        qconf -ah $compute_host
        ret_code=$?
        if [ $ret_code -ne 0 ]; then
            echo "add sge host auth failed"
            return 1
        fi
        qconf -mattr queue hostlist $compute_host $queue_name
        ret_code=$?
        if [ $ret_code -ne 0 ]; then
            echo "add sge host to queue failed"
            return 1
        fi
        qmod -d ${queue_name}@${compute_host}
    done
    return 0
}
# 添加sge user
function add_sge_user() {
    groupadd -g 1008 sge
    useradd -u 1008 -g sge sge
    usermod -G sge sge
    chown -R sge:sge /opt/sge
    return 0
}
# 启动ldap主进程,主节点执行
function start_slapd_no_ha() {
    systemctl enable slapd
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "set slapd autostart failed"
        return 1
    fi
    systemctl start slapd
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "start slapd failed"
        return 1
    fi
    slapd_status=`systemctl status slapd | grep Active | awk '{print $3}' | cut -d "(" -f2 | cut -d ")" -f1`
    if [ "$slapd_status" != "running" ]
    then
		echo "start slapd failed"
		return 1
	fi
    check_port_is_open $SLAPD_PORT
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "start slapd failed"
        return 1
    fi
    return 0
}
# 启动nslcd
function start_nslcd() {
    master_host=$1
    # centos7.9配置修改
    if [ -f "/etc/redhat-release" ]
    then
        authconfig --enableldap --enableldapauth --ldapserver=$master_host --ldapbasedn="dc=baiduhpc,dc=com" --enablemkhomedir --update
        ret_code=$?
        if [ $ret_code -ne 0 ]
        then
            echo "start sgeexecd failed"
            return 1
        fi
    # ubuntu20.04配置修改
    elif [ -f "/etc/lsb-release" ]
    then
        # 根据hostname解析ip地址
        ip=`nslookup $master_host|sed -n '6p'|awk '{print $2}'`
        ret_code=$?
        if [ $ret_code -ne 0 ]; then
        echo "get master ip failed"
        return 1
        fi
        match_num=$(grep -c "session optional pam_mkhomedir.so skel=/etc/skel umask=077" /etc/pam.d/common-session)
        if [ $match_num = 0 ]; then
            echo "session optional pam_mkhomedir.so skel=/etc/skel umask=077">>/etc/pam.d/common-session
        fi

        match_num=$(grep -c "pam_authc_search NONE" /etc/nslcd.conf)
        if [ $match_num = 0 ]; then
            echo "pam_authc_search NONE">>/etc/nslcd.conf
        fi

        match_num=$(grep -c "uri ldap" /etc/nslcd.conf)
        if [ $match_num = 0 ]; then
            echo "uri ldap://$ip/">>/etc/nslcd.conf
        else
            sed -i "s#^uri ldap:.*#uri ldap://$ip/#" /etc/nslcd.conf
        fi

    fi
    # 关闭nscd服务,nscd缓存会导致登录失败
    systemctl disable nscd
    systemctl enable nslcd
    ret_code=$?
    if [ $ret_code -ne 0 ]
    then
        echo "set nslcd autostart failed"
        return 1
    fi

    systemctl stop nslcd
    sleep $CHECK_ALIVE_INTERVAL
    systemctl start nslcd
    ret_code=$?
    if [ $ret_code -ne 0 ]
    then
        echo "start nslcd failed"
        return 1
    fi
    nslcd_status=`systemctl status nslcd | grep Active | awk '{print $3}' | cut -d "(" -f2 | cut -d ")" -f1`
    if [ "$nslcd_status" != "running" ]
    then
		echo "start nslcd failed"
		return 1
	fi
    return 0
}
# 修改slurm.conf,高可用场景,主节点执行 TO DO
modify_slurm_conf_with_ha() {
    echo "cluster_name=$2" >> $1
    echo "SlurmctldHost=$3" >> $1
    echo "SlurmctldHost=$4" >> $1
    echo "SlurmctldHost=$5" >> $1
    echo "StateSaveLocation=$6/slurmctld" >> $1
    echo " SlurmctldTimeout=30" >> $1
    echo "JobAcctGatherType=jobacct_gather/cgroup" >> $1
    echo "JobCompHost=127.0.0.1" >> $1
    echo "JobCompLoc=slurm_jobcomp_db" >> $1
    echo "JobCompPort=6446" >> $1
    # 需要改成循环
    echo "Nodeset=set_$7 Feature=feature_$7" >> $1
    echo "partition_name=$7 Default=YES Nodes=set_$7" >> $1
    return 0
}
# 修改slurmdbd.conf,高可用场景,主节点执行 TO DO
function modify_slurm_dbconf_with_ha() {
    echo "DbdBackupHost=$2" >> $1
    echo "DbdBackupHost=$3" >> $1
    echo "StoragePort=6446" >> $1
    return 0
}
# 移除sge节点,api-server调用
function remove_slave()
{
    queue_name=$1
    hostname=$2
    qmod -d "*@${hostname}"
    if [[ $? -ne 0 ]]; then
        echo "disable slave ${hostname} failed"
        return 1
    fi
    qconf -dattr queue hostlist ${hostname} ${queue_name}
    if [[ $? -ne 0 ]]; then
        echo "qconf -dattr queue hostlist ${hostname} ${queue_name} failed"
        return 1
    fi
    qconf -de ${hostname}
    if [[ $? -ne 0 ]]; then
        echo "qconf -de ${hostname} failed"
        return 1
    fi
    qconf -dh ${hostname}
    if [[ $? -ne 0 ]]; then
        echo "revoke auth of slave ${hostname} failed"
        return 1
    fi
    return 0
}
# 删除sge队列,api-server调用
function delete_sge_queues() {
    queue_name=$1
    qconf -dq $queue_name
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "remove sge queue failed"
        return 1
    fi
}
# 判断当前操作系统为centos或ubuntu
function check_env() {
    if [ -f "/etc/redhat-release" ]
    then
        echo "1"
    elif [ -f "/etc/lsb-release" ]
    then
        echo "2"
    else
        echo "0"
    fi
}
# 启动nfs进程
function start_nfs() {
    ret_code=`check_env`
    if [ $ret_code == 1 ]
    then
        systemctl enable nfs
        systemctl enable rpcbind
        systemctl start rpcbind
        systemctl start nfs
    elif [ $ret_code == 2 ]
    then
        systemctl enable nfs-server
        systemctl enable rpcbind
        systemctl start rpcbind
        systemctl start nfs-server
    else
        echo "unexpected os,only support centos or ubuntu,exit!!!"
        return 1
    fi
    return 0
}
# cfs挂载
function mount_cfs() {
    mount_target=$1
    mount_dir=$2
    cluster_name_id=$3
    if [ ! -d "$mount_dir" ]
    then
        mkdir -p "$mount_dir"
    fi
    umount "$mount_dir"
    chmod -R 777 "$mount_dir"

    # 先将共享存储挂载到/mnt目录，用来在共享存储中创建/chpc/cluster_name#cluster_id/app 和 /chpc/cluster_name#cluster_id/home目录
    temp_mount_dir="/mnt"
    if [ ! -d $temp_mount_dir ]
    then
        mkdir -p $temp_mount_dir
    fi
    umount $temp_mount_dir
    mount -t nfs4 -o minorversion=1,rsize=1048576,wsize=1048576,hard,timeo=600,retrans=2,noresvport $mount_target:/ $temp_mount_dir
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "mount cfs failed"
        return 1
    fi

    # 创建共享存储目录
    local_app_dir="$temp_mount_dir/chpc/$cluster_name_id/app"
    remote_app_dir="/chpc/$cluster_name_id/app"
    local_home_dir="$temp_mount_dir/chpc/$cluster_name_id/home"
    remote_home_dir="/chpc/$cluster_name_id/home"
    mkdir -p "$local_app_dir"
    mkdir -p "$local_home_dir"

    # 卸载临时挂载在/mnt的共享存储
    umount $temp_mount_dir

    # 挂载共享存储目录
    do_mount_cfs "$mount_target" "$remote_app_dir" "/app"
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "mount /app cfs failed"
        return 1
    fi
    do_mount_cfs "$mount_target" "/" "/chpcdata"
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "mount /chpcdata cfs failed"
        return 1
    fi
    do_mount_cfs "$mount_target" "$remote_home_dir" "/home"
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "mount /home cfs failed"
        return 1
    fi

    # 为其他用户添加cfs的写权限
    chmod 777 "$mount_dir"
    chmod 777 "$local_app_dir"
    chmod 755 "$local_home_dir"
    chmod 777 "/chpcdata"

}

function do_mount_cfs() {
    mount_target=$1
    remote_dir=$2
    local_dir=$3

    if [ ! -d "$local_dir" ]
    then
        mkdir -p "$local_dir"
    fi
    umount "$local_dir"

    mount -t nfs4 -o minorversion=1,rsize=1048576,wsize=1048576,hard,timeo=600,retrans=2,noresvport $mount_target:$remote_dir $local_dir
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "mount $local_dir cfs failed"
        return 1
    fi
    write_mount_info "$mount_target":"$remote_dir" "$local_dir"
    ret_code=$?
    if [ $ret_code -ne 0 ]; then
        echo "write_mount_info $local_dir failed"
        return 1
    fi
}

function write_mount_info() {
  remote_mount_target=$1
  mount_dir=$2

  match_num=$(grep -c "$remote_mount_target:/  $mount_dir" /etc/fstab)
  if [ "$match_num" = 0 ]; then
      echo "$remote_mount_target  $mount_dir  nfs  minorversion=1,rsize=1048576,wsize=1048576,hard,timeo=600,retrans=2,noresvport  0   0">>/etc/fstab
  else
      sed -i "s#^$remote_mount_target  $mount_dir.*#$remote_mount_target  $mount_dir  nfs  minorversion=1,rsize=1048576,wsize=1048576,hard,timeo=600,retrans=2,noresvport  0   0#" /etc/fstab
  fi
  return $?
}

# 检测端口是否被占用 每个5s检测一次,循环20次
function check_port_is_open() {
    port=$1
    # 先sleep 5s,再检测端口占用
    sleep $CHECK_ALIVE_INTERVAL
    for((i=0;i<10;i++));
    do
        nc -zv localhost $port
        ret_code=$?
        if [ $ret_code -ne 0 ]; then
            sleep $CHECK_ALIVE_INTERVAL
            continue
        else
            return 0
        fi
    done
    echo "port $port not open,process start failed"
    return 1
}

# 启动chpc server
function start_chpc_server() {
    systemctl stop chpc-server
    systemctl disable chpc-server
    sed "s/region #REGION#/region $1/" $CHPC_SERVICE_TEMPLATE_PATH > $CHPC_SERVICE_PATH
    systemctl enable chpc-server
    systemctl start chpc-server
    sleep $CHECK_ALIVE_INTERVAL
    # TODO chpc-server 有时候莫名奇妙的起不来，重试就能解决，后续定位根本原因
    systemctl start chpc-server
    server_status=`systemctl status chpc-server | grep Active | awk '{print $3}' | cut -d "(" -f2 | cut -d ")" -f1`
    if [ "$server_status" != "running" ]
    then
      echo "start chpc-server failed"
      return 1
    fi
    return 0
}
# 删除slurm queue
function delete_slurm_queues() {
    partition_names=$1
    for partition_name in ${partition_names[@]}
    do
        sed -i "/$partition_name/d" $SLURM_CONF_PATH
        ret_code=$?
        if [ $ret_code -ne 0 ]
        then
            echo "add slurm queue failed"
            return 1
        fi
    done
    return 0
}
# 移除slurm节点,测试脚本调用
function remove_slurm_slave() {
    compute_host=$1
    scontrol delete NodeName=$compute_host
    ret_code=$?
    if [ $ret_code -ne 0 ]
    then
        echo "delete slurm node failed"
        return 1
    fi
}
# 更新slurm节点状态为drain
function update_slurm_slave_state_to_drain() {
    compute_host=$1
    scontrol update NodeName=$compute_host state=drain reason='not use'
    ret_code=$?
    if [ $ret_code -ne 0 ]
    then
        echo "update slurm node state to drain failed"
        return 1
    fi
}
# 更新slurm节点状态为idle
function update_slurm_slave_state_to_idle() {
    compute_host=$1
    scontrol update NodeName=$compute_host state=idle
    ret_code=$?
    if [ $ret_code -ne 0 ]
    then
        echo "update slurm node state to idle failed"
        return 1
    fi
}
# 检测gpu卡型号和数目
function check_gpu_model_and_num() {
    # gpu检测
    if [ -f "/usr/bin/nvidia-smi" ] ; then
        /usr/bin/nvidia-smi -L > /dev/null 2>&1
        ret_code=$?
        if [ $ret_code -eq 0 ] ; then
	        gpudata=(`/usr/bin/nvidia-smi --query-gpu=gpu_name --format=csv,noheader | awk '{gsub(" ", "_", $0); print tolower($0)}'`)
	        model=${gpudata[0]}
	        num=${#gpudata[@]}
            echo $model $num
            return 0
        else
           return 1
        fi
    else
        return 1
    fi
}
# 检测并设置sge slaver节点实际资源
function set_sge_slave_resource() {
    hostname=`hostname`
    core=`/usr/bin/nproc`
    mem=`/usr/bin/free -gh | sed -n '2p' | awk '{print $2}'`
    # ubuntu输出为Gi,需去除i
    mem=${mem//i/}
    qconf -aattr exechost complex_values "slots=${core},m_thread=${core},mem_free=${mem}" ${hostname}
    if [[ $? -ne 0 ]]; then
        echo "modeify slave ${hostname} complex_values failed"
        return 1
    fi
    # 检测是否有GPU卡,修改单节点mc_template配置
    gpu_info=`check_gpu_model_and_num`
    ret_code=$?
    if [[ $ret_code -eq 0 ]]; then
        gpu_num=`echo $gpu_info|awk '{print $2}'`
        host_name=`hostname`
        qconf -aattr exechost complex_values "gpu=$gpu_num" $host_name
        ret_code=$?
        if [[ $ret_code -ne 0 ]]; then
            echo "qconf set sge execd gpu resources failed"
            return 1
        fi
    fi
    return 0
}

function add_work_for_hosts() {
    chown -R work /etc/hosts
}

parse_args $@
main $exec_func
