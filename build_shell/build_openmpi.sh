#! /bin/bash


CURRENT_DIR=$(cd $(dirname $0); pwd)
# 临时文件下载目录
TEMP_DOWNLOAD_DIR="/opt/downloads/$RANDOM"


BASE_INSTALL_DIR="/opt"

PMIX_DIR="$BASE_INSTALL_DIR/pmix/3.1.6"
HWLOC_DIR="$BASE_INSTALL_DIR/hwloc/2.8.0"
LIB_EVENT_DIR="$BASE_INSTALL_DIR/libevent/2.1.12"
UCX_DIR="$BASE_INSTALL_DIR/ucx/1.14.1"
KNEM_DIR="$BASE_INSTALL_DIR/knem"
OPENMPI_VERSION="4.1.5_ucx_knem"
OPENMPI_DIR="$BASE_INSTALL_DIR/openmpi/$OPENMPI_VERSION"

MODULE_HOME="/etc/modulefiles"

REDHAT=1
UBUNTU2004=2
UBUNTU2204=3

WITH_PMIX=0
WITH_HWLOC=0
WITH_LIBEVENT=0
WITH_UCX=0
WITH_KNEM=0
WITH_CUDA=0
WITH_ALL=0

CLEAN=1

ALLOW_INSTALL=0
ALLOW_MODULEFILE=0
SET_DEFAULT=0


function parse_args() {
    args=`getopt -o i,m,h -l help,with-all,with-pmix,with-knem,with-hwloc,with-libevent,with-ucx,with-cuda,default,not-clean,base_install_dir:,modulefile_home: -- "$@"`
    if [ $? != 0 ] ; then 
        echo "Parse error! Terminating..." >&2 ; 
        exit 1 ; 
    fi
    #echo $args
    eval set -- "$args"
    while true ; do
        case "$1" in
            -i) ALLOW_INSTALL=1; shift 1;;
            -m) ALLOW_MODULEFILE=1; shift 1;;
            --with-pmix) WITH_PMIX=1; shift 1;;
            --with-hwloc) WITH_HWLOC=1; shift 1;;
            --with-libevent) WITH_LIBEVENT=1; shift 1;;
            --with-ucx) WITH_UCX=1; shift 1;;
            --with-knem) WITH_KNEM=1; shift 1;;
            --with-cuda) WITH_CUDA=1; shift 1;;
            --with-all) WITH_ALL=1; shift 1;;
            --default) SET_DEFAULT=1; shift 1;;
            --not-clean) CLEAN=0; shift 1;;
            --base_install_dir) BASE_INSTALL_DIR="$2"; shift 2;;
            --modulefile_home) MODULE_HOME="$2"; shift 2;;
            -h) help; exit 0 ;;    
            --help) help; exit 0 ;;  
            --) shift ; break ;;
            *) echo "Parameters error!!!$1" ; exit 1 ;;        
        esac
    done
    exec_func=$1
}

function help() {
    echo "Usage: build_openmpi [options]"
    echo "Options:"
    echo " -i                     build and install openmpi"
    echo " -m                     build and install openmpi modulefiles"
    echo " --default              set openmpi modulefiles as default, only work when use -m"
    echo " --with-pmix            build and install pmix for openmpi"
    echo " --with-knem            build and install knem for openmpi"
    echo " --with-hwloc           build and install hwloc for openmpi"
    echo " --with-libevent        build and install libevent for openmpi"
    echo " --with-ucx             build and install ucx for openmpi"
    echo " --with-cuda            build and install ucx with cuda"
    echo " --with-all             build and install all module for openmpi with cuda and module file"
    echo " --base_install_dir     the bash dir to install softwares"
    echo " --modulefile_home      the home path to install modulefiles"
    echo ""
    echo " -h, --help             display this help"
    return 0
}

# 判断当前操作系统为centos或ubuntu
function check_env() {
    if [ -f "/etc/redhat-release" ]
    then
        echo $REDHAT
    elif [ -f "/etc/lsb-release" ]
    then
        source /etc/lsb-release
        if [ "$DISTRIB_RELEASE" == "20.04" ]; then
          echo $UBUNTU2004
        elif [ "$DISTRIB_RELEASE" == "22.04" ]; then
          echo $UBUNTU2204
        fi
    else
        echo 0
    fi
}

function init_local_variables(){
    PMIX_DIR="$BASE_INSTALL_DIR/pmix/3.1.6"
    HWLOC_DIR="$BASE_INSTALL_DIR/hwloc/2.8.0"
    LIB_EVENT_DIR="$BASE_INSTALL_DIR/libevent/2.1.12"
    UCX_DIR="$BASE_INSTALL_DIR/ucx/1.14.1"
    KNEM_DIR="$BASE_INSTALL_DIR/knem"

    OPENMPI_VERSION="4.1.5"
    # if [[ $WITH_UCX > 0 ]]; then
    #     OPENMPI_VERSION=$OPENMPI_VERSION"_ucx1.14.1"
    # fi

    # if [[ $WITH_KNEM > 0 ]]; then
    #     OPENMPI_VERSION=$OPENMPI_VERSION"_knem"
    # fi

    OPENMPI_DIR="$BASE_INSTALL_DIR/openmpi/$OPENMPI_VERSION"
    if [[ $WITH_ALL > 0 ]]; then
        WITH_PMIX=1
        WITH_HWLOC=1
        WITH_LIBEVENT=1
        WITH_UCX=1
        WITH_KNEM=1
        WITH_CUDA=1
        ALLOW_INSTALL=1
        ALLOW_MODULEFILE=1
        SET_DEFAULT=1
    fi
}

function install_dependence(){
    ret_code=`check_env`
    if [[ $ret_code == $REDHAT ]]
    then
        yum install -y numactl-devel environment-modules gcc-c++ openssl-devel
        if [[ $ALLOW_MODULEFILE == 0 ]]; then
            return 0
        fi
        mkdir -p $MODULE_HOME
        match_num=$(grep -c "^$MODULE_HOME" /usr/share/Modules/init/.modulespath)
        if [ $match_num == 0 ]; then
            echo $MODULE_HOME >> /usr/share/Modules/init/.modulespath
        fi
        source /usr/share/Modules/init/bash
    else
        if [[ $ret_code == $UBUNTU2204 ]]; then
            # 禁止弹出交互窗口，默认不重启服务，仅列出可选项
            sed -i "s/\#\$nrconf{restart} = 'i'/\$nrconf{restart} = 'l'/" /etc/needrestart/needrestart.conf
        fi
        apt update && apt -y install unzip vim environment-modules libssl-dev
        if [[ $ALLOW_MODULEFILE == 0 ]]; then
            return 0
        fi
        mkdir -p $MODULE_HOME
        match_num=$(grep -c "^$MODULE_HOME" /usr/share/modules/init/.modulespath)
        if [ $match_num == 0 ]; then
            echo $MODULE_HOME >> /usr/share/modules/init/.modulespath
        fi
        source /usr/share/modules/init/bash
    fi
    module use $MODULE_HOME
    return 0
}

function install_cuda_module() {
    if [ ! -d /usr/local/cuda ]
    then
      echo "no cuda dir found"
      return 1
    fi
    
    cuda_dir_names=$(ls /usr/local/ | grep cuda-)

    for x in $cuda_dir_names
    do

        if [ ! -d /usr/local/$x ]
        then
          echo "no cuda found in /usr/local/$x"
          continue
        fi
        if [ ! -f /usr/local/$x/bin/nvcc ]
        then
          echo "no cuda nvcc found in /usr/local/$x"
          continue
        fi
        cuda_dir_name=$x
        break
    done

    
    if [[ $cuda_dir_name == "" ]]
    then
      echo "no cuda found"
      return 1
    fi
    

    CUDA_DIR="/usr/local/$cuda_dir_name"
    MODULEFILE_VERSION=${cuda_dir_name:5}
    MODULEFILE_DIR="$MODULE_HOME/cuda"

    #cuda module file
    mkdir -p $MODULEFILE_DIR
    rm -f build_cuda_modulefile.sh
    wget https://chpc.bj.bcebos.com/packages/build_shell/build_cuda_modulefile.sh
    bash build_cuda_modulefile.sh "$CUDA_DIR" "$MODULEFILE_VERSION" > $MODULEFILE_DIR/$MODULEFILE_VERSION
    echo -e "#%Module\n\nset ModulesVersion $MODULEFILE_VERSION" > $MODULEFILE_DIR/.version
}

function prepare_cuda(){

    module load cuda
    if [[ $CUDA_HOME == "" ]]; then
        install_cuda_module
        module load cuda
        if [[ $CUDA_HOME == "" ]]; then
          echo "no CUDA_HOME found"
          return 1
        fi
    fi

    return 0
}

function install_hwloc(){
    cd $TEMP_DOWNLOAD_DIR

    if [[ -f $HWLOC_DIR"/lib/libhwloc.so" ]]; then
        echo "hwloc already installed, ignore"
        return 0
    fi

    rm -f hwloc-hwloc-2.8.0.tar.gz
    wget https://chpc.bj.bcebos.com/packages/hwloc-hwloc-2.8.0.tar.gz

    rm -rf hwloc-hwloc-2.8.0
    tar -xf hwloc-hwloc-2.8.0.tar.gz

    cd hwloc-hwloc-2.8.0
    rm -rf $HWLOC_DIR
    ./autogen.sh
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "hwloc autogen.sh failed"
        return 1
    fi

    ./configure --prefix=$HWLOC_DIR
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "hwloc configure failed"
        return 1
    fi
    make -j && make install

    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "hwloc make failed"
        return 1
    fi

    # install modulefiles if allow
    if [[ $ALLOW_MODULEFILE > 0 ]]; then
        cd $TEMP_DOWNLOAD_DIR
        HWLOC_MODULEFILE_DIR="$MODULE_HOME/hwloc"
        mkdir -p $HWLOC_MODULEFILE_DIR
        rm -f build_hwloc_modulefile.sh
        wget https://chpc.bj.bcebos.com/packages/build_shell/build_hwloc_modulefile.sh && bash build_hwloc_modulefile.sh "$HWLOC_DIR" "2.8.0" "--prefix=$HWLOC_DIR" > $HWLOC_MODULEFILE_DIR/2.8.0

        if [[ SET_DEFAULT > 0 ]]; then
            echo -e "#%Module\n\nset ModulesVersion 2.8.0" > $HWLOC_MODULEFILE_DIR/.version
        fi
    fi
    return 0


}

function install_libevent(){
    cd $TEMP_DOWNLOAD_DIR

    if [[ -f $LIB_EVENT_DIR"/lib/libevent_core.so" ]]; then
        echo "libevent already installed, ignore"
        return 0
    fi

    rm -f libevent-2.1.12-stable.tar.gz
    wget https://chpc.bj.bcebos.com/packages/libevent-2.1.12-stable.tar.gz

    rm -rf libevent-2.1.12-stable
    tar -xf libevent-2.1.12-stable.tar.gz

    cd $TEMP_DOWNLOAD_DIR/libevent-2.1.12-stable
    rm -rf $LIB_EVENT_DIR
    ./configure --prefix=$LIB_EVENT_DIR
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "libevent configure failed"
        return 1
    fi
    make -j && make install

    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "libevent make failed"
        return 1
    fi
    return 0

}

function install_pmix(){
    cd $TEMP_DOWNLOAD_DIR

    if [[ -f $PMIX_DIR"/lib/libpmix.la" ]]; then
        echo "pmix already installed, ignore"
        return 0
    fi

    rm -f pmix-3.1.6.tar.gz
    wget https://slurm-prod.bj.bcebos.com/packages/pmix-3.1.6.tar.gz

    rm -rf pmix-3.1.6
    tar -xf pmix-3.1.6.tar.gz

    cd $TEMP_DOWNLOAD_DIR/pmix-3.1.6
    rm -rf $PMIX_DIR
    softopt="--prefix=$PMIX_DIR"
    if [[ $WITH_LIBEVENT > 0 ]]; then
        softopt=$softopt" --with-libevent=$LIB_EVENT_DIR"
    fi

    if [[ $WITH_HWLOC > 0 ]]; then
        softopt=$softopt" --with-hwloc=$HWLOC_DIR"
    fi

    ./configure $softopt


    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "pmix configure failed"
        return 1
    fi
    make -j && make install

    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "pmix make failed"
        return 1
    fi
    return 0

}


function install_ucx(){
    cd $TEMP_DOWNLOAD_DIR

    if [[ -f $UCX_DIR"/lib/libucs.so" ]]; then
        echo "ucx already installed, ignore"
        return 0
    fi

    softopt="--prefix=$UCX_DIR  --enable-mt"

    if [[ $WITH_CUDA > 0 ]]; then
        prepare_cuda
        ret_code=$?
        if [[ $ret_code -ne 0 ]]; then
            echo "build ucx with cuda failed"
            return 1
        fi
        softopt=$softopt" --with-cuda=$CUDA_HOME"
    fi
    

    rm -f ucx-1.14.1.tar.gz
    wget https://chpc.bj.bcebos.com/packages/ucx-1.14.1.tar.gz

    rm -rf ucx-1.14.1
    tar -xf ucx-1.14.1.tar.gz

    cd $TEMP_DOWNLOAD_DIR/ucx-1.14.1
    rm -rf $UCX_DIR



    ./configure $softopt
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "ucx configure failed"
        return 1
    fi
    make -j && make install

    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "ucx make failed"
        return 1
    fi
    return 0

}


function install_knem(){
    cd $TEMP_DOWNLOAD_DIR

    if [[ -f $KNEM_DIR"/include/knem_io.h" ]]; then
        echo "knem already installed, ignore"
        return 0
    fi

    rm -f knem.zip
    wget https://chpc.bj.bcebos.com/packages/knem.zip

    rm -rf knem
    unzip knem.zip

    cd $TEMP_DOWNLOAD_DIR/knem
    rm -rf $KNEM_DIR
    ./autogen.sh
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "knem autogen.sh failed"
        return 1
    fi

    ./configure --prefix=$KNEM_DIR
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "knem configure failed"
        return 1
    fi

    make -j && make install
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "ucx make failed"
        return 1
    fi
    return 0

}

function install_openmpi(){
    softopt=$1
    cd $TEMP_DOWNLOAD_DIR
    rm -f openmpi-4.1.5.tar.gz
    # slurm安装
    wget https://chpc.bj.bcebos.com/packages/openmpi-4.1.5.tar.gz
    rm -rf openmpi-4.1.5
    tar -xf openmpi-4.1.5.tar.gz

    cd $TEMP_DOWNLOAD_DIR/openmpi-4.1.5
    rm -rf $OPENMPI_DIR
    # ./configure --prefix=$OPENMPI_DIR --with-pmix=$PMIX_DIR --with-ucx=$UCX_DIR --with-hwloc=$HWLOC_DIR --with-libevent=$LIB_EVENT_DIR --with-knem=$KNEM_DIR
    ./configure $softopt
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "openmpi configure failed"
        return 1
    fi

    make -j && make install
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "openmpi make failed"
        return 1
    fi
    return 0
}

function main(){
    set -x 

    if [ ! -d $TEMP_DOWNLOAD_DIR ]
    then
        mkdir -p $TEMP_DOWNLOAD_DIR
    fi
    cd $TEMP_DOWNLOAD_DIR

    rm -rf *

    init_local_variables

    #statements
    install_dependence
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo -e "\033[31m install dependence failed \033[0m"
        return 1
    fi


    opt="--prefix=$OPENMPI_DIR"

    if [[ $WITH_CUDA > 0 ]]; then
        prepare_cuda
        ret_code=$?
        if [[ $ret_code -ne 0 ]]; then
            echo -e "\033[31mno cuda found\033[0m"
            return 1
        fi
    fi
    

    # install hwloc if allow
    if [[ $WITH_HWLOC > 0 ]]; then
        install_hwloc
        ret_code=$?
        if [[ $ret_code -ne 0 ]]; then
            echo -e "\033[31m install hwloc failed \033[0m"
            return 1
        fi
        opt=$opt" --with-hwloc=$HWLOC_DIR"
    fi

    
    # install hwloc if allow
    if [[ $WITH_LIBEVENT > 0 ]]; then
        install_libevent
        ret_code=$?
        if [[ $ret_code -ne 0 ]]; then
            echo -e "\033[31m install libevent failed \033[0m"
            return 1
        fi
        opt=$opt" --with-libevent=$LIB_EVENT_DIR"
    fi
    

    if [[ $WITH_PMIX > 0 ]]; then
        install_pmix
        ret_code=$?
        if [[ $ret_code -ne 0 ]]; then
            echo -e "\033[31m install pmix failed \033[0m"
            return 1
        fi
        opt=$opt" --with-pmix=$PMIX_DIR"
    fi
    

    if [[ $WITH_UCX > 0 ]]; then
        install_ucx
        ret_code=$?
        if [[ $ret_code -ne 0 ]]; then
            echo -e "\033[31m install ucx failed \033[0m"
            return 1
        fi
        opt=$opt" --with-ucx=$UCX_DIR"
    fi
    

    if [[ $WITH_KNEM > 0 ]]; then
        install_knem
        ret_code=$?
        if [[ $ret_code -ne 0 ]]; then
            echo -e "\033[31m install knem failed \033[0m"
            return 1
        fi
        opt=$opt" --with-knem=$KNEM_DIR"
    fi

    if [[ $ALLOW_INSTALL > 0 ]]; then
        install_openmpi "$opt"
        ret_code=$?
        if [[ $ret_code -ne 0 ]]; then
            echo -e "\033[31m install openmpi failed \033[0m"
            return 1
        fi
    fi

    
    # install modulefiles if allow
    if [[ $ALLOW_MODULEFILE > 0 ]]; then
        cd $TEMP_DOWNLOAD_DIR
        MPI_MODULEFILE_DIR="$MODULE_HOME/mpi"
        mkdir -p $MPI_MODULEFILE_DIR
        rm -f build_openmpi_modulefile.sh
        wget https://chpc.bj.bcebos.com/packages/build_shell/build_openmpi_modulefile.sh && bash build_openmpi_modulefile.sh "$OPENMPI_DIR" "$OPENMPI_VERSION" "$opt" > $MPI_MODULEFILE_DIR/openmpi-$OPENMPI_VERSION

        if [[ SET_DEFAULT > 0 ]]; then
            echo -e "#%Module\n\nset ModulesVersion openmpi-$OPENMPI_VERSION" > $MPI_MODULEFILE_DIR/.version
        fi
    fi
    
    # clean after install
    if [[ $CLEAN > 0 ]]; then
        rm -rf $TEMP_DOWNLOAD_DIR
        rm -f $CURRENT_DIR/build_openmpi.sh
    fi
}

parse_args $@
main
