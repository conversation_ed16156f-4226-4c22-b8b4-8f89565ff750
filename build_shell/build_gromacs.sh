#! /bin/bash


CURRENT_DIR=$(cd $(dirname $0); pwd)
# 临时文件下载目录
TEMP_DOWNLOAD_DIR="/opt/downloads/$RANDOM"


BASE_INSTALL_DIR="/opt"

FFTW_DIR="$BASE_INSTALL_DIR/fftw/3.3.10"
CMAKE_DIR="$BASE_INSTALL_DIR/cmake/3.26.4"

GROMACS_VERSION="2023.1"
GROMACS_DIR="$BASE_INSTALL_DIR/gromacs/$GROMACS_VERSION"


MODULE_HOME="/etc/modulefiles"


WITH_MPI=0
WITH_CUDA=0


REDHAT=1
UBUNTU2004=2
UBUNTU2204=3

WITH_LOCAL_FFTW=1

CLEAN=1

ALLOW_INSTALL=0
ALLOW_MODULEFILE=0
SET_DEFAULT=0


function parse_args() {
    args=`getopt -o i,m,h -l help,download-fftw,default,with-mpi,with-cuda,not-clean,base_install_dir:,modulefile_home: -- "$@"`
    if [ $? != 0 ] ; then 
        echo "Parse error! Terminating..." >&2 ; 
        exit 1 ; 
    fi
    #echo $args
    eval set -- "$args"
    while true ; do
        case "$1" in
            -i) ALLOW_INSTALL=1; shift 1;;
            -m) ALLOW_MODULEFILE=1; shift 1;;
            --default) SET_DEFAULT=1; shift 1;;
            --with-mpi) WITH_MPI=1; shift 1;;
            --with-cuda) WITH_CUDA=1; shift 1;;
            --download-fftw) WITH_LOCAL_FFTW=0; shift 1;;
            --not-clean) CLEAN=0; shift 1;;
            --base_install_dir) BASE_INSTALL_DIR="$2"; shift 2;;
            --modulefile_home) MODULE_HOME="$2"; shift 2;;
            -h) help; exit 0 ;;    
            --help) help; exit 0 ;;  
            --) shift ; break ;;
            *) echo "Parameters error!!!$1" ; exit 1 ;;        
        esac
    done
    exec_func=$1
}

function help() {
    echo "Usage: build_gromacs [options]"
    echo "Options:"
    echo " -i                     build and install gromacs"
    echo " -m                     build and install gromacs modulefiles"
    echo " --default              set gromacs modulefiles as default, only work when use -m"
    echo " --with-mpi             build and install gromacs with mpi, install openmpi 4.1.5 and set to default if no mpi installed"
    echo " --with-cuda            build and install gromacs with cuda"
    echo " --download-fftw        download and build fftw by gromacs, default is build local fftw3"
    echo " --base_install_dir     the bash dir to install softwares"
    echo " --modulefile_home      the home path to install modulefiles"
    echo ""
    echo " -h, --help             display this help"
    return 0
}

# 判断当前操作系统为centos或ubuntu
function check_env() {
    if [ -f "/etc/redhat-release" ]
    then
        echo $REDHAT
    elif [ -f "/etc/lsb-release" ]
    then
        source /etc/lsb-release
        if [ "$DISTRIB_RELEASE" == "20.04" ]; then
          echo $UBUNTU2004
        elif [ "$DISTRIB_RELEASE" == "22.04" ]; then
          echo $UBUNTU2204
        fi
    else
        echo 0
    fi
}

function init_local_variables(){
    FFTW_DIR="$BASE_INSTALL_DIR/fftw/3.3.10"
    CMAKE_DIR="$BASE_INSTALL_DIR/cmake/3.26.4"

    GROMACS_VERSION="2023.1"
    

    # if [[ $WITH_CUDA > 0 ]]; then
    #     GROMACS_VERSION=$GROMACS_VERSION"_cuda"
    # fi

    # if [[ $WITH_MPI > 0 ]]; then
    #     GROMACS_VERSION=$GROMACS_VERSION"_mpi"
    # fi

    GROMACS_DIR="$BASE_INSTALL_DIR/gromacs/$GROMACS_VERSION"
}


function install_dependence(){
    ret_code=`check_env`
    if [[ $ret_code == $REDHAT ]]
    then
        yum install -y numactl-devel environment-modules gcc-c++ openssl-devel
        if [[ $ALLOW_MODULEFILE == 0 ]]; then
            return 0
        fi
        mkdir -p $MODULE_HOME
        match_num=$(grep -c "^$MODULE_HOME" /usr/share/Modules/init/.modulespath)
        if [ $match_num == 0 ]; then
            echo $MODULE_HOME >> /usr/share/Modules/init/.modulespath
        fi
        source /usr/share/Modules/init/bash
    else
        if [[ $ret_code == $UBUNTU2204 ]]; then
            # 禁止弹出交互窗口，默认不重启服务，仅列出可选项
            sed -i "s/\#\$nrconf{restart} = 'i'/\$nrconf{restart} = 'l'/" /etc/needrestart/needrestart.conf
        fi
        apt update && apt -y install unzip vim environment-modules libssl-dev
        if [[ $ALLOW_MODULEFILE == 0 ]]; then
            return 0
        fi
        mkdir -p $MODULE_HOME
        match_num=$(grep -c "^$MODULE_HOME" /usr/share/modules/init/.modulespath)
        if [ $match_num == 0 ]; then
            echo $MODULE_HOME >> /usr/share/modules/init/.modulespath
        fi
        source /usr/share/modules/init/bash
    fi

    module use $MODULE_HOME
    return 0
}

function install_fftw3(){
    cd $TEMP_DOWNLOAD_DIR


    if [[ -f $FFTW_DIR"/lib/libfftw3f.so" ]]; then
        echo "fftw3 already installed, ignore"
        return 0
    fi


    rm -f fftw-3.3.10.tar.gz
    wget https://chpc.bj.bcebos.com/software/fftw-3.3.10.tar.gz

    rm -rf fftw-3.3.10
    tar -xf fftw-3.3.10.tar.gz

    cd fftw-3.3.10
    rm -rf $FFTW_DIR

    MODULE_VERSION=3.3.10


    softopt=""
    if [[ $WITH_MPI > 0 ]]; then

        softopt="LDFLAGS=-L$MPI_HOME/lib CPPFLAGS=-I$MPI_HOME/include --enable-mpi"
        # MODULE_VERSION=$MODULE_VERSION"_mpi"
    fi
    
    softopt=$softopt" --prefix=$FFTW_DIR --enable-float --enable-threads --enable-openmp --enable-sse2 --enable-avx --enable-avx2 --enable-shared --enable-static --enable-fma"


    match_num=$(grep -c "avx512" /proc/cpuinfo)
    if [[ $match_num > 0 ]]; then
        softopt=$softopt" --enable-avx512"
    fi

    ./configure $softopt
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "fftw3 configure failed"
        return 1
    fi
    make -j && make install

    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "fftw3 make failed"
        return 1
    fi

    # install modulefiles if allow
    if [[ $ALLOW_MODULEFILE > 0 ]]; then
        cd $TEMP_DOWNLOAD_DIR
        MODULEFILE_DIR="$MODULE_HOME/fftw"
        mkdir -p $MODULEFILE_DIR
        rm -f build_fftw_modulefile.sh
        wget https://chpc.bj.bcebos.com/packages/build_shell/build_fftw_modulefile.sh && bash build_fftw_modulefile.sh "$CMAKE_DIR" "$MODULE_VERSION" "$softopt" > $MODULEFILE_DIR/$MODULE_VERSION

        if [[ SET_DEFAULT > 0 ]]; then
            echo -e "#%Module\n\nset ModulesVersion $MODULE_VERSION" > $MODULEFILE_DIR/.version
        fi
    fi
    module load fftw
    return 0


}

function install_cmake(){
    cd $TEMP_DOWNLOAD_DIR

    # if [[ -f $CMAKE_DIR"/bin/cmake" ]]; then
    #     echo "cmake already installed, ignore"
    #     module load cmake
    #     return 0
    # fi

    rm -f cmake-3.26.4-linux-x86_64.tar.gz
    wget https://chpc.bj.bcebos.com/software/cmake-3.26.4-linux-x86_64.tar.gz
    rm -rf cmake-3.26.4-linux-x86_64
    tar -xf cmake-3.26.4-linux-x86_64.tar.gz

    mkdir -p $CMAKE_DIR
    cp -rf cmake-3.26.4-linux-x86_64/* $CMAKE_DIR/
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "cp cmake files failed"
        return 1
    fi
    # install modulefiles if allow
    if [[ $ALLOW_MODULEFILE > 0 ]]; then
        cd $TEMP_DOWNLOAD_DIR
        MODULEFILE_DIR="$MODULE_HOME/cmake"
        mkdir -p $MODULEFILE_DIR
        rm -f build_cmake_modulefile.sh
        wget https://chpc.bj.bcebos.com/packages/build_shell/build_cmake_modulefile.sh && bash build_cmake_modulefile.sh "$CMAKE_DIR" > $MODULEFILE_DIR/3.26.4

        if [[ SET_DEFAULT > 0 ]]; then
            echo -e "#%Module\n\nset ModulesVersion 3.26.4" > $MODULEFILE_DIR/.version
        fi
    fi
    module load cmake
    return 0


}


function prepare_mpi(){
    cd $TEMP_DOWNLOAD_DIR

    module load mpi
    if [[ $MPI_HOME == "" ]]; then
        echo "no MPI_HOME found, now install openmpi..."
        wget "https://chpc.bj.bcebos.com/packages/build_shell/build_openmpi.sh"
        if [[ $WITH_CUDA > 0 ]]; then
            bash build_openmpi.sh -i -m --default --with-ucx --with-cuda --base_install_dir="$BASE_INSTALL_DIR" --modulefile_home="$MODULE_HOME"
        else
            bash build_openmpi.sh -i -m --default --with-ucx --base_install_dir="$BASE_INSTALL_DIR" --modulefile_home="$MODULE_HOME"
        fi
        
    fi

    module load mpi
    if [[ $MPI_HOME == "" ]]; then
        echo "no MPI_HOME found and install openmpi failed"
        return 1

    fi
    return 0

}
function install_cuda_module() {
    if [ ! -d /usr/local/cuda ]
    then
      echo "no cuda dir found"
      return 1
    fi

    cuda_dir_names=$(ls /usr/local/ | grep cuda-)

    for x in $cuda_dir_names
    do

        if [ ! -d /usr/local/$x ]
        then
          echo "no cuda found in /usr/local/$x"
          continue
        fi
        if [ ! -f /usr/local/$x/bin/nvcc ]
        then
          echo "no cuda nvcc found in /usr/local/$x"
          continue
        fi
        cuda_dir_name=$x
        break
    done


    if [[ $cuda_dir_name == "" ]]
    then
      echo "no cuda found"
      return 1
    fi


    CUDA_DIR="/usr/local/$cuda_dir_name"
    MODULEFILE_VERSION=${cuda_dir_name:5}
    MODULEFILE_DIR="$MODULE_HOME/cuda"

    #cuda module file
    mkdir -p $MODULEFILE_DIR
    rm -f build_cuda_modulefile.sh
    wget https://chpc.bj.bcebos.com/packages/build_shell/build_cuda_modulefile.sh
    bash build_cuda_modulefile.sh "$CUDA_DIR" "$MODULEFILE_VERSION" > $MODULEFILE_DIR/$MODULEFILE_VERSION
    echo -e "#%Module\n\nset ModulesVersion $MODULEFILE_VERSION" > $MODULEFILE_DIR/.version
}

function prepare_cuda(){

    module load cuda
    if [[ $CUDA_HOME == "" ]]; then
        install_cuda_module
        module load cuda
        if [[ $CUDA_HOME == "" ]]; then
          echo "no CUDA_HOME found"
          return 1
        fi
    fi

    return 0
}


function install_gromacs(){
    cd $TEMP_DOWNLOAD_DIR
    softopt=$1
    
    rm -f regressiontests-2023.1.tar.gz
    wget https://chpc.bj.bcebos.com/software/regressiontests-2023.1.tar.gz
    rm -rf regressiontests-2023.1
    tar -xf regressiontests-2023.1.tar.gz


    rm -f gromacs-2023.1.tar.gz
    wget https://chpc.bj.bcebos.com/software/gromacs-2023.1.tar.gz
    rm -rf gromacs-2023.1
    tar -xf gromacs-2023.1.tar.gz

    rm -rf $GROMACS_DIR
    cd $TEMP_DOWNLOAD_DIR/gromacs-2023.1
    mkdir build
    cd build

    cmake .. $softopt
    
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "gromacs cmake failed"
        return 1
    fi

    make -j && make install
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "gromacs make failed"
        return 1
    fi
    return 0
}

function main(){
    set -x 

    if [ ! -d $TEMP_DOWNLOAD_DIR ]
    then
        mkdir -p $TEMP_DOWNLOAD_DIR
    fi
    cd $TEMP_DOWNLOAD_DIR

    rm -rf *

    init_local_variables

    #statements
    install_dependence
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "install dependence failed"
        return 1
    fi


    if [[ $ALLOW_INSTALL > 0 ]]; then


        opt=""

        # load cuda if allow
        if [[ $WITH_CUDA > 0 ]]; then
            prepare_cuda
            ret_code=$?
            if [[ $ret_code -ne 0 ]]; then
                echo "prepare_cuda failed"
                return 1
            fi
            opt=$opt" -DGMX_GPU=CUDA"
        fi
        
        # load mpi or install mpi if allow
        if [[ $WITH_MPI > 0 ]]; then
            prepare_mpi
            ret_code=$?
            if [[ $ret_code -ne 0 ]]; then
                echo "prepare_mpi failed"
                return 1
            fi
            opt=$opt" -DGMX_MPI=on"
        fi

        install_cmake
        ret_code=$?
        if [[ $ret_code -ne 0 ]]; then
            echo "install_cmake failed"
            return 1
        fi


        # install fftw3 if allow
        if [[ $WITH_LOCAL_FFTW > 0 ]]; then

            install_fftw3
            ret_code=$?
            if [[ $ret_code -ne 0 ]]; then
                echo "install fftw3 failed"
                return 1
            fi
            opt=$opt" -DGMX_BUILD_OWN_FFTW=OFF -DGMX_FFT_LIBRARY=fftw3 -DFFTWF_LIBRARY=$FFTW_DIR/lib/libfftw3f.so -DFFTWF_INCLUDE_DIR=$FFTW_DIR/include" 
        else
            opt=$opt" -DGMX_BUILD_OWN_FFTW=ON"
        fi

        opt=$opt" -DREGRESSIONTEST_DOWNLOAD=OFF -DREGRESSIONTEST_PATH=$TEMP_DOWNLOAD_DIR/regressiontests-2023.1 -DCMAKE_INSTALL_PREFIX=$GROMACS_DIR"
        install_gromacs "$opt"
        ret_code=$?
        if [[ $ret_code -ne 0 ]]; then
            echo "install_gromacs failed"
            return 1
        fi
    fi

    
    # install modulefiles if allow
    if [[ $ALLOW_MODULEFILE > 0 ]]; then
        cd $TEMP_DOWNLOAD_DIR
        GROMACS_MODULEFILE_DIR="$MODULE_HOME/gromacs"
        mkdir -p $GROMACS_MODULEFILE_DIR
        rm -f build_gromacs_modulefile.sh
        wget https://chpc.bj.bcebos.com/packages/build_shell/build_gromacs_modulefile.sh && bash build_gromacs_modulefile.sh "$GROMACS_DIR" "$GROMACS_VERSION" "$opt" > $GROMACS_MODULEFILE_DIR/$GROMACS_VERSION

        if [[ SET_DEFAULT > 0 ]]; then
            echo -e "#%Module\n\nset ModulesVersion $GROMACS_VERSION" > $GROMACS_MODULEFILE_DIR/.version
        fi
    fi
    
    # clean after install
    if [[ $CLEAN > 0 ]]; then
        rm -rf $TEMP_DOWNLOAD_DIR
        rm -f $CURRENT_DIR/build_gromacs.sh
    fi
}
    
parse_args $@
main
