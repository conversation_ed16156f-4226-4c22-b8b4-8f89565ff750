#! /bin/bash

#define parameters which are passed in.
HOME=$1
VERSION=$2

#define the template.
cat  << EOF
#%Module######################################################################
##
##      gcc software modulefile
##


proc ModulesHelp { } {
    puts stderr "\t gcc:$VERSION"
    puts stderr "\t install step $ ./contrib/download_prerequisites"
    puts stderr "\t install step $ ./configure --prefix=$HOME --enable-languages=c,c++,objc,obj-c++,fortran --disable-multilib --disable-checking"
    puts stderr "\t install step $ make -j"
    puts stderr "\t install step $ make install"
    puts stderr "\t usage: $ gcc --help"
}

module-whatis "For more information, $ module help gcc/$VERSION"

conflict gcc

set root $HOME

prepend-path INCLUDE  \$root/include
prepend-path LD_LIBRARY_PATH \$root/libexec
prepend-path LD_LIBRARY_PATH \$root/lib
prepend-path LD_LIBRARY_PATH \$root/lib64
prepend-path PATH \$root/bin
prepend-path MANPATH \$root/share/man
EOF