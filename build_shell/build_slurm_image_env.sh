#! /bin/bash

PKG_URL="https://chpc-internal.bj.bcebos.com/tools/chpc_image_builder.zip"
CURRENT_DIR=$(cd $(dirname $0); pwd)
# 临时文件下载目录
TEMP_DOWNLOAD_DIR="/opt/image_builder_$RANDOM"

BASE_INSTALL_DIR="/opt"
MODULE_HOME="/etc/modulefiles"

WITH_MPI=0
WITH_CUDA=0
WITH_SLURM=0

REDHAT=1
UBUNTU2004=2
UBUNTU2204=3

WITH_SGE=0
WITH_SLURM=0

CLEAN=1

ALLOW_INSTALL=0
ALLOW_MODULEFILE=0
SET_DEFAULT=0

function parse_args() {
    args=`getopt -o i,m,h -l help,with-slurm,with-sge,with-mpi,with-cuda,not-clean,base_install_dir:,modulefile_home: -- "$@"`
    if [ $? != 0 ] ; then
        echo "Parse error! Terminating..." >&2 ;
        exit 1 ;
    fi
    #echo $args
    eval set -- "$args"
    while true ; do
        case "$1" in
            #-i) ALLOW_INSTALL=1; shift 1;;
            -m) ALLOW_MODULEFILE=1; shift 1;;
            #--default) SET_DEFAULT=1; shift 1;;
            #--with-mpi) WITH_MPI=1; shift 1;;
            #--with-cuda) WITH_CUDA=1; shift 1;;
            --with-sge) WITH_SGE=1; shift 1;;
            --with-slurm) WITH_SLURM=1; shift 1;;
            --not-clean) CLEAN=0; shift 1;;
            #--base_install_dir) BASE_INSTALL_DIR="$2"; shift 2;;
            #--modulefile_home) MODULE_HOME="$2"; shift 2;;
            -h) help; exit 0 ;;
            --help) help; exit 0 ;;
            --) shift ; break ;;
            *) echo "Parameters error!!!$1" ; exit 1 ;;
        esac
    done
    exec_func=$1
}

function help() {
    echo "Usage: build_slurm_image_env [options]"
    echo "Options:"
    echo " -m                     build and install modulefiles for software:cuda,mpi"
    #echo " --default              set installed modulefiles as default, only work when use -m"
    #echo " --with-mpi             build and install slurm/sge with mpi, install openmpi 4.1.5 and set to default if no mpi installed"
    #echo " --with-cuda            build and install slurm/sge with cuda"
    echo " --with-sge             build and install sge"
    echo " --with-slurm           build and install slurm (default)"
    #echo " --base_install_dir     the bash dir to install softwares, default is /opt"
   # echo " --modulefile_home      the home path to install modulefiles, default is /etc/modulefiles"
    echo ""
    echo " -h, --help             display this help"
    return 0
}

# 判断当前操作系统为centos或ubuntu
function check_env() {
    if [ -f "/etc/redhat-release" ]
    then
        echo $REDHAT
    elif [ -f "/etc/lsb-release" ]
    then
        source /etc/lsb-release
        if [ "$DISTRIB_RELEASE" == "20.04" ]; then
          echo $UBUNTU2004
        elif [ "$DISTRIB_RELEASE" == "22.04" ]; then
          echo $UBUNTU2204
        fi
    else
        echo 0
    fi
}

function init_local_variables(){
    return 0
}


function install_dependence(){
    ret_code=`check_env`
    if [[ $ret_code == $REDHAT ]]
    then
        yum install -y numactl-devel environment-modules gcc-c++ openssl-devel
        if [[ $ALLOW_MODULEFILE == 0 ]]; then
            return 0
        fi
        match_num=$(grep -c "^$MODULE_HOME" /usr/share/Modules/init/.modulespath)
        if [ $match_num == 0 ]; then
            mkdir -p $MODULE_HOME
            echo $MODULE_HOME >> /usr/share/Modules/init/.modulespath
        fi
    else
        if [[ $ret_code == $UBUNTU2204 ]]; then
            # 禁止弹出交互窗口，默认不重启服务，仅列出可选项
            sed -i "s/\#\$nrconf{restart} = 'i'/\$nrconf{restart} = 'l'/" /etc/needrestart/needrestart.conf
        fi
        apt update && apt -y install unzip vim environment-modules
        if [[ $ALLOW_MODULEFILE == 0 ]]; then
            return 0
        fi
        match_num=$(grep -c "^$MODULE_HOME" /usr/share/modules/init/.modulespath)
        if [ $match_num == 0 ]; then
            mkdir -p $MODULE_HOME
            echo $MODULE_HOME >> /usr/share/modules/init/.modulespath
        fi

    fi
}

function wait_cuda_install() {
    result=$(ps -ef|grep auto_install.sh|egrep -v 'grep|gdb')
    while [[ $result != "" ]]; do
        echo "cuda is installing, sleep 10s"
        sleep 10s
        result=$(ps -ef|grep auto_install.sh|egrep -v 'grep|gdb')
    done

}

function install_cuda_modulefiles() {
    if [ ! -d /usr/local/cuda ]
    then
      echo "no cuda dir found"
      exit 0
    fi

    cuda_dir_name=$(ls /usr/local/ | grep cuda-)

    if [ ! -d /usr/local/$cuda_dir_name ]
    then
      echo "no cuda found"
      exit 0
    fi

    if [ ! -f /usr/local/$cuda_dir_name/bin/nvcc ]
    then
      echo "no cuda nvcc found"
      exit 0
    fi

    CUDA_DIR="/usr/local/$cuda_dir_name"
    MODULEFILE_VERSION=${cuda_dir_name:5}
    MODULEFILE_DIR="$MODULE_HOME/cuda"

    #cuda module file
    mkdir -p $MODULEFILE_DIR
    rm -f build_cuda_modulefile.sh
    wget https://chpc.bj.bcebos.com/packages/build_shell/build_cuda_modulefile.sh
    bash build_cuda_modulefile.sh "$CUDA_DIR" "$MODULEFILE_VERSION" > $MODULEFILE_DIR/$MODULEFILE_VERSION
    echo -e "#%Module\n\nset ModulesVersion $MODULEFILE_VERSION" > $MODULEFILE_DIR/.version
}

function build_slurm_image_env() {
    cd $TEMP_DOWNLOAD_DIR
    wget "$PKG_URL" -O chpc_image_builder.zip
    unzip chpc_image_builder.zip
    cd chpc_image_builder/image
    bash build_slurm_image.sh
}

function build_sge_image_env() {
    cd $TEMP_DOWNLOAD_DIR
    wget "$PKG_URL" -O chpc_image_builder.zip
    unzip chpc_image_builder.zip
    cd chpc_image_builder/image
    bash build_sge_image.sh
}

function cleanEnv(){

    # clean hostname in /etc/hosts
    hostname=$(hostname)
    sed -i "/$hostname/d" /etc/hosts
}

function main(){
    set -x

    if [ ! -d $TEMP_DOWNLOAD_DIR ]
    then
        mkdir -p $TEMP_DOWNLOAD_DIR
    fi
    cd $TEMP_DOWNLOAD_DIR

    rm -rf *

    init_local_variables
    
    wait_cuda_install

    #statements
    install_dependence
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "install dependence failed"
        return 1
    fi

    if [[ $WITH_SLURM == 1 ]]; then
        build_slurm_image_env
        if [[ $ret_code -ne 0 ]]; then
            echo "build_slurm_image_env failed"
            return 1
        fi
    fi

    if [[ $WITH_SGE == 1 ]]; then
        build_sge_image_env
        if [[ $ret_code -ne 0 ]]; then
            echo "build_sge_image_env failed"
            return 1
        fi
    fi

    if [[ $ALLOW_MODULEFILE == 1 ]]; then
        install_cuda_modulefiles
        if [[ $ret_code -ne 0 ]]; then
            echo "install_cuda_modulefiles failed"
            return 1
        fi
    fi

    cleanEnv

    # clean after install
    if [[ $CLEAN > 0 ]]; then
        rm -rf $TEMP_DOWNLOAD_DIR
        rm -f $CURRENT_DIR/build_slurm_image_env.sh
        touch /root/.bash_history_tmp
        mv /root/.bash_history_tmp /root/.bash_history
    fi
}

parse_args $@
main
