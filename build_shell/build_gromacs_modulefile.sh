#! /bin/bash

#define parameters which are passed in.
HOME=$1
VERSION=$2
CONFIGURE_OPT=$3

#define the template.
cat  << EOF
#%Module 1.0
#
#  Gromacs module for use with 'environment-modules' package:

proc ModulesHelp { } {
    puts stderr "\t gromacs/$VERSION"
    puts stderr "\t gcc:9.4.0"
    puts stderr "\t install step $ tar gromacs-$VERSION.tar.gz && cd gromacs-$VERSION"
    puts stderr "\t install step $ mkdir build && cd build"
    puts stderr "\t install step $ cmake .. $CONFIGURE_OPT"
    puts stderr "\t install step $ make -j"
    puts stderr "\t install step $ make install"
    puts stderr "\t version: $ gmx_mpi --version"
}

module-whatis "For more information, $ module help gromacs/$VERSION"

conflict gromacs

set gromacs_root $HOME

prepend-path GMXBIN \$gromacs_root/bin
prepend-path GMXDATA \$gromacs_root/share/gromacs
prepend-path GMXLDLIB \$gromacs_root/lib64
prepend-path GMXMAN \$gromacs_root/share/man
prepend-path GROMACS_DIR \$gromacs_root
prepend-path LD_LIBRARY_PATH \$gromacs_root/lib64
prepend-path MANPATH \$gromacs_root/share/man
prepend-path PATH \$gromacs_root/bin
prepend-path PKG_CONFIG_PATH \$gromacs_root/lib64/pkgconfig
EOF