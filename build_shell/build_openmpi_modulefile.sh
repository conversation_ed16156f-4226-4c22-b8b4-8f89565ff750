#! /bin/bash

#define parameters which are passed in.
HOME=$1
VERSION=$2
CONFIGURE_OPT=$3

#define the template.
cat  << EOF
#%Module 1.0
#
#  OpenMPI module for use with 'environment-modules' package:

proc ModulesHelp { } {
    puts stderr "\t mpi/openmpi-$VERSION"
    puts stderr "\t gcc:9.4.0"
    puts stderr "\t install step $ ./configure $CONFIGURE_OPT"
    puts stderr "\t install step $ make -j"
    puts stderr "\t install step $ make install"
    puts stderr "\t usage: $ mpirun --help"
}


module-whatis "For more information, $ module help mpi/openmpi-$VERSION"


conflict     mpi
set mpi_root $HOME
setenv            MPI_VERSION    openmpi-$VERSION

prepend-path       PATH        \$mpi_root/bin
prepend-path      LD_LIBRARY_PATH \$mpi_root/lib
prepend-path      MANPATH     \$mpi_root/share/man
setenv          MPI_BIN     \$mpi_root/bin
setenv            MPI_SYSCONFIG   \$mpi_root/etc
setenv            MPI_INCLUDE \$mpi_root/include
setenv            MPI_LIB     \$mpi_root/lib
setenv            MPI_MAN     \$mpi_root/share/man
setenv          MPI_SUFFIX  _openmpi
setenv            MPI_HOME    \$mpi_root
EOF