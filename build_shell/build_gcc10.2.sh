#! /bin/bash

CURRENT_DIR=$(cd $(dirname $0); pwd)
# 临时文件下载目录
TEMP_DOWNLOAD_DIR="/opt/downloads/$RANDOM"

BASE_INSTALL_DIR="/opt"


GCC_VERSION="10.2.0"
GCC_DIR="$BASE_INSTALL_DIR/gcc/$GCC_VERSION"


MODULE_HOME="/etc/modulefiles"


REDHAT=1
UBUNTU2004=2
UBUNTU2204=3


CLEAN=1

ALLOW_INSTALL=0
ALLOW_MODULEFILE=0
SET_DEFAULT=1


function parse_args() {
    args=`getopt -o i,m,h -l help,default,not-clean,base_install_dir:,modulefile_home: -- "$@"`
    if [ $? != 0 ] ; then
        echo "Parse error! Terminating..." >&2 ;
        exit 1 ;
    fi
    #echo $args
    eval set -- "$args"
    while true ; do
        case "$1" in
            -i) ALLOW_INSTALL=1; shift 1;;
            -m) ALLOW_MODULEFILE=1; shift 1;;
            --not-clean) CLEAN=0; shift 1;;
            --base_install_dir) BASE_INSTALL_DIR="$2"; shift 2;;
            --modulefile_home) MODULE_HOME="$2"; shift 2;;
            -h) help; exit 0 ;;
            --help) help; exit 0 ;;
            --) shift ; break ;;
            *) echo "Parameters error!!!$1" ; exit 1 ;;
        esac
    done
    exec_func=$1
}

function help() {
    echo "Usage: build_gcc [options]"
    echo "Options:"
    echo " -i                     build and install gcc"
    echo " -m                     build and install gcc modulefiles"
    echo " --base_install_dir     the bash dir to install softwares"
    echo " --modulefile_home      the home path to install modulefiles"
    echo ""
    echo " -h, --help             display this help"
    return 0
}

# 判断当前操作系统为centos或ubuntu
function check_env() {
    if [ -f "/etc/redhat-release" ]
    then
        echo $REDHAT
    elif [ -f "/etc/lsb-release" ]
    then
        source /etc/lsb-release
        if [ "$DISTRIB_RELEASE" == "20.04" ]; then
          echo $UBUNTU2004
        elif [ "$DISTRIB_RELEASE" == "22.04" ]; then
          echo $UBUNTU2204
        fi
    else
        echo 0
    fi
}

function init_local_variables(){

    GCC_VERSION="10.2.0"

    GCC_DIR="$BASE_INSTALL_DIR/gcc/$GCC_VERSION"
}


function install_dependence(){
    ret_code=`check_env`
    if [[ $ret_code == $REDHAT ]]
    then
        yum install -y numactl-devel environment-modules gcc-c++ openssl-devel
        if [[ $ALLOW_MODULEFILE == 0 ]]; then
            return 0
        fi
        mkdir -p $MODULE_HOME
        match_num=$(grep -c "^$MODULE_HOME" /usr/share/Modules/init/.modulespath)
        if [ $match_num == 0 ]; then
            echo $MODULE_HOME >> /usr/share/Modules/init/.modulespath
        fi
        source /usr/share/Modules/init/bash
    else
        if [[ $ret_code == $UBUNTU2204 ]]; then
            # 禁止弹出交互窗口，默认不重启服务，仅列出可选项
            sed -i "s/\#\$nrconf{restart} = 'i'/\$nrconf{restart} = 'l'/" /etc/needrestart/needrestart.conf
        fi
        apt update && apt -y install unzip vim environment-modules libssl-dev
        if [[ $ALLOW_MODULEFILE == 0 ]]; then
            return 0
        fi
        mkdir -p $MODULE_HOME
        match_num=$(grep -c "^$MODULE_HOME" /usr/share/modules/init/.modulespath)
        if [ $match_num == 0 ]; then
            echo $MODULE_HOME >> /usr/share/modules/init/.modulespath
        fi
        source /usr/share/modules/init/bash

    fi
    module use $MODULE_HOME
    return 0
}



function install_gcc(){
    cd $TEMP_DOWNLOAD_DIR
    softopt=$1

    rm -f gcc-10.2.0-with-dependences.tar.gz
    wget https://chpc.bj.bcebos.com/software/gcc-10.2.0-with-dependences.tar.gz
    rm -rf gcc-10.2.0
    tar -xf gcc-10.2.0-with-dependences.tar.gz

    rm -rf $GCC_DIR
    cd $TEMP_DOWNLOAD_DIR/gcc-10.2.0
    ./configure --prefix=$GCC_DIR --enable-languages=c,c++,objc,obj-c++,fortran --disable-multilib --disable-checking
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "gcc configure failed"
        return 1
    fi

    make -j && make install
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "gcc make failed"
        return 1
    fi
    return 0
}

function main(){
    set -x

    if [ ! -d $TEMP_DOWNLOAD_DIR ]
    then
        mkdir -p $TEMP_DOWNLOAD_DIR
    fi
    cd $TEMP_DOWNLOAD_DIR

    rm -rf *

    init_local_variables

    #statements
    install_dependence
    ret_code=$?
    if [[ $ret_code -ne 0 ]]; then
        echo "install dependence failed"
        return 1
    fi


    if [[ $ALLOW_INSTALL > 0 ]]; then
        install_gcc
        ret_code=$?
        if [[ $ret_code -ne 0 ]]; then
            echo "install_gcc failed"
            return 1
        fi
    fi


    # install modulefiles if allow
    if [[ $ALLOW_MODULEFILE > 0 ]]; then
        cd $TEMP_DOWNLOAD_DIR
        GCC_MODULEFILE_DIR="$MODULE_HOME/gcc"
        mkdir -p $GCC_MODULEFILE_DIR
        rm -f build_gcc_modulefile.sh
        wget https://chpc.bj.bcebos.com/packages/build_shell/build_gcc_modulefile.sh && bash build_gcc_modulefile.sh "$GCC_DIR" "$GCC_VERSION" > $GCC_MODULEFILE_DIR/$GCC_VERSION

        if [[ SET_DEFAULT > 0 ]]; then
            echo -e "#%Module\n\nset ModulesVersion $GCC_VERSION" > $GCC_MODULEFILE_DIR/.version
        fi
    fi

    # clean after install
    if [[ $CLEAN > 0 ]]; then
        rm -rf $TEMP_DOWNLOAD_DIR
        rm -f $CURRENT_DIR/build_gcc.sh
    fi
}

parse_args $@
main
