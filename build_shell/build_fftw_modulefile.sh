#! /bin/bash

#define parameters which are passed in.
HOME=$1
VERSION=$2
CONFIGURE_OPT=$3

#define the template.
cat  << EOF
#%Module 1.0
#
#  FFTW module for use with 'environment-modules' package:

proc ModulesHelp { } {
    puts stderr "\t fftw/$VERSION"
    puts stderr "\t install step $ ./configure $CONFIGURE_OPT"
    puts stderr "\t install step $ make -j"
    puts stderr "\t install step $ make install"
}

module-whatis "For more information, $ module help fftw/$VERSION"

conflict     fftw
set FFTW_PATH $HOME
setenv FFTW_HOME \$FFTW_PATH

prepend-path PATH \$FFTW_PATH/bin
prepend-path LD_LIBRARY_PATH \$FFTW_PATH/lib
prepend-path LIBRARY_PATH \$FFTW_PATH/lib
prepend-path INCLUDE  \$FFTW_PATH/include
EOF