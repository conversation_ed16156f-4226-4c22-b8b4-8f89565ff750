#! /bin/bash

#define parameters which are passed in.
HOME=$1
VERSION=$2
CONFIGURE_OPT=$3

#define the template.
cat  << EOF
#%Module 1.0
#
#  hwloc module for use with 'environment-modules' package:

proc ModulesHelp { } {
    puts stderr "\t hwloc/$VERSION"
    puts stderr "\t install step $ ./autogen.sh"
    puts stderr "\t install step $ ./configure $CONFIGURE_OPT"
    puts stderr "\t install step $ make -j"
    puts stderr "\t install step $ make install"
}

module-whatis "For more information, $ module help hwloc/$VERSION"

conflict     hwloc
set hwloc_PATH $HOME
setenv HWLOC_HOME \$hwloc_PATH

prepend-path LD_LIBRARY_PATH \${hwloc_PATH}/lib
prepend-path LIBRARY_PATH \${hwloc_PATH}/lib
prepend-path CPATH \${hwloc_PATH}/include
prepend-path C_INCLUDE_PATH \${hwloc_PATH}/include
prepend-path PATH \${hwloc_PATH}/bin
EOF