[Unit]
# Description=open-ldap demon service
Wants=network-online.target

[Service]
Type=simple
EnvironmentFile=-/etc/sysconfig/slapd
PIDFile=/opt/ldap/2.5.13/var/run/slapd.pid
ExecStart=/opt/ldap/2.5.13/libexec/slapd -F /opt/ldap/2.5.13/etc/slapd.d
KillSignal=SIGTERM
TimeoutStopSec=5
KillMode=process
# Uncomment the following lines to disable logging through journald.
# NOTE: It may be preferable to set these through an override file instead.
#StandardOutput=null
#StandardError=null
[Install]
WantedBy=multi-user.target
