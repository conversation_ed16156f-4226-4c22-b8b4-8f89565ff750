/*
 * @Author: sunkaixuan01 <EMAIL>
 * @Date: 2022-12-16 15:14:06
 * @LastEditors: sunkaixuan01 <EMAIL>
 * @LastEditTime: 2022-12-16 15:21:52
 * @FilePath: \chpc_image_builder\ldap\ldap.go
 * @Description: ldap 启动命令
 */
package ldap

import (
	"github.com/astaxie/beego/logs"

	"baidu/hpc/chpc_image_builder/util"
)

// 启动ldap-server
func StartLdapServer() (err error) {
	args := []string{util.ShellSciptName, "startSlapdNoHA"}
	err = util.ExecCmd(args...)
	if err != nil {
		logs.Error("start ldap server fail,error info is %s", err.Error())
		return
	}
	return
}

// 启动ldap-client
func StartLdapClient(clusterParam util.ConsleParam) (err error) {
	args := []string{util.ShellSciptName, "startNslcd", "--masterHost", clusterParam.MasterHostName}
	err = util.ExecCmd(args...)
	if err != nil {
		logs.Error("start ldap client fail,error info is %s", err.Error())
		return
	}
	return
}
