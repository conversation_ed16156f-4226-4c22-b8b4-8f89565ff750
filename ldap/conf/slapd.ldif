#
# See slapd-config(5) for details on configuration options.
# This file should NOT be world readable.
#
dn: cn=config
objectClass: olcGlobal
cn: config
#
#
# Define global ACLs to disable default read access.
#
olcArgsFile: /opt/ldap/2.5.13/var/run/slapd.args
olcPidFile: /opt/ldap/2.5.13/var/run/slapd.pid
#
# Do not enable referrals until AFTER you have a working directory
# service AND an understanding of referrals.
#olcReferral:   ldap://root.openldap.org
#
# Sample security restrictions
#       Require integrity protection (prevent hijacking)
#       Require 112-bit (3DES or better) encryption for updates
#       Require 64-bit encryption for simple bind
#olcSecurity: ssf=1 update_ssf=112 simple_bind=64


#
# Load dynamic backend modules:
#
#dn: cn=module,cn=config
#objectClass: olcModuleList
#cn: module
#olcModulepath: /opt/ldap/2.5.13/libexec/openldap
#olcModuleload: back_mdb.la
#olcModuleload: back_ldap.la
#olcModuleload: back_passwd.la
#olcModuleload: back_shell.la


dn: cn=schema,cn=config
objectClass: olcSchemaConfig
cn: schema

include: file:///opt/ldap/2.5.13/etc/openldap/schema/core.ldif
include: file:///opt/ldap/2.5.13/etc/openldap/schema/cosine.ldif
include: file:///opt/ldap/2.5.13/etc/openldap/schema/nis.ldif
include: file:///opt/ldap/2.5.13/etc/openldap/schema/misc.ldif
include: file:///opt/ldap/2.5.13/etc/openldap/schema/inetorgperson.ldif
include: file:///opt/ldap/2.5.13/etc/openldap/schema/openldap.ldif
# Frontend settings
#
dn: olcDatabase=frontend,cn=config
objectClass: olcDatabaseConfig
objectClass: olcFrontendConfig
olcDatabase: frontend
#
# Sample global access control policy:
#       Root DSE: allow anyone to read it
#       Subschema (sub)entry DSE: allow anyone to read it
#       Other DSEs:
#               Allow self write access
#               Allow authenticated users read access
#               Allow anonymous users to authenticate
#
#olcAccess: to dn.base="" by * read
#olcAccess: to dn.base="cn=Subschema" by * read
#olcAccess: to *
#       by self write
#       by users read
#       by anonymous auth
#
# if no access controls are present, the default policy
# allows anyone and everyone to read anything but restricts
# updates to rootdn.  (e.g., "access to * by * read")
#
# rootdn can always read and write EVERYTHING!
#


#######################################################################
# LMDB database definitions
#######################################################################
#
dn: olcDatabase=mdb,cn=config
objectClass: olcDatabaseConfig
objectClass: olcMdbConfig
olcDatabase: mdb
olcDbMaxSize: 1073741824
olcSuffix: dc=baiduhpc,dc=com
olcRootDN: cn=Manager,dc=baiduhpc,dc=com
# Cleartext passwords, especially for the rootdn, should
# be avoided.  See slappasswd(8) and slapd-config(5) for details.
# Use of strong authentication encouraged.
# 默认密码9:V@_IMevA7S
olcRootPW: {SSHA}/yOPFfdUOJfsvMGpSj553n7/dPQdSnsi
# The database directory MUST exist prior to running slapd AND 
# should only be accessible by the slapd and slap tools.
# Mode 700 recommended.
olcDbDirectory: /opt/ldap/2.5.13/var/openldap-data
# Indices to maintain
olcDbIndex: objectClass eq

dn: olcDatabase=monitor,cn=config
objectClass: olcDatabaseConfig
olcDatabase: monitor
olcRootDN: cn=config
olcMonitoring: FALSE