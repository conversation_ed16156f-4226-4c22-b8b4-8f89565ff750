#! /bin/bash
###
 # @Author: sunkaixuan01 <EMAIL>
 # @Date: 2022-12-19 09:53:59
 # @LastEditors: sunkaixuan01 <EMAIL>
 # @LastEditTime: 2023-02-06 10:03:03
 # @PATHPath: \chpc_image_builder\ldap\centos7.sh
 # @Description: centos7产品库制作脚本
### 
set -x
set -e

CURRENT_DIR=$(cd $(dirname $0); pwd)
# 临时文件下载目录
TEMP_DOWNLOAD_DIR="/opt/downloads"
# ldap server 配置使用到的文件
BASE_DN_FILE="$CURRENT_DIR/conf/basedn.ldif"
OU_DN_FILE="$CURRENT_DIR/conf/ou.ldif"
USER_DN_FILE="$CURRENT_DIR/conf/user_group.ldif"
SLAPD_CONF_FILE="$CURRENT_DIR/conf/slapd.ldif"
SLAPD_SEVICE_FILE="$CURRENT_DIR/service/slapd.service"
# 软件安装路径
CYRUS_SASL_INSTALL_DIR="/opt/cyrus-sasl/2.1.28"
LDAP_INSTALL_DIR="/opt/ldap/2.5.13"
# nscld文件
NSLCD_CONF_FILE="/etc/nslcd.conf"
# nsswitch
NSSWITCH_CONF_FILE="/etc/nsswitch.conf"
# 判断当前操作系统为centos或ubuntu
function check_env() {
    if [ -f "/etc/redhat-release" ]
    then
        echo "1"
    elif [ -f "/etc/lsb-release" ]
    then
        echo "2"
    else
        echo "0"
    fi
} 
# 安装ldap server
function install_server() {
    ret_code=`check_env`
    if [ $ret_code == 1 ]
    then
        # centos7 安装
        yum install gcc libtool-ltdl-devel -y
    elif [ $ret_code == 2 ]
    then
        # ubuntu20.04 没有依赖需要安装
        echo "ubuntu"
    else 
        echo "unexpected os,only support centos or ubuntu,exit!!!"
        return 1
    fi 
    if [ ! -d $TEMP_DOWNLOAD_DIR ]
    then
        mkdir -p /opt/downloads
    fi
    cd $TEMP_DOWNLOAD_DIR
    # cyrus-sasl-2.1.28安装
    wget  https://chpc-dev.bj.bcebos.com/image-build/cyrus-sasl-2.1.28.tar.gz
    tar -zxvf cyrus-sasl-2.1.28.tar.gz
    cd $TEMP_DOWNLOAD_DIR/cyrus-sasl-2.1.28
    mkdir -p $CYRUS_SASL_INSTALL_DIR
    ./configure --prefix=$CYRUS_SASL_INSTALL_DIR
    make -j
    make install
    
    cd $TEMP_DOWNLOAD_DIR
    # open ldap安装
    wget  https://chpc-dev.bj.bcebos.com/image-build/openldap-2.5.13.tgz
    gunzip -c openldap-2.5.13.tgz | tar xvfB -
    mkdir -p $LDAP_INSTALL_DIR
    cd  $TEMP_DOWNLOAD_DIR/openldap-2.5.13
    ./configure --prefix=$LDAP_INSTALL_DIR --enable-backends --enable-local --enable-slapd  --enable-wt=no --enable-spasswd --enable-modules --with-tls=no CPPFLAGS="-I$CYRUS_SASL_INSTALL_DIR/include/sasl/" LDFLAGS="-L$CYRUS_SASL_INSTALL_DIR/lib/ -L$CYRUS_SASL_INSTALL_DIR/lib/sasl2/"
    make depend
    make -j
    # make test
    make install
    # 环境变量修改
    echo "PATH=$LDAP_INSTALL_DIR/bin:$LDAP_INSTALL_DIR/sbin:$LDAP_INSTALL_DIR/libexec:\$PATH" >> /etc/profile
    source /etc/profile
    # 生成open ldap server启动配置项
    mkdir -p $LDAP_INSTALL_DIR/etc/slapd.d
    mkdir -p $LDAP_INSTALL_DIR/var/openldap-data
    rm $LDAP_INSTALL_DIR/etc/openldap/slapd.ldif
    cp $SLAPD_CONF_FILE $LDAP_INSTALL_DIR/etc/openldap/
    # 生成LDAP配置
    $LDAP_INSTALL_DIR/sbin/slapadd -n 0 -F $LDAP_INSTALL_DIR/etc/slapd.d -l $LDAP_INSTALL_DIR/etc/openldap/slapd.ldif
    # 生成开机自启动项
    cp $SLAPD_SEVICE_FILE /usr/lib/systemd/system/
    # 导入open ldap basedn.ldif,注意basedn文件存放目录,如有变动,需手动修改。需启动ldap server。
    systemctl enable slapd
    systemctl start slapd
    sleep 10s
    $LDAP_INSTALL_DIR/bin/ldapadd -x -D "cn=Manager,dc=baiduhpc,dc=com" -w 9:V@_IMevA7S -f $BASE_DN_FILE
    # 导入work用户组,未初始化密码。
    $LDAP_INSTALL_DIR/bin/ldapadd -x -D "cn=Manager,dc=baiduhpc,dc=com" -w 9:V@_IMevA7S -f $OU_DN_FILE
    $LDAP_INSTALL_DIR/bin/ldapadd -x -D "cn=Manager,dc=baiduhpc,dc=com" -w 9:V@_IMevA7S -f $USER_DN_FILE
    user="uid=work,ou=people,dc=baiduhpc,dc=com"
    $LDAP_INSTALL_DIR/bin/ldappasswd -x -D "cn=Manager,dc=baiduhpc,dc=com" -w 9:V@_IMevA7S -s 2X9@7usvAef $user
    # kill slapd进程
    systemctl stop slapd
    systemctl disable slapd
    # ldap server默认日志配置
    echo "loglevel        4095" >> $LDAP_INSTALL_DIR/etc/openldap/ldap.conf
    echo "local4.*                                                /var/log/ldap.log" >> /etc/rsyslog.conf
    service rsyslog restart
    return 0
}
# 安装ldap client
function install_client() {
    ret_code=`check_env`
    if [ $ret_code == 1 ]
    then
        # centos7 安装ldap-client
        yum -y install openldap-clients nss-pam-ldapd
        # nc 工具安装
        yum -y install nc
    elif [ $ret_code == 2 ]
    then
        apt-get update
        # ubuntu 安装ldap-client
        DEBIAN_FRONTEND=noninteractive apt -yq install libnss-ldapd libpam-ldapd ldap-utils
        # 支持第一次登录创建home dir
        pam-auth-update --enable mkhomedir
    else
        echo "unexpected os,only support centos or ubuntu,exit!!!"
        return 1
    fi
    # 删除/etc/nslcd.conf中uri行,该行信息会在节点启动时根据master节点ip追加写入
    sed -i '/uri/d' $NSLCD_CONF_FILE
    sed -i '/base/d' $NSLCD_CONF_FILE
    echo "base dc=baiduhpc,dc=com" >> $NSLCD_CONF_FILE

    # 修改/etc/nsswitch.conf 支持通过ldap进行ssh登录验证
    sed -i '/^passwd:/{N;s/\n/ ldap\n/}' $NSSWITCH_CONF_FILE
    sed -i '/^group:/{N;s/\n/ ldap\n/}' $NSSWITCH_CONF_FILE
    sed -i '/^shadow:/{N;s/\n/ ldap\n/}' $NSSWITCH_CONF_FILE
    sed -i '/^gshadow:/{N;s/\n/ ldap\n/}' $NSSWITCH_CONF_FILE

    # 关闭开机自启动项和停止nscd、nslcd进程
    systemctl stop nscd
    systemctl stop nslcd
    systemctl disable nslcd
    systemctl disable nscd
}

install_server
install_client
# 清理安装残留文件
# 修改open ldap server基础配置部分，当前使用的为已修改过的版本,如需修改,请手动执行
# vi$LDAP_INSTALL_DIR/etc/openldap/slapd.ldif
# 修改 olcRootDN=Manager,dc=baiduhpc,dc=com
# 修改 olcRootPW=xxxx(生成Baidu@123的密码,slappasswd)
# 修改 olcDbDirectory=/opt/ldap/2.5.13/openldap-data

