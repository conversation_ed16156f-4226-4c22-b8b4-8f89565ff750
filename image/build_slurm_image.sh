#! /bin/bash
###
 # @Author: sunkaixuan01 <EMAIL>
 # @Date: 2022-12-15 11:54:36
 # @LastEditors: sunkaixuan01 <EMAIL>
 # @LastEditTime: 2023-01-18 20:43:28
 # @FilePath: \slurm_conf\image\centos7.9-slurm-image.sh
 # @Description: slurm镜像一键制作
### 

set -e
# 脚本当前所在目录
INSTALL_CLEAN_FILE="install_clean.sh"
CURRENT_DIR=$(cd $(dirname $0); pwd)
TEMP_DOWNLOAD_DIR="/opt/downloads"
# hpc软件安装
function install() {
    if [ ! -d $TEMP_DOWNLOAD_DIR ]
    then
        mkdir -p $TEMP_DOWNLOAD_DIR
    fi
    cd $CURRENT_DIR/../slurm
    bash build_slurm.sh
    cd $CURRENT_DIR/../ldap
    bash build_ldap.sh
}

bash $CURRENT_DIR/install_clean.sh before-install
install
bash $CURRENT_DIR/install_clean.sh after-install slurm
