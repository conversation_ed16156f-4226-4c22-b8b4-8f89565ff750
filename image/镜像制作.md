<!--
 * @Author: sunkaixuan01 <EMAIL>
 * @Date: 2022-12-20 21:03:47
 * @LastEditors: sunkaixuan01 <EMAIL>
 * @LastEditTime: 2022-12-31 22:34:29
 * @FilePath: \chpc_image_builder\image\镜像制作.md
 * @Description: 镜像制作流程
-->
# 镜像制作
## 环境配置和脚本使用
    环境配置：安装qemu、kvm环境，系统需支持虚拟化。
    启动命令：qemu-system-x86_64 -nographic xxx.qcow2 -enable-kvm -m 8192
    镜像制作：虚拟内mkdir -p /opt/downloads,下载代码库并打包,到该目录解压执行即可，制作完完成后shutdown或者poweroff关机。
    示例：
        虚拟内
        mkdir -p /opt/downloads
        tar -zxvf 代码库.tar.gz
        cd image && bash build_sge_image.sh
        shutdown