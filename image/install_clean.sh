#! /bin/bash
###
 # @Author: sunkaixuan01 <EMAIL>
 # @Date: 2022-12-21 19:48:34
 # @LastEditors: sunkaixuan01 <EMAIL>
 # @LastEditTime: 2023-01-31 17:26:24
 # @FilePath: \chpc_image_builder\image\install_clean.sh
 # @Description:前置和后置操作
### 

set -x
set -e

CURRENT_DIR=$(cd $(dirname $0); pwd)

param=$1
SCHEDULER_TYPE=$2
# 判断当前操作系统为centos或ubuntu
function check_env() {
    if [ -f "/etc/redhat-release" ]
    then
        echo "1"
    elif [ -f "/etc/lsb-release" ]
    then
        echo "2"
    else
        echo "0"
    fi
} 
# 安装前置工作
function before_install() {
    # stop 
    systemctl stop bcm-agent
    systemctl stop hosteye 
    service bcm-agent stop
    # 用qcow2制作时需要启用，线上不用。
    # hostnamectl set-hostname hpc-image
    # cenots7换源
    # cd /etc/yum.repos.d/
    # wget http://mirrors.aliyun.com/repo/Centos-7.repo
    # wget http://mirrors.aliyun.com/repo/epel-7.repo
    # mv CentOS-Base.repo CentOS-Base.repo.bak
    # mv epel.repo epel.repo.bak
    # mv Centos-7.repo CentOS-Base.repo
    # mv epel-7.repo epel.repo
    # yum clean all
    # yum makecache
    install_nfs
}
# 安装后置工作
function after_install() {
    systemctl stop nscd
    systemctl stop nslcd
    systemctl disable nscd
    systemctl disable nslcd

    SCHEDULER_TYPE=$1

     # hosteye日志清理
    cd /opt/hosteye/logs
    rm -rf *.log
    # 日志清理
    cd /var/log
    rm -f boot.log  cloud-init.log  cloud-init-output.log
    # bce-agent日志清理
    cd /opt/bcm-agent/var/lib/bcm-agent
    rm -rf daemon.*
    rm -rf /opt/downloads
    # 文件拷贝
    mkdir -p /opt/chpc/cluster-api/conf
    mkdir -p /opt/chpc/cluster-api/template

#    cp $CURRENT_DIR/../cluster_control /opt/chpc
    cd /opt/chpc
    rm -f /opt/chpc/cluster_control
    wget https://chpc-dev.bj.bcebos.com/image-build/cluster_control

    cp $CURRENT_DIR/../cluster_control.sh /opt/chpc
    cp $CURRENT_DIR/../api-server/conf/*.yaml  /opt/chpc/cluster-api/conf
#    cp $CURRENT_DIR/../api-server/service/*.template  /opt/chpc/cluster-api/template
    # 先复制到 opt 目录，后续由 cluster_control.sh 在启动 chpc server 前，根据集群信息调整参数并复制到系统目录
    if [ $SCHEDULER_TYPE == "slurm" ]; then
      cp $CURRENT_DIR/../api-server/service/chpc-server.service.slurm.template /opt/chpc/cluster-api/template/chpc-server.service.template
    elif [ $SCHEDULER_TYPE == "sge" ]; then
      cp $CURRENT_DIR/../api-server/service/chpc-server.service.sge.template /opt/chpc/cluster-api/template/chpc-server.service.template
    else
      echo "unrecognized scheduler type!"
      return 1
    fi
#    cp $CURRENT_DIR/../api-server/cluster-api /opt/chpc/cluster-api/
    cd /opt/chpc/cluster-api/
    rm -f /opt/chpc/cluster-api/cluster-api
    wget https://chpc-dev.bj.bcebos.com/image-build/cluster-api

    if [ -f "/opt/chpc/cluster_control" ]; then
        chmod +x /opt/chpc/cluster_control
    else
        echo -e "\033[31m build image environment failed, /opt/chpc/cluster_control not exist \033[0m"
        return 1
    fi


    chmod +x /opt/chpc/cluster_control.sh
    if [ -f "/opt/chpc/cluster-api/cluster-api" ]; then
        chmod +x /opt/chpc/cluster-api/cluster-api
    else
        echo -e "\033[31m build image environment failed, /opt/chpc/cluster-api/cluster-api not exist \033[0m"
        return 1
    fi

    touch /root/.bash_history_tmp
    mv /root/.bash_history_tmp /root/.bash_history
}
# nfs 软件安装
function install_nfs() {
    ret_code=`check_env`
    if [ $ret_code == 1 ]
    then
        # centos7 安装nfs
        yum -y install nfs-utils
        systemctl stop rpcbind
        systemctl stop nfs
        systemctl disable nfs
        systemctl disable rpcbind
    elif [ $ret_code == 2 ]
    then
        # ubuntu 安装nfs
        apt update && apt install -y nfs-common nfs-kernel-server
        systemctl stop rpcbind
        systemctl stop nfs-server
        systemctl disable rpcbind
        systemctl disable nfs-server
    else
        echo "unexpected os,only support centos or ubuntu,exit!!!"
        return 1
    fi
}
# 外部调用逻辑
function execute() {
    if [ x$param == x"before-install" ]
    then
        before_install
        ret_code=$?
        if [ $ret_code != 0 ]
        then
            echo "execute before install failed"
        fi
    elif [ x$param == x"after-install" ]
    then    
        after_install $SCHEDULER_TYPE
        ret_code=$?
        if [ $ret_code != 0 ]
        then
            echo "execute after install failed"
        fi 
    fi

}

execute