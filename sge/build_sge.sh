#! /bin/bash
###
 # @Author: sunkaixuan01 <EMAIL>
 # @Date: 2022-12-19 21:06:24
 # @LastEditors: sunkaixuan01 <EMAIL>
 # @LastEditTime: 2022-12-31 22:06:28
 # @FilePath: \chpc_image_builder\slurm\build_sge.sh
 # @Description: sge产品库制作
### 
set -e
set -x

CURRENT_DIR=$(cd $(dirname $0); pwd)
# 临时文件下载目录
TEMP_DOWNLOAD_DIR="/opt/downloads"
# sge配置文件
MC_TEMPLATE_FILE="$CURRENT_DIR/conf/mc_template"
MSCONF_TEMPLATE_FILE="$CURRENT_DIR/conf/msconf_template"
ME_GLOBAL_TEMPLATE_FILE="$CURRENT_DIR/conf/me_global_template"
QUEUE_TEMPLATE_FILE="$CURRENT_DIR/conf/queue_template"
DEFAULT_QUEUE_TEMPLATE_FILE="$CURRENT_DIR/conf/default_queue_template"
# sge安装目录
SGE_INSTALL_DIR="/opt/sge"
# 判断当前操作系统为centos或ubuntu
function check_env() {
    if [ -f "/etc/redhat-release" ]
    then
        echo "1"
    elif [ -f "/etc/lsb-release" ]
    then
        echo "2"
    else
        echo "0"
    fi
} 
# sge 安装
function install_sge() {
    ret_code=`check_env`
    if [ $ret_code == 1 ]
    then  
        yum -y groupinstall 'Development Tools'
        yum -y install git openssl-devel libtirpc-devel motif-devel ncurses-devel libdb-devel pam-devel systemd-devel wget cmake
    elif [ $ret_code == 2 ]
    then
        apt update && apt -y install build-essential cmake git libdb5.3-dev libmotif-dev libncurses-dev libpam0g-dev libssl-dev libsystemd-dev libtirpc-dev libxext-dev pkgconf
    else
        echo "unexpected os,only support centos or ubuntu,exit!!!"
        return 1
    fi
    mkdir -p $SGE_INSTALL_DIR
    if [ ! -d $TEMP_DOWNLOAD_DIR ]
    then
        mkdir -p $TEMP_DOWNLOAD_DIR
    fi
    cd $TEMP_DOWNLOAD_DIR
    wget https://chpc-dev.bj.bcebos.com/image-build/sge.tar.gz
    wget https://chpc-dev.bj.bcebos.com/image-build/cmake-3.26.4-linux-x86_64.tar.gz
    wget https://chpc-dev.bj.bcebos.com/image-build/hwloc-hwloc-2.8.0.tar.gz
    tar -zxvf sge.tar.gz
    tar -zxvf cmake-3.26.4-linux-x86_64.tar.gz
    tar -xf hwloc-hwloc-2.8.0.tar.gz
    path_bak=$PATH
    export PATH=$TEMP_DOWNLOAD_DIR/cmake-3.26.4-linux-x86_64/bin:$PATH
    # apt 自动安装的hwloc（2.1.0版本，2.7.0版本），在bcc.gn5.c2m8 规格的实例上会导致libhwloc.so产生FPE错误，无法启动sgeexecd
    # 编译安装hwloc-2.8.0可以解决这个问题，具体问题原因是hwloc的版本还是apt导致的？需要进一步排查
    cd $TEMP_DOWNLOAD_DIR/hwloc-hwloc-2.8.0
    ./autogen.sh
    ./configure
    make
    make install

    cd $TEMP_DOWNLOAD_DIR/sge
    if [ $ret_code == 2 ]
    then
        git config --global --add safe.directory /opt/downloads/sge
    fi
    cmake -S . -B build -DCMAKE_INSTALL_PREFIX=$SGE_INSTALL_DIR -DSYSTEMD=OFF
    cmake --build build -j
    cmake --install build
    # sgemaster和sgeexecd安装
    cd $SGE_INSTALL_DIR/
    ./inst_sge -m -auto $SGE_INSTALL_DIR/util/install_modules/inst_template.conf
    # 默认选项从y修改为n
    sed -i '537 s/def\ \"y\"/def\ \"n\"/' $SGE_INSTALL_DIR/util/install_modules/inst_execd.sh 
    ./inst_sge -x -auto $SGE_INSTALL_DIR/util/install_modules/inst_template.conf
    # 拷贝配置文件
    mkdir -p $SGE_INSTALL_DIR/templates
    cp $MC_TEMPLATE_FILE $SGE_INSTALL_DIR/templates/
    cp $MSCONF_TEMPLATE_FILE $SGE_INSTALL_DIR/templates/
    cp $QUEUE_TEMPLATE_FILE $SGE_INSTALL_DIR/templates/
    cp $DEFAULT_QUEUE_TEMPLATE_FILE $SGE_INSTALL_DIR/templates/
    cp $ME_GLOBAL_TEMPLATE_FILE $SGE_INSTALL_DIR/templates/
    # 设置开机自启动项
    cp $SGE_INSTALL_DIR/util/rctemplates/systemd/sgemaster.service /usr/lib/systemd/system/
    cp $SGE_INSTALL_DIR/util/rctemplates/systemd/sgeexecd.service /usr/lib/systemd/system/
    PATH=$path_bak
    echo "PATH=$SGE_INSTALL_DIR/bin/lx-amd64:\$PATH" >> /etc/profile
    echo "source $SGE_INSTALL_DIR/default/common/settings.sh" >> /etc/profile
    source $SGE_INSTALL_DIR/default/common/settings.sh
    source /etc/profile
    # 删除默认的all.q队列
    qconf -dq all.q
    # 关闭开机自启动并停止sge当前进程
    systemctl stop sgeexecd
    systemctl stop sgemaster
    systemctl disable sgeexecd
    systemctl disable sgemaster
    # 安装文件清理
    cat /dev/null >$SGE_INSTALL_DIR/default/common/act_qmaster
    rm -rf $SGE_INSTALL_DIR/default/common/install_logs
    host_name=`hostname`
    if [ x"$host_name" != x"" ]
    then 
        rm -rf $SGE_INSTALL_DIR/default/spool/$host_name
        rm -rf $SGE_INSTALL_DIR/default/spool/qmaster/exec_hosts/$host_name
    fi
    cat /dev/null >$SGE_INSTALL_DIR/default/spool/qmaster/qmaster.pid
}
install_sge