/*
 * @Author: sunkaixuan01 <EMAIL>
 * @Date: 2022-11-30 15:25:11
 * @LastEditors: sunkaixuan01 <EMAIL>
 * @LastEditTime: 2023-02-09 16:22:47
 * @FilePath: \slurm_conf\sge\sge.go
 * @Description: sge节点启动
 */
package sge

import (
	"errors"
	"fmt"

	"github.com/astaxie/beego/logs"

	"baidu/hpc/chpc_image_builder/ldap"
	"baidu/hpc/chpc_image_builder/util"
)

// 启动slurm节点,根据节点类型执行对应的启动命令
func StartSgeNode(clusterParam util.ConsleParam) (err error) {
	args := []string{util.ShellSciptName, "startNfs"}
	err = util.ExecCmd(args...)
	if err != nil {
		logs.Error(err.Error())
		return
	}
	err = util.MountCfs(clusterParam)
	if err != nil {
		logs.Error(err.Error())
		return
	}
	if clusterParam.NodeType == util.MasterNode {
		// 主节点启动函数
		err = startSgeMasterNode(clusterParam)
		if err != nil {
			logs.Error(err.Error())
			return
		}
		return
	} else if clusterParam.NodeType == util.ComputeNode {
		// 计算节点启动函数
		err = startSgeComputeNode(clusterParam)
		if err != nil {
			logs.Error(err.Error())
			return
		}
		return
	} else if clusterParam.NodeType == util.BackUpNode {
		// 高可用备节点启动函数
		err = startSgeBackupNode(clusterParam)
		if err != nil {
			logs.Error(err.Error())
			return
		}
		return
	} else if clusterParam.NodeType == util.LoginNode {
		// 登录节点启动函数
		err = startSgeLoginNode(clusterParam)
		if err != nil {
			logs.Error(err.Error())
			return
		}
		return
	} else {
		err = fmt.Errorf("invalid node type %v", clusterParam.NodeType)
		logs.Error(err.Error())
		return
	}
}

// 启动sge主节点
func startSgeMasterNode(clusterParam util.ConsleParam) (err error) {
	err = ldap.StartLdapServer()
	if err != nil {
		logs.Error(err.Error())
		return
	}
	err = ldap.StartLdapClient(clusterParam)
	if err != nil {
		logs.Error(err.Error())
		return
	}
	if clusterParam.EnableHa {
		// TO DO,sge高可用
		return
	}
	// 启动sge主节点
	args := []string{util.ShellSciptName, "startSgeMasterNoHA", "-c", clusterParam.ClusterName, "--masterHost", clusterParam.MasterHostName}
	if clusterParam.LoginHostname != "" {
		args = append(args, "--loginHost="+clusterParam.LoginHostname)
	}
	err = util.ExecCmd(args...)
	if err != nil {
		logs.Error("Start sge master node,error info is %s", err.Error())
		return
	}
	defaultPartitionList := make([]string, 0)
	otherPartitionList := make([]string, 0)
	for _, par := range clusterParam.QueueList {
		if par.IsDefault {
			defaultPartitionList = append(defaultPartitionList, par.Name)
		} else {
			otherPartitionList = append(otherPartitionList, par.Name)
		}
	}
	defaultPartitionStr := util.Array2String(defaultPartitionList...)
	otherPartitionListStr := util.Array2String(otherPartitionList...)
	// 添加非默认队列信息
	if len(otherPartitionList) > 0 {
		args := []string{util.ShellSciptName, "createSgeQueues", "-p", otherPartitionListStr}
		err = util.ExecCmd(args...)
		if err != nil {
			logs.Error("add other queue:%v fail,error info is %s", otherPartitionListStr, err.Error())
			return
		}
	}
	// 添加默认队列信息
	if len(defaultPartitionList) > 1 {
		err = errors.New("only allow one default queue,please change cluster conf")
		logs.Error(err.Error())
		return
	}
	if len(defaultPartitionList) > 0 {
		args := []string{util.ShellSciptName, "createSgeDefaultQueue", "-p", defaultPartitionStr}
		err = util.ExecCmd(args...)
		if err != nil {
			logs.Error("add default queue:%v fail,error info is %s", defaultPartitionStr, err.Error())
			return
		}
	}
	// 添加sge计算节点到sge队列中
	for _, par := range clusterParam.QueueList {
		hostnameArr := make([]string, 0)
		for _, node := range par.Node {
			hostnameArr = append(hostnameArr, node.HostName)
		}
		if len(hostnameArr) == 0 {
			continue
		}
		hostnameStr := util.Array2String(hostnameArr...)
		args = []string{util.ShellSciptName, "addSgeComputeToQueue", "-p", par.Name, "--computeHosts", hostnameStr}
		err = util.ExecCmd(args...)
		if err != nil {
			logs.Error("add host to queue fail,error info is %s", err.Error())
			return err
		}
	}

	region := clusterParam.Region
	if region == "" {
		region = "default"
	}

	args = []string{util.ShellSciptName, "startChpcServer", "--region", region}
	err = util.ExecCmd(args...)
	if err != nil {
		logs.Error("start chpc server fail,error info is %s", err.Error())
		return
	}
	return
}

// 启动sge计算节点
func startSgeComputeNode(clusterParam util.ConsleParam) (err error) {
	err = ldap.StartLdapClient(clusterParam)
	if err != nil {
		logs.Error(err.Error())
		return
	}
	if clusterParam.EnableHa {
		// TO DO,高可用计算节点启动
		return
	}
	// sge master节点注册计算节点时默认设置为不可调度(否则在节点真正启动的这段时间可能会发生调度错误)
	// 所以sge计算节点启动后需要自己将节点设置为可调度，此时需要传入队列名
	// copy from slurm
	var queueName string
	// 获取计算节点归属的队列
	for _, par := range clusterParam.QueueList {
		for _, node := range par.Node {
			result, err := util.CheckIP(node.IP)
			if err != nil {
				logs.Error("Get local ip error,%v", err.Error())
				return err
			}
			if result {
				queueName = par.Name
				break
			}
		}
	}
	if queueName == "" {
		err = fmt.Errorf("slurmqueue_name is empty,please check cluster param:%+v", clusterParam)
		logs.Error(err.Error())
		return
	}
	// 启动sge计算节点
	args := []string{util.ShellSciptName, "startSgeExecdNoHA",
		"--masterHost", clusterParam.MasterHostName,
		"-p", queueName}
	err = util.ExecCmd(args...)
	if err != nil {
		logs.Error("start sge compute node fail,error info is %s", err.Error())
		return
	}
	return
}

// TO DO,备节点
func startSgeBackupNode(clusterParam util.ConsleParam) (err error) {
	args := []string{"1.sh", "StartSlurmSlave"}
	err = util.ExecCmd(args...)
	if err != nil {
		logs.Error("start sge backup fail,error info is %s", err.Error())
		return
	}
	return
}

// 登录节点
func startSgeLoginNode(clusterParam util.ConsleParam) (err error) {
	args := []string{util.ShellSciptName, "startSgeLoginHost", "--masterHost", clusterParam.MasterHostName}
	err = util.ExecCmd(args...)
	if err != nil {
		logs.Error("start sge login node fail,error info is %s", err.Error())
		return
	}
	return
}
