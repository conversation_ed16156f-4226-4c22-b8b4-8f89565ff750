app:
  # 服务名
  name: cluster-api
  # 服务版本
  version: v1
  # 环境：测试/沙盒/线上
  env: dev
  # 临时集群信息文件，建议不要修改
  clusterInfoFileName: /opt/chpc/cluster-api/conf/.sge-cluster.json
  # 服务配置
  server:
    # 服务IP
    host: 127.0.0.1
    # 端口
    port: 8100
  # 日志配置
  log:
    # 日志目录
    path: /opt/chpc/cluster-api/logs
    # 日志等级
    level: 7
    # 清理周期（天）
    clean: 7
    # 是否开启logroate
    rotate: true
    # 日志分割
    separate: info,debug,error
    # 日志输出层级
    funcdepth: 3
  # 时间配置
  time:
    # 异步任务执行周期
    executionCycle: 10
    # 动态扩缩容采集周期
    collectionCycle: 10
    # 动态扩容执行周期
    autoExpandCycle: 60
    # 动态缩容执行周期
    autoShrinkCycle: 60
cloud:
  # iam鉴权的服务地址
  iamEndPoint: http://iam.bj.internal-qasandbox.baidu-int.com
  bceVersion: /v3
  domain: Default
  auth:
    # iam: 平台自身鉴权
    iamUserName: chpc
    iamPassWord: CDyftmfwBhm8cwJSt2aW9nYOLF7nju96
    iamService: bce:chpc
  metric:
    enable: true
    endpoint: http://bcm.{region}.baidubce.com
    bcmScope: BCE_CHPC
    userId: {userId}
    ak: 066edfb6f99a2f853fa16e1433035637f14c8e9b11ef09e823de9182f8e7040aa4c8459200c4dda0d26eb89c8d64bb039e9a2b733a0be5dfda4b021338e8f36e93c04b9b253fbd9c2b758a36272e00c1bb90bcd0a8a6db810f07131cb7d4118ea7dbb7f76eec6bdca18d79e950557d71fe3b4bd3ee7741d944df26814726d54c7a64043ed344825b7b05b498dca7373d5f232fe54d1f1ef69af795d5d147714e8a1833193ce10682f9c5be7dd6524d6bf02bf06af2daeee7e5044bcc25004d17a82887c584f90995888bd9b26f02424a0562d9ef72df3d92d81a2914df1e12085aa7aa65411c9d7b6dc5a620678934744c25f6b3013ae2a64b848bae592d9659
    sk: 1183f00453efcff74d1c8ad6e4642219a6fdc7d3b944e3ce621aa81e6a164011529b591c98e31ce108f079585df9d44f3252aead6560ef6bae4e47bfebedb8dc8a67e7cc3f9b7206417e7dec90a8bdab96493d4f07937c3fbdbf5381c1305e22788750c4bea000887f33f2a9c090c5a33c912f4246f1539dd3ae41b3d133823a8ede7230b46c66980917e7b8fec1f986baa6866008434070afe85cb16cb2645bd5c5b8b6383aec5801df74826980908e4442723b2f47441bc65d4e79c62a7f6d248cbf62667a02d89035f515e25998b4cc5f60862aeeec1376ea598bae3ee98e40432fa92dde9ad9560851b060ac57ab2a40cbfd759c1aa32573939ab88d4853
    pushGap: 25
sge:
  sgeSettingFileName: /opt/sge/default/common/settings.sh
  sgeControllerShellFileName: /opt/chpc/cluster_control.sh
  sgeRootDir: /opt/sge
  sgeBinDir: /opt/sge/bin/lx-amd64
schedulerPlugin:
  schedulerPluginConfPath: /plugin/chpc/scheduler_plugin.conf
