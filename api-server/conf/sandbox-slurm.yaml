app:
  # 服务名
  name: cluster-api
  # 服务版本
  version: v1
  # 环境：测试/沙盒/线上
  env: dev
  # 临时集群信息文件，建议不要修改
  clusterInfoFileName: /opt/chpc/cluster-api/conf/.slurm-cluster.json
  # 服务配置
  server:
    # 服务IP
    host: 127.0.0.1
    # 端口
    port: 8100
  # 日志配置
  log:
    # 日志目录
    path: /opt/chpc/cluster-api/logs
    # 日志等级
    level: 7
    # 清理周期（天）
    clean: 7
    # 是否开启logroate
    rotate: true
    # 日志分割
    separate: info,debug,error
    # 日志输出层级
    funcdepth: 3
  # 时间配置
  time:
    # 异步任务执行周期
    executionCycle: 10
    # 动态扩缩容采集周期
    collectionCycle: 10
    # 动态扩容执行周期
    autoExpandCycle: 60
    # 动态缩容执行周期
    autoShrinkCycle: 60
    # 心跳请求周期
    heartbeatCycle: 60
    # 监控数据采集周期
    metricsCycle: 60
cloud:
  # iam鉴权的服务地址
  iamEndPoint: http://iam.bj.internal-qasandbox.baidu-int.com
  bceVersion: /v3
  domain: Default
  auth:
    # iam: 平台自身鉴权
    iamUserName: chpc
    iamPassWord: CDyftmfwBhm8cwJSt2aW9nYOLF7nju96
    iamService: bce:chpc
  metric:
    enable: true
    endpoint: http://100.67.179.47:8869
    bcmScope: BCE_CHPC
    userId: {userId}
    ak: 9997f4e9729d72de0033ac9f93f14f99bda140890f6d58466c7c14a2e54c080f052c4444441a4a5391a840e4edfb91b9582579277e9800094e0a1e1ad729e27ecabf5e7b566e2dba47f6ed5a4ffd4d8d1f5f0c1b14d41c249cd37a1ac8c53ea495737a0b5323aa650b295a623ce3e5e44d474bfecf3c23349cc44ac39370c69241f39237edc19564c2be5feb44bd4f65ddac038febda95eb2d500ad9d52cbe07d9358d806654880cc89bfdb9421996490168d374e1f747bdf03d7c90c4132345ed37ce5eca2368fafb649d9a2415ff3ac5d7e43ca781c37b5109f303ef29181ccd663cf1bdf465140cc6fb4642ebe2513639e7389b92d399c78fc6902cdcdbb6
    sk: 0775a7565388b086030c7c6f0bddd2b7480cb1cd9051ef7b8fe712cd5c5b873d43c85e7493d1f90fb5f73818c031676d6fe3240b9dfe31410785464f9937d614eb84a586a8606e12dc4f55373cbccd43798763094d3b7e1fe53fd331aae5f1e4f68bd59e053578da3bade20ea576e93a8f24f65ad60e9fd130656c3ad18c1901d8ad332ed95a891459b7b9134d5def1a070e63ba333a627d9efbb69e1def5aa0d54cbcf79e0a5b8fe4efe428598e982365730e466c319036e57457061f32aa0a0c8742e16eb718523438c65b5169be97e6120ebea24fda9673a924f47d2069343bcd86771d6c1fe1fbe2bc80a5ea5efdf8ca137d4e5c5e77fe199c7419c74401
    pushGap: 25
slurm:
  slurmConfFile: /opt/slurm/22.05.9/etc/slurm.conf
  slurmRootDir: /opt/slurm/22.05.9
  slurmBinDir: /opt/slurm/22.05.9/bin
  slurmAPI:
    # slurmrestd服务IP
    host: 127.0.0.1
    # 服务端口
    port: 6820
    # 版本
    version: v0.0.38
    # 用户
    user: slurmapi
    # token文件地址
    tokenFile: /opt/chpc/cluster-api/conf/slurmapi.token
schedulerPlugin:
  schedulerPluginConfPath: /plugin/chpc/scheduler_plugin.conf
