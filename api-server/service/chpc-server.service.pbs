[Unit]
# Description=chpc api-server demon service

[Service]
Restart=always
RestartSec=5
Type=simple
ExecStart=/bin/bash -c '. "$0" && exec "$@"' /opt/pbs/etc/pbs.sh /opt/chpc/cluster-api/cluster-api -conf /opt/chpc/cluster-api/conf/online.yaml -clusterConf /opt/chpc/cluster.conf -region {region} > /dev/null 2>&1
KillSignal=SIGTERM
TimeoutStopSec=5
KillMode=process
[Install]
WantedBy=multi-user.target
