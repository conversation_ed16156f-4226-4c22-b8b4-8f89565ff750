#!/bin/bash

REGION=""
ARCH=""

function parse_args() {
    args=$(/opt/homebrew/opt/gnu-getopt/bin/getopt -o h: -l region:,arch: -- "$@")
    if [ $? != 0 ]; then
        echo "Parse error! Terminating..." >&2
        exit 1
    fi
    echo $args
    echo ""
    eval set -- "$args"
    while true; do
        case "$1" in
        --region)
            REGION="$2"
            shift 2
            ;;
        --arch)
            ARCH="$2"
            shift 2
            ;;
        -h)
            help
            exit 0
            ;;
        --)
            shift
            break
            ;;
        *)
            echo "Parameters error!!!$1"
            exit 1
            ;;
        esac
    done
    exec_func=$1
}

function main() {
    file_path="pkg/version.list"

    echo "exec_func: $1"

    if [ x"$1" == x"buildPkg" ]; then
        mkdir -p build
        rm -rf build/*
        mkdir -p build/output

        while IFS= read -r line; do
            # 获取软件列表
            IFS=':' read -ra parts <<<"$line"
            software="${parts[0]}"
            version="${parts[1]}"
            echo $software
            echo $version
            targetFile=${software}_${version}_${ARCH}
            targetDir=build/${software}/${version}/${targetFile}
            mkdir -p $targetDir

            # 复制软件脚本到目标目录
            cp -rf pkg/${software}/${version}/* $targetDir
            cp -rf pkg/common/* $targetDir

            # 制作软件介质
            pushd $targetDir
            if [[ -f build_pkg.sh ]]; then
                bash build_pkg.sh
            fi
            popd

            pushd $targetDir/..
            tar -zcvf ${targetFile}.tar.gz $targetFile
            mv ${targetFile}.tar.gz ../../output
            popd

        done <"$file_path"
    fi
}

parse_args $@
echo $ARCH
main $exec_func
