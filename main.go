/*
 * @Author: sunkaixuan01 <EMAIL>
 * @Date: 2022-12-15 21:18:14
 * @LastEditors: sunkaixuan01 <EMAIL>
 * @LastEditTime: 2023-01-17 17:13:05
 * @FilePath: \chpc_image_builder\main.go
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"os"
	"strings"

	"github.com/astaxie/beego/logs"

	"baidu/hpc/chpc_image_builder/sge"
	"baidu/hpc/chpc_image_builder/slurm"
	"baidu/hpc/chpc_image_builder/util"
)

var (
	runMode     = flag.String("m", "", "exec run mode")
	confContent = flag.String("c", "", "cluter detail content")
)

func init() {

	flag.Usage = func() {
	}
	flag.Parse()
}

func main() {
	errInfo := util.ErrResp{
		ErrCode: 0,
		ErrMsg:  "success",
	}
	errBytes, err := json.Marshal(errInfo)
	if err != nil {
		errString := `{"err_code":1,"err_msg":"marshal success info fail,need check"}`
		os.Stdout.Write([]byte(errString))
		os.Exit(1)
	}
	// 日志初始化
	err = util.InitLog()
	if err != nil {
		errInfo.ErrCode = 1
		errInfo.ErrMsg = fmt.Sprintf("init beego log failed,%s", err.Error())
		errBytes, err := json.Marshal(errInfo)
		if err != nil {
			errString := `{"err_code":1,"err_msg":"marshal log init error fail,need check"}`
			os.Stdout.Write([]byte(errString))
			os.Exit(1)
		}
		os.Stdout.Write(errBytes)
		os.Exit(1)
	}
	cleanedConfContent := strings.ReplaceAll(*confContent, `\"`, `"`)
	logs.Debug(*confContent)
	logs.Debug(cleanedConfContent)
	clusterParam := util.ConsleParam{}
	err = json.Unmarshal([]byte(cleanedConfContent), &clusterParam)
	if err != nil {
		logs.Error(err.Error())
		errString := `{"err_code":1,"err_msg":"unmarshal cluster detail error,need check"}`
		os.Stdout.Write([]byte(errString))
		os.Exit(1)
	}
	schedulerType := clusterParam.SchedulerType
	// if schedulerType == util.SLURM {
	// 	clusterParam.SchedulerType = util.SchedulerPlugin
	// }
	clusterBytes, err := json.Marshal(clusterParam)
	if err != nil {
		logs.Error(err.Error())
		errString := `{"err_code":1,"err_msg":"marshal cluster detail error,need check"}`
		os.Stdout.Write([]byte(errString))
		os.Exit(1)
	}
	// 写入集群信息到文件中
	_, err = os.Stat(util.ClusterConfFile)
	if os.IsNotExist(err) {
		f, err := os.Create(util.ClusterConfFile)
		if err != nil {
			logs.Error(err.Error())
			errString := `{"err_code":1,"err_msg":"create cluster detail file error,need check"}`
			os.Stdout.Write([]byte(errString))
			os.Exit(1)
		}
		defer f.Close()
		_, err = f.Write(clusterBytes)
		if err != nil {
			logs.Error(err.Error())
			errString := `{"err_code":1,"err_msg":"write cluster detail file error,need check"}`
			os.Stdout.Write([]byte(errString))
			os.Exit(1)
		}
	}
	// 校验参数有效性
	err = util.ValidateParms(clusterParam)
	if err != nil {
		logs.Error(err.Error())
		errString := `{"err_code":1,"err_msg":"cluster name or queue name invalid,need check"}`
		os.Stdout.Write([]byte(errString))
		os.Exit(1)
	}
	// 启动集群节点
	if *runMode == util.CreateCluster {
		if schedulerType == util.SLURM {
			err = slurm.StartSlurmNode(clusterParam)
			if err != nil {
				logs.Error(err.Error())
				errString := `{"err_code":1,"err_msg":"start slurm node error,need check"}`
				os.Stdout.Write([]byte(errString))
				os.Exit(1)
			}
			os.Stdout.Write(errBytes)
			os.Exit(0)
		} else if schedulerType == util.SGE {
			err = sge.StartSgeNode(clusterParam)
			if err != nil {
				logs.Error(err.Error())
				errString := `{"err_code":1,"err_msg":"start sge node error,need check"}`
				os.Stdout.Write([]byte(errString))
				os.Exit(1)
			}
			os.Stdout.Write(errBytes)
			os.Exit(0)
		} else {
			err = fmt.Errorf("invalid scheduler type:%v,should be sge/slurm", clusterParam.SchedulerType)
			logs.Error(err.Error())
			errString := `{"err_code":1,"err_msg":"invalid scheduler type,should be sge/slurm,need check"}`
			os.Stdout.Write([]byte(errString))
			os.Exit(1)
		}
	} else {
		err := fmt.Errorf("invalid runmode:%v,should be createCluster", runMode)
		logs.Error(err.Error())
		errString := `{"err_code":1,"err_msg":"invalid runmode type,should be createCluster,need check"}`
		os.Stdout.Write([]byte(errString))
		os.Exit(1)
	}
}
