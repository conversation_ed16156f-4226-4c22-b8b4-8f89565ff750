#! /bin/bash
set -x
set -e

if [ ! -d /usr/local/cuda ]
then
  echo "no cuda dir found"
  exit 0
fi

cuda_dir_name=$(ls /usr/local/ | grep cuda-)

if [ ! -d /usr/local/$cuda_dir_name ]
then
  echo "no cuda found"
  exit 0
fi

if [ ! -f /usr/local/$cuda_dir_name/bin/nvcc ]
then
  echo "no cuda nvcc found"
  exit 0
fi

CURRENT_DIR=$(cd $(dirname $0); pwd)

#cuda module file
if [ ! -d /etc/modulefiles/cuda ]
then
    mkdir -p /etc/modulefiles/cuda
fi
cd $CURRENT_DIR/../modulefiles
MODULE_VERSION=${cuda_dir_name:5}
bash generate_cuda.sh /usr/local/$cuda_dir_name  $MODULE_VERSION> /etc/modulefiles/cuda/$MODULE_VERSION
echo -e "#%Module\n\nset ModulesVersion $MODULE_VERSION" > /etc/modulefiles/cuda/.version