package util

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net"
	"os/exec"
	"regexp"

	"github.com/astaxie/beego/logs"
	"github.com/baidubce/bce-sdk-go/util/log"
)

// 脚本路径
const (
	ShellSciptName  = "/opt/chpc/cluster_control.sh"
	ClusterConfFile = "/opt/chpc/cluster.conf"
	ConsoleLogFile  = "/opt/chpc/console.log"
)

// go程序运行参数
const (
	CreateCluster = "createCluster" // 启动集群节点调用命令
)

// 节点类型
const (
	MasterNode  = "master"
	ComputeNode = "compute"
	BackUpNode  = "backup"
	LoginNode   = "login"
)

// 调度器类型
const (
	SGE   = "sge"
	SLURM = "slurm"

	SchedulerPlugin = "scheduler_plugin"
)

// 挂载协议
const (
	NFS = "nfs"
)

// 集群创建参数
type ConsleParam struct {
	EnableHa          bool            `json:"enable_ha"`
	SchedulerType     string          `json:"scheduler_type"`
	NodeType          string          `json:"node_type"`
	ClusterName       string          `json:"cluster_name"`
	ClusterID         string          `json:"cluster_id"`
	MasterHostName    string          `json:"master_hostname"`
	BackupHostnames   []string        `json:"backup_hostnames"`
	LoginHostname     string          `json:"login_hostname"`
	SharedstorageList []Sharedstorage `json:"sharedstorage_list"`
	QueueList         []Queue         `json:"queue_list"`
	EnableMonitor     bool            `json:"enable_monitor"`
	Region            string          `json:"region"`
	MaxNodes          int             `json:"max_nodes"`
	MaxCpus           int             `json:"max_cpus"`
}

// 共享存储参数
type Sharedstorage struct {
	StorageProtocol string `json:"storage_protocol"`
	MountTarget     string `json:"mount_target"`
	MountDir        string `json:"mount_dir"`
}

// 节点元信息
type Node struct {
	IP       string `json:"ip"`
	Spec     string `json:"spec"`
	ID       string `json:"id"`
	HostName string `json:"hostname"`
}

// 队列元信息
type Queue struct {
	IsDefault bool   `json:"is_default"`
	Name      string `json:"name"`
	Node      []Node `json:"node"`

	// 自动伸缩相关配置
	EnableAutoGrow   bool   `json:"enableAutoGrow,omitempty"`   // 是否允许自动扩容
	EnableAutoShrink bool   `json:"enableAutoShrink,omitempty"` // 是否允许自动缩容
	MaxNodes         int    `json:"maxNodes,omitempty"`         // 最大节点数
	MinNodes         int    `json:"minNodes,omitempty"`         // 最小节点数
	MaxScalePerCycle int    `json:"maxScalePerCycle,omitempty"` // 每次扩容的最大节点数
	Spec             string `json:"spec,omitempty"`             // 节点规格
}

// 错误返回码和错误返回信息
type ErrResp struct {
	ErrCode int    `json:"err_code"`
	ErrMsg  string `json:"err_msg"`
}

// 解析集群创建参数
func PraseConf(confPath *string) (clusterParam ConsleParam, err error) {
	confBytes, err := ioutil.ReadFile(*confPath)
	if err != nil {
		logs.Error("Load cluster conf fail,error info is %s", err.Error())
		return
	}
	err = json.Unmarshal(confBytes, &clusterParam)
	if err != nil {
		logs.Error("Unmarshal cluster conf fail,error info is %s", err.Error())
		return
	}
	return
}

// 执行shell命令
func ExecCmd(args ...string) (err error) {
	cmd := exec.Command("bash", args...)
	var execOut, execErr bytes.Buffer
	cmd.Stdout = &execOut
	cmd.Stderr = &execErr
	err = cmd.Run()
	if err != nil {
		logs.Error("Execute cmd:%s fail,error info is %s,err detail is %s,ouput detail is %s",
			cmd, err.Error(), execErr.String(), execOut.String())
		return
	}
	return
}

// 判断元素是否在数组中
func JudgeEleInList(ele interface{}, list ...interface{}) bool {
	for _, v := range list {
		if ele == v {
			return true
		}
	}
	return false
}

// 将数组转为string,格式如str1,str2,...
func Array2String(args ...string) (result string) {
	for _, arg := range args {
		result = arg + "," + result
	}
	if len(result) >= 1 {
		return result[:len(result)-1]
	}
	return result
}

// check本机ip与给定ip是否一致
func CheckIP(ip string) (result bool, err error) {
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		logs.Error("Get host ip fail ,error info is %s", err.Error())
		return false, err
	}
	for _, address := range addrs {
		if ipnet, ok := address.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipnet.IP.String() == ip {
				return true, nil
			}
		}
	}
	return false, nil
}

// cfs 挂载
func MountCfs(clusterParam ConsleParam) (err error) {
	for _, v := range clusterParam.SharedstorageList {
		if v.StorageProtocol != NFS {
			err = fmt.Errorf("invalid mount protocol:%v,should be nfs", v.StorageProtocol)
			logs.Error(err.Error())
			return
		}
		clusterNameID := clusterParam.ClusterName + "#" + clusterParam.ClusterID
		args := []string{ShellSciptName, "mountCfs", "-t", v.MountTarget, "--mountDir", v.MountDir,
			"--clusterNameId", clusterNameID}
		err = ExecCmd(args...)
		if err != nil {
			log.Error(err.Error())
			return
		}
	}
	return nil
}

// 日志配置初始化
func InitLog() (err error) {
	logConfig := make(map[string]interface{})
	logConfig["filename"] = ConsoleLogFile
	logConfig["level"] = 7
	logConfig["daily"] = true
	logConfig["maxdays"] = 30
	logConfig["separate"] = "error"
	logConfigByte, err := json.Marshal(logConfig)
	if err != nil {
		return err
	}
	err = logs.SetLogger(logs.AdapterFile, string(logConfigByte))
	if err != nil {
		return err
	}
	// logs.SetLogger(logs.AdapterConsole, `{"level":7}`)
	logs.EnableFuncCallDepth(true)
	logs.SetLogFuncCallDepth(3)
	return
}

func ValidateParms(clusterParam ConsleParam) (err error) {
	params := make([]string, 0)
	params = append(params, clusterParam.ClusterName)
	for _, v := range clusterParam.QueueList {
		params = append(params, v.Name)
	}
	pattern := "^[a-zA-Z][a-zA-Z0-9_]{0,13}[a-zA-Z0-9]$"
	reg, err := regexp.Compile(pattern)
	if err != nil {
		log.Error(err.Error())
		return
	}
	for _, v := range params {
		isValid := reg.MatchString(v)
		if !isValid {
			err = fmt.Errorf("invalid param %v", v)
			logs.Error(err.Error())
			return
		}
	}
	return
}
